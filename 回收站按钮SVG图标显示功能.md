# 回收站按钮SVG图标显示功能

## Core Features

- 清空回收站SVG图标

- 恢复选中提示词SVG图标

- 彻底删除选中提示词SVG图标

- 按钮图标样式优化

## Tech Stack

{
  "Web": null,
  "iOS": null,
  "Android": null,
  "Desktop": "PySide6 + SVG图标资源"
}

## Design

为回收站的三个核心操作按钮设计合适的SVG图标：1. 清空回收站使用垃圾桶清空图标 2. 恢复功能使用还原/撤销图标 3. 彻底删除使用删除/叉号图标。图标应该直观易懂，符合用户操作习惯，并与现有UI风格保持一致。

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 创建三个SVG图标文件并保存到icons目录

[X] 设计并实现SVG图标的样式和颜色方案

[X] 在回收站视图中集成SVG图标按钮

[X] 配置图标按钮的悬停和点击状态效果

[X] 测试图标按钮的显示效果和交互功能
