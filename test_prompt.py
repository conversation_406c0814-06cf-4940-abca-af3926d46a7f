import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import PromptModel

def test_get_prompt():
    model = PromptModel()
    
    # 测试获取ID为21的提示词
    prompt = model.get_prompt(21)
    
    if prompt:
        print(f"提示词ID: {prompt.id}")
        print(f"标题: {prompt.title}")
        print(f"内容: {prompt.content}")
        print(f"auxiliary_fields: {prompt.auxiliary_fields}")
        print(f"auxiliary_fields类型: {type(prompt.auxiliary_fields)}")
        
        if prompt.auxiliary_fields:
            print("辅助选择字段内容:")
            for field_name, field_value in prompt.auxiliary_fields.items():
                print(f"  {field_name}: {field_value}")
    else:
        print("未找到提示词")

if __name__ == "__main__":
    test_get_prompt() 