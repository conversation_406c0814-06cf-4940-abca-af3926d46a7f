"""
服务模块，包含业务逻辑和数据处理服务。
""" 

from .prompt_service import PromptService
from .category_service import CategoryService
from .tag_service import TagService
from .color_service import ColorService

# AI服务可选导入，避免依赖问题影响主程序启动
try:
    from .ai_service import AIService, ai_service
    AI_SERVICE_AVAILABLE = True
    __all__ = [
        'PromptService',
        'CategoryService', 
        'TagService',
        'ColorService',
        'AIService',
        'ai_service',
        'AI_SERVICE_AVAILABLE'
    ]
except ImportError as e:
    print(f"AI服务不可用: {e}")
    print("如需使用AI功能，请安装依赖: pip install aiohttp requests openai")
    AI_SERVICE_AVAILABLE = False
    AIService = None
    ai_service = None
    __all__ = [
        'PromptService',
        'CategoryService', 
        'TagService',
        'ColorService',
        'AI_SERVICE_AVAILABLE'
    ] 