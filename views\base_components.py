#!/usr/bin/env python3
"""
基础视图组件
提供所有视图组件的基类和通用功能
"""
from typing import Optional, Callable, Dict, Any, List, Tuple
import os

from PySide6.QtWidgets import (
    QWidget, QPushButton, QFrame, QLabel, QVBoxLayout, 
    QHBoxLayout, QGraphicsDropShadowEffect, QDialog
)
from PySide6.QtCore import Qt, QSize, QPoint, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QColor, QIcon, QPixmap, QPainter, QPalette, QBrush, QFont
from PySide6.QtSvg import QSvgRenderer

# 导入样式配置
try:
    from config.style_config import (
        COLORS, BUTTON_STYLES, CARD_STYLES, 
        INPUT_STYLES, TAG_STYLES, DIALOG_STYLES
    )
except ImportError:
    # 如果无法导入，使用空字典
    COLORS = {}
    BUTTON_STYLES = {}
    CARD_STYLES = {}
    INPUT_STYLES = {}
    TAG_STYLES = {}
    DIALOG_STYLES = {}

# 导入UI辅助函数
from utils.ui_helpers import (
    create_svg_icon, set_button_svg_icon, create_styled_button, 
    create_shadow_effect, create_horizontal_separator
)


class BaseComponent(QWidget):
    """所有自定义组件的基类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._setup_base_properties()
    
    def _setup_base_properties(self):
        """设置基础属性"""
        # 启用鼠标跟踪，使enterEvent和leaveEvent生效
        self.setMouseTracking(True)
        
        # 设置属性，可用于样式表
        self.setProperty("class", self.__class__.__name__)
    
    def apply_style(self, style: str):
        """应用样式表"""
        self.setStyleSheet(style)
    
    def create_shadow(self, color="#000000", blur_radius=20, x_offset=0, y_offset=0):
        """为组件添加阴影效果"""
        shadow = create_shadow_effect(color, blur_radius, x_offset, y_offset)
        self.setGraphicsEffect(shadow)
    
    def update_property(self, name: str, value):
        """更新组件属性并触发样式刷新"""
        self.setProperty(name, value)
        self.style().unpolish(self)
        self.style().polish(self)


class BasePushButton(QPushButton):
    """基础按钮类"""
    
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self._setup_base_properties()
        self._is_active = False
        self._hover = False
        self._pressed = False
    
    def _setup_base_properties(self):
        """设置基础属性"""
        self.setMouseTracking(True)
        self.setCursor(Qt.PointingHandCursor)
        self.setProperty("class", self.__class__.__name__)
    
    def apply_style(self, style: str):
        """应用样式表"""
        self.setStyleSheet(style)
    
    def set_active(self, active: bool):
        """设置激活状态"""
        if self._is_active != active:
            self._is_active = active
            self.update_style()
            # 更新属性，用于样式表
            self.setProperty("active", active)
            self.style().unpolish(self)
            self.style().polish(self)
    
    def is_active(self) -> bool:
        """获取激活状态"""
        return self._is_active
    
    def update_style(self):
        """更新样式（在子类中实现）"""
        pass
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self._hover = True
        self.update_style()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self._hover = False
        self.update_style()
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._pressed = True
            self.update_style()
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self._pressed = False
            self.update_style()
        super().mouseReleaseEvent(event)


class IconButton(BasePushButton):
    """图标按钮"""
    
    def __init__(self, svg_content=None, tooltip="", size=QSize(32, 32), parent=None):
        super().__init__("", parent)
        self.svg_content = svg_content
        self.default_color = COLORS.get("gray_600", "#4B5563")
        self.hover_color = COLORS.get("primary", "#0EA5E9")
        self.active_color = COLORS.get("primary_dark", "#0284C7")
        
        self.setFixedSize(size)
        self.setToolTip(tooltip)
        
        if svg_content:
            self.update_icon()
        
        self.apply_style(BUTTON_STYLES.get("icon_button", ""))
    
    def update_icon(self):
        """更新图标"""
        color = self.default_color
        if self._is_active:
            color = self.active_color
        elif self._hover:
            color = self.hover_color
        
        if self.svg_content:
            set_button_svg_icon(self, self.svg_content, color)
    
    def update_style(self):
        """更新样式"""
        self.update_icon()


class BaseCardFrame(QFrame):
    """基础卡片框架"""
    
    clicked = Signal()  # 点击信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._selected = False
        self._hover = False
        self._setup_base_properties()
    
    def _setup_base_properties(self):
        """设置基础属性"""
        self.setMouseTracking(True)
        self.setCursor(Qt.PointingHandCursor)
        self.setProperty("class", self.__class__.__name__)
        
        # 应用默认样式
        self.apply_style(CARD_STYLES.get("prompt_card", ""))
    
    def apply_style(self, style: str):
        """应用样式表"""
        self.setStyleSheet(style)
    
    def set_selected(self, selected: bool):
        """设置选中状态"""
        if self._selected != selected:
            self._selected = selected
            self.update_style()
            # 更新属性，用于样式表
            self.setProperty("selected", selected)
            self.style().unpolish(self)
            self.style().polish(self)
    
    def is_selected(self) -> bool:
        """获取选中状态"""
        return self._selected
    
    def update_style(self):
        """更新样式"""
        if self._selected:
            self.apply_style(CARD_STYLES.get("prompt_card_selected", ""))
        else:
            self.apply_style(CARD_STYLES.get("prompt_card", ""))
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self._hover = True
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self._hover = False
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.clicked.emit()
        super().mousePressEvent(event)


class TagLabel(QLabel):
    """标签标签控件"""
    
    clicked = Signal(str)  # 点击信号，传递标签文本
    
    def __init__(self, text="", clickable=True, parent=None):
        super().__init__(text, parent)
        self.clickable = clickable
        self._hover = False
        self._setup_base_properties()
    
    def _setup_base_properties(self):
        """设置基础属性"""
        self.setMouseTracking(True)
        if self.clickable:
            self.setCursor(Qt.PointingHandCursor)
        
        # 应用样式
        style = TAG_STYLES.get("clickable_tag" if self.clickable else "tag", "")
        self.apply_style(style)
    
    def apply_style(self, style: str):
        """应用样式表"""
        self.setStyleSheet(style)
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self._hover = True
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self._hover = False
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.clickable and event.button() == Qt.LeftButton:
            self.clicked.emit(self.text())
        super().mousePressEvent(event)


class BaseDialog(QDialog):
    """基础对话框"""
    
    def __init__(self, parent=None, title="对话框", width=400, height=300):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(width, height)
        self._drag_position = None
        self._setup_ui(title)
    
    def _setup_ui(self, title):
        """设置UI"""
        # 应用基本对话框样式
        dialog_style = """
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """
        self.setStyleSheet(dialog_style)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题栏
        self.title_bar = QWidget()
        self.title_bar.setFixedHeight(40)
        self.title_bar.setStyleSheet("""
            QWidget {
                background-color: white;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        self.title_bar.setMouseTracking(True)
        
        title_layout = QHBoxLayout(self.title_bar)
        title_layout.setContentsMargins(15, 0, 15, 0)
        
        self.title_label = QLabel(title)
        self.title_label.setStyleSheet("font-size: 14px; font-weight: 600;")
        
        close_button = QPushButton("×")
        close_button.setFixedSize(24, 24)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                font-size: 16px;
                color: #6B7280;
            }
            QPushButton:hover {
                color: #EF4444;
            }
        """)
        close_button.clicked.connect(self.reject)
        
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        title_layout.addWidget(close_button)
        
        # 内容区域
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        
        main_layout.addWidget(self.title_bar)
        main_layout.addWidget(self.content_widget)
        
        # 添加阴影
        self.create_shadow()
        
        # 连接事件
        self.title_bar.mousePressEvent = self.title_bar_mouse_press
        self.title_bar.mouseMoveEvent = self.title_bar_mouse_move
    
    def apply_style(self, style: str):
        """应用样式表"""
        self.setStyleSheet(style)
    
    def create_shadow(self):
        """创建阴影效果"""
        try:
            from PySide6.QtWidgets import QGraphicsDropShadowEffect
            from PySide6.QtGui import QColor
            
            shadow = QGraphicsDropShadowEffect()
            shadow.setColor(QColor(0, 0, 0, 80))  
            shadow.setBlurRadius(20)
            shadow.setOffset(0, 5)
            self.setGraphicsEffect(shadow)
        except Exception:
            # 如果阴影创建失败，就不设置阴影
            pass
    
    def title_bar_mouse_press(self, event):
        """标题栏鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self._drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
    
    def title_bar_mouse_move(self, event):
        """标题栏鼠标移动事件"""
        if event.buttons() == Qt.LeftButton and self._drag_position:
            self.move(event.globalPosition().toPoint() - self._drag_position)
    
    def set_content_layout(self, layout):
        """设置内容区域布局"""
        # 清除现有布局
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 添加新布局
        self.content_layout.addLayout(layout)
    
    def showEvent(self, event):
        """显示事件，居中显示对话框"""
        super().showEvent(event)
        
        # 相对于父窗口居中
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            self.move(x, y) 
    
    @staticmethod
    def create_dialog_with_type(dialog_type: str, parent=None, **kwargs) -> 'BaseDialog':
        """
        创建指定类型的对话框 - 对话框工厂方法
        
        Args:
            dialog_type: 对话框类型
            parent: 父窗口
            **kwargs: 其他参数
            
        Returns:
            BaseDialog实例
        """
        if dialog_type == "confirm":
            dialog = BaseDialog(parent, kwargs.get("title", "确认"), 
                              kwargs.get("width", 350), kwargs.get("height", 200))
        elif dialog_type == "info":
            dialog = BaseDialog(parent, kwargs.get("title", "信息"), 
                              kwargs.get("width", 400), kwargs.get("height", 250))
        elif dialog_type == "warning":
            dialog = BaseDialog(parent, kwargs.get("title", "警告"), 
                              kwargs.get("width", 400), kwargs.get("height", 250))
        elif dialog_type == "error":
            dialog = BaseDialog(parent, kwargs.get("title", "错误"), 
                              kwargs.get("width", 400), kwargs.get("height", 250))
        else:
            # 默认对话框
            dialog = BaseDialog(parent, kwargs.get("title", "对话框"), 
                              kwargs.get("width", 400), kwargs.get("height", 300))
        
        # 应用对话框样式
        dialog_style = BaseDialog.get_dialog_style(dialog_type)
        dialog.apply_style(dialog_style)
        
        return dialog
    
    @staticmethod
    def get_dialog_style(dialog_type: str) -> str:
        """
        获取对话框样式
        
        Args:
            dialog_type: 对话框类型
            
        Returns:
            样式字符串
        """
        base_style = """
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """
        
        if dialog_type == "warning":
            return base_style + """
                QDialog {
                    border-color: #F59E0B;
                }
            """
        elif dialog_type == "error":
            return base_style + """
                QDialog {
                    border-color: #EF4444;
                }
            """
        elif dialog_type == "info":
            return base_style + """
                QDialog {
                    border-color: #3B82F6;
                }
            """
        else:
            return base_style 