#!/usr/bin/env python3
"""
测试标签筛选功能
"""
import sys
from PySide6.QtWidgets import QApplication
from main import PromptAssistantRedesigned
from model import Prompt, SQLiteStorage

def create_test_data_with_tags():
    """创建包含标签的测试数据"""
    storage = SQLiteStorage()
    
    # 清除现有数据
    storage.cursor.execute("DELETE FROM prompts")
    storage.conn.commit()
    
    # 创建包含不同标签的测试提示词
    test_prompts = [
        Prompt(
            title="AI绘画风景提示词",
            content="创建一幅美丽的山水风景画，包含青山绿水，云雾缭绕",
            category="AI绘画",
            tags=["风景", "山水", "自然"],
            is_favorite=1
        ),
        Prompt(
            title="人物肖像绘画",
            content="绘制一个优雅的女性肖像，具有古典美感",
            category="AI绘画", 
            tags=["人物", "肖像", "古典"],
            is_favorite=0
        ),
        Prompt(
            title="商业文案写作",
            content="为新产品撰写吸引人的营销文案",
            category="文案写作",
            tags=["商业", "营销", "产品"],
            is_favorite=1
        ),
        Prompt(
            title="社交媒体文案",
            content="创作适合社交平台的短文案",
            category="文案写作",
            tags=["社交媒体", "短文案", "营销"],
            is_favorite=0
        ),
        Prompt(
            title="Python爬虫代码",
            content="编写一个网页数据爬取脚本",
            category="代码生成",
            tags=["Python", "爬虫", "数据"],
            is_favorite=1
        ),
        Prompt(
            title="React组件开发",
            content="创建一个可复用的React UI组件",
            category="代码生成",
            tags=["React", "组件", "前端"],
            is_favorite=0
        ),
        Prompt(
            title="数据分析报告",
            content="分析销售数据并生成可视化报告",
            category="数据分析",
            tags=["数据", "分析", "可视化"],
            is_favorite=1
        ),
        Prompt(
            title="创意Logo设计",
            content="设计一个现代简约风格的企业Logo",
            category="创意设计",
            tags=["Logo", "设计", "简约", "企业"],
            is_favorite=0
        )
    ]
    
    # 保存到数据库
    for prompt in test_prompts:
        storage.add_prompt(prompt)
    
    print("标签筛选测试数据创建完成！")
    print(f"总共创建了 {len(test_prompts)} 个提示词")
    
    # 统计标签
    all_tags = {}
    for prompt in test_prompts:
        for tag in prompt.tags:
            all_tags[tag] = all_tags.get(tag, 0) + 1
    
    print("\n标签统计:")
    for tag, count in sorted(all_tags.items()):
        print(f"  {tag}: {count} 个")
    
    print(f"\n收藏的提示词: {sum(1 for p in test_prompts if p.is_favorite)} 个")
    
    print("\n=== 标签筛选功能测试说明 ===")
    print("1. 应用启动后会显示所有8个提示词")
    print("2. 点击工具栏中的标签筛选按钮（标签图标）")
    print("3. 标签筛选栏会显示/隐藏，包含以下标签按钮：")
    print("   - 全部 (默认选中)")
    for tag in sorted(all_tags.keys()):
        print(f"   - {tag} ({all_tags[tag]}个)")
    print("4. 点击任意标签按钮可以筛选包含该标签的提示词")
    print("5. 可以同时使用收藏筛选和标签筛选")
    print("6. 标签筛选和分类筛选是互斥的（只能同时显示一个）")
    print("7. 状态栏会显示当前筛选状态和数量信息")

if __name__ == '__main__':
    # 创建测试数据
    create_test_data_with_tags()
    
    # 启动应用
    app = QApplication(sys.argv)
    window = PromptAssistantRedesigned()
    window.show()
    sys.exit(app.exec())