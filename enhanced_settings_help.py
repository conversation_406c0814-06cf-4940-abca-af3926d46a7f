#!/usr/bin/env python3
"""
增强的设置和帮助页面模块
为Prompt Assistant添加完整的设置和帮助功能
"""

import sys
import os
from pathlib import Path
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QPushButton, 
                               QLabel, QFrame, QScrollArea, QComboBox, QSlider,
                               QCheckBox, QSpinBox, QLineEdit, QTextEdit, QTabWidget,
                               QGroupBox, QFileDialog, QMessageBox, QProgressBar,
                               QSizePolicy, QSpacerItem, QListWidget, QStackedWidget,
                               QListWidgetItem, QMenu, QDialog)
from PySide6.QtCore import Qt, QSettings, Signal, QThread, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QPixmap, QPainter, QColor, QIcon
from datetime import datetime



class SettingsPage(QWidget):
    """增强的设置页面"""
    
    # 设置变更信号
    settingsChanged = Signal(str, object)  # 设置名称, 新值
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.settings = QSettings("PromptAssistant", "AppSettings")
        
        # 定义内部方法（避免方法定义顺序问题）
        self._test_connection_wrapper = self._create_test_connection_wrapper()
        self._save_config_wrapper = self._create_save_config_wrapper()
        
        self.init_ui()
        self.load_settings()
    
    def _create_test_connection_wrapper(self):
        """创建连接测试包装方法"""
        def wrapper(platform_id):
            if hasattr(self, 'test_api_connection'):
                self.test_api_connection(platform_id)
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "测试功能暂不可用")
        return wrapper
    
    def _create_save_config_wrapper(self):
        """创建保存配置包装方法"""
        def wrapper():
            if hasattr(self, 'save_ai_config'):
                self.save_ai_config()
            else:
                from PySide6.QtWidgets import QMessageBox
                QMessageBox.warning(self, "错误", "保存功能暂不可用")
        return wrapper
        
    def init_ui(self):
        """初始化UI"""
        # 主滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
        """)
        
        # 滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(15, 15, 15, 15)
        scroll_layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("设置")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 10px;
            }
        """)
        scroll_layout.addWidget(title_label)
        
        # 数据管理组
        data_group = self.create_data_group()
        scroll_layout.addWidget(data_group)
        
        # AI配置组
        ai_group = self.create_ai_group()
        scroll_layout.addWidget(ai_group)
        
        # 高级设置组
        advanced_group = self.create_advanced_group()
        scroll_layout.addWidget(advanced_group)
        
        # 关于信息组
        about_group = self.create_about_group()
        scroll_layout.addWidget(about_group)
        
        scroll_layout.addStretch()
        scroll_area.setWidget(scroll_content)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll_area)
        
    def create_group_frame(self, title):
        """创建设置组框架"""
        group = QGroupBox(title)
        group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        return group
        

        

        
    def create_data_group(self):
        """创建数据管理组"""
        group = self.create_group_frame("数据管理")
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 数据库路径
        db_layout = QVBoxLayout()
        db_label = QLabel("数据库位置:")
        db_label.setStyleSheet("color: #4B5563; font-size: 13px; font-weight: normal;")
        
        db_path_layout = QHBoxLayout()
        self.db_path_edit = QLineEdit()
        self.db_path_edit.setReadOnly(True)
        self.db_path_edit.setStyleSheet("""
            QLineEdit {
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 6px 8px;
                background-color: #F9FAFB;
                font-size: 12px;
                color: #6B7280;
            }
        """)
        
        browse_db_btn = QPushButton("浏览")
        browse_db_btn.setFixedWidth(60)
        browse_db_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        browse_db_btn.clicked.connect(self.browse_database_location)
        
        db_path_layout.addWidget(self.db_path_edit)
        db_path_layout.addWidget(browse_db_btn)
        
        db_layout.addWidget(db_label)
        db_layout.addLayout(db_path_layout)
        layout.addLayout(db_layout)
        
        # 数据操作按钮
        data_buttons_layout = QHBoxLayout()
        
        backup_btn = QPushButton("备份数据")
        backup_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        backup_btn.clicked.connect(self.backup_data)
        
        restore_btn = QPushButton("恢复数据")
        restore_btn.setStyleSheet("""
            QPushButton {
                background-color: #F59E0B;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #D97706;
            }
        """)
        restore_btn.clicked.connect(self.restore_data)
        
        clear_cache_btn = QPushButton("清理缓存")
        clear_cache_btn.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        clear_cache_btn.clicked.connect(self.clear_cache)
        
        data_buttons_layout.addWidget(backup_btn)
        data_buttons_layout.addWidget(restore_btn)
        data_buttons_layout.addWidget(clear_cache_btn)
        data_buttons_layout.addStretch()
        layout.addLayout(data_buttons_layout)
        
        # 存储统计
        stats_layout = QVBoxLayout()
        stats_label = QLabel("存储统计:")
        stats_label.setStyleSheet("color: #4B5563; font-size: 13px; font-weight: normal;")
        
        self.stats_text = QLabel()
        self.stats_text.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                padding: 8px;
                margin-top: 5px;
            }
        """)
        self.stats_text.setWordWrap(True)
        
        stats_layout.addWidget(stats_label)
        stats_layout.addWidget(self.stats_text)
        layout.addLayout(stats_layout)
        
        return group
        
    def create_ai_group(self):
        """创建简化的AI配置组"""
        group = self.create_group_frame("AI配置")
        layout = QVBoxLayout(group)
        layout.setSpacing(12)
        
        # 检查AI服务可用性
        try:
            from services import AI_SERVICE_AVAILABLE
            if not AI_SERVICE_AVAILABLE:
                # AI服务不可用时显示提示
                warning_label = QLabel("⚠️ AI服务不可用，请安装依赖包：pip install aiohttp requests openai")
                warning_label.setStyleSheet("""
                    QLabel {
                        color: #DC2626;
                        font-size: 12px;
                        background-color: #FEF2F2;
                        border: 1px solid #FECACA;
                        border-radius: 4px;
                        padding: 8px;
                    }
                """)
                layout.addWidget(warning_label)
                return group
        except ImportError:
            # 导入失败时显示提示
            warning_label = QLabel("⚠️ AI服务模块导入失败，请检查安装")
            warning_label.setStyleSheet("""
                QLabel {
                    color: #DC2626;
                    font-size: 12px;
                    background-color: #FEF2F2;
                    border: 1px solid #FECACA;
                    border-radius: 4px;
                    padding: 8px;
                }
            """)
            layout.addWidget(warning_label)
            return group
        
        # 已配置平台摘要区域  
        self.create_configured_platforms_summary(layout)
        
        # 操作按钮区域  
        self.create_ai_action_buttons(layout)
        
        # 全局AI参数配置
        params_label = QLabel("AI参数配置:")
        params_label.setStyleSheet("color: #4B5563; font-size: 13px; font-weight: 600; margin-top: 15px;")
        layout.addWidget(params_label)
        
        # Temperature设置
        temp_layout = QHBoxLayout()
        temp_label = QLabel("Temperature:")
        temp_label.setFixedWidth(100)
        temp_label.setStyleSheet("color: #374151; font-size: 12px;")
        
        # Temperature帮助图标
        temp_help_icon = QLabel("❓")
        temp_help_icon.setFixedSize(16, 16)
        temp_help_icon.setAlignment(Qt.AlignCenter)
        temp_help_icon.setStyleSheet("""
            QLabel {
                color: #6B7280;
                background-color: #F3F4F6;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QLabel:hover {
                color: #3B82F6;
                background-color: #EBF4FF;
                border-color: #3B82F6;
            }
        """)
        temp_help_icon.setToolTip("""<b>Temperature (创造性控制)</b><br/>
控制AI回复的随机性和创造性：<br/>
• <b>0.0-0.3</b>: 非常确定，适合事实性任务<br/>
• <b>0.4-0.7</b>: 平衡创造性和准确性<br/>
• <b>0.8-1.0</b>: 高创造性，适合创意写作<br/>
<i>推荐值: 0.7</i>""")
        
        self.temperature_slider = QSlider(Qt.Horizontal)
        self.temperature_slider.setRange(0, 100)
        self.temperature_slider.setValue(70)  # 默认0.7
        self.temperature_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #D1D5DB;
                height: 4px;
                background: #F3F4F6;
                border-radius: 2px;
            }
            QSlider::handle:horizontal {
                background: #3B82F6;
                border: 1px solid #2563EB;
                width: 16px;
                margin: -6px 0;
                border-radius: 8px;
            }
            QSlider::sub-page:horizontal {
                background: #3B82F6;
                border-radius: 2px;
            }
        """)
        
        self.temperature_value_label = QLabel("0.7")
        self.temperature_value_label.setFixedWidth(30)
        self.temperature_value_label.setStyleSheet("color: #374151; font-size: 12px;")
        self.temperature_slider.valueChanged.connect(lambda value: self.temperature_value_label.setText(f"{value/100.0:.1f}"))
        
        temp_layout.addWidget(temp_label)
        temp_layout.addWidget(temp_help_icon)
        temp_layout.addSpacing(5)
        temp_layout.addWidget(self.temperature_slider)
        temp_layout.addWidget(self.temperature_value_label)
        layout.addLayout(temp_layout)
        
        # Max Tokens设置
        tokens_layout = QHBoxLayout()
        tokens_label = QLabel("Max Tokens:")
        tokens_label.setFixedWidth(100)
        tokens_label.setStyleSheet("color: #374151; font-size: 12px;")
        
        # Max Tokens帮助图标
        tokens_help_icon = QLabel("❓")
        tokens_help_icon.setFixedSize(16, 16)
        tokens_help_icon.setAlignment(Qt.AlignCenter)
        tokens_help_icon.setStyleSheet("""
            QLabel {
                color: #6B7280;
                background-color: #F3F4F6;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QLabel:hover {
                color: #3B82F6;
                background-color: #EBF4FF;
                border-color: #3B82F6;
            }
        """)
        tokens_help_icon.setToolTip("""<b>Max Tokens (最大生成长度)</b><br/>
限制AI单次生成的最大字符数：<br/>
• <b>100-500</b>: 简短回复，适合问答<br/>
• <b>500-1500</b>: 中等长度，适合解释说明<br/>
• <b>1500-4096</b>: 长文本，适合创作和详细分析<br/>
<i>注意: 更高的值会消耗更多API费用</i>""")
        
        self.max_tokens_spinbox = QSpinBox()
        self.max_tokens_spinbox.setRange(100, 4096)
        self.max_tokens_spinbox.setValue(1000)
        self.max_tokens_spinbox.setStyleSheet("""
            QSpinBox {
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: white;
                font-size: 12px;
                color: #374151;
            }
            QSpinBox:focus {
                border-color: #3B82F6;
            }
        """)
        
        tokens_layout.addWidget(tokens_label)
        tokens_layout.addWidget(tokens_help_icon)
        tokens_layout.addSpacing(5)
        tokens_layout.addWidget(self.max_tokens_spinbox)
        tokens_layout.addStretch()
        layout.addLayout(tokens_layout)
        
        # 保存配置按钮
        save_layout = QHBoxLayout()
        save_btn = QPushButton("保存AI配置")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        # 使用包装方法
        save_btn.clicked.connect(self._save_config_wrapper)
        
        save_layout.addWidget(save_btn)
        save_layout.addStretch()
        layout.addLayout(save_layout)
        
        # 延迟加载配置，确保所有方法都已定义
        if hasattr(self, 'load_ai_config'):
            self.load_ai_config()
        
        return group
    
    def create_configured_platforms_summary(self, layout):
        """创建已配置平台摘要显示"""
        summary_label = QLabel("已配置平台:")
        summary_label.setStyleSheet("color: #4B5563; font-size: 13px; font-weight: 600;")
        layout.addWidget(summary_label)
        
        # 平台状态卡片容器
        self.platforms_container = QWidget()
        self.platforms_layout = QVBoxLayout(self.platforms_container)
        self.platforms_layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.platforms_container)
        
        # 加载并显示平台摘要
        self.refresh_platforms_summary()
    
    def create_ai_action_buttons(self, layout):
        """创建操作按钮区域"""
        buttons_layout = QHBoxLayout()
        
        # 添加平台按钮
        add_platform_btn = QPushButton("+ 添加AI平台")
        add_platform_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        add_platform_btn.clicked.connect(self.open_platform_dialog)
        
        # 管理配置按钮
        manage_btn = QPushButton("⚙️ 管理配置")
        manage_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        manage_btn.clicked.connect(lambda: self.open_platform_dialog(management_mode=True))
        
        buttons_layout.addWidget(add_platform_btn)
        buttons_layout.addWidget(manage_btn)
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def open_platform_dialog(self, management_mode=False):
        """打开平台配置对话框"""
        try:
            from views.dialogs import AIPlatformDialog
            
            dialog = AIPlatformDialog(self.parent_window, management_mode)
            dialog.platform_configured.connect(self.on_platform_configured)
            dialog.platform_removed.connect(self.on_platform_removed)
            
            if dialog.exec() == QDialog.Accepted:
                self.refresh_platforms_summary()
        except ImportError as e:
            QMessageBox.warning(self, "模块导入错误", f"无法导入AI平台对话框: {e}")
    
    def on_platform_configured(self, platform_id: str):
        """处理平台配置完成"""
        self.refresh_platforms_summary()
        
        # 显示成功提示
        try:
            from config.ai_platforms import PLATFORM_UI_CONFIG
            platform_name = PLATFORM_UI_CONFIG.get(platform_id, {}).get('display_name', platform_id)
            QMessageBox.information(
                self, 
                "配置成功", 
                f"{platform_name} 配置完成！"
            )
        except ImportError:
            QMessageBox.information(self, "配置成功", "AI平台配置完成！")
    
    def on_platform_removed(self, platform_id: str):
        """处理平台移除"""
        self.refresh_platforms_summary()
    
    def refresh_platforms_summary(self):
        """刷新平台摘要显示"""
        # 清空现有摘要
        self.clear_layout(self.platforms_layout)
        
        try:
            from services.ai_config_manager import ai_config_manager
            configured_platforms = ai_config_manager.get_configured_platforms()
            
            if not configured_platforms:
                # 显示空状态
                empty_label = QLabel("暂未配置AI平台，点击上方按钮开始配置")
                empty_label.setStyleSheet("color: #6B7280; font-size: 12px; padding: 20px; text-align: center;")
                self.platforms_layout.addWidget(empty_label)
            else:
                # 显示配置摘要
                for platform_data in configured_platforms:
                    summary_card = self.create_platform_summary_card(platform_data)
                    self.platforms_layout.addWidget(summary_card)
        except ImportError:
            # AI配置管理器不可用
            error_label = QLabel("AI配置管理器不可用")
            error_label.setStyleSheet("color: #DC2626; font-size: 12px; padding: 20px;")
            self.platforms_layout.addWidget(error_label)
    
    def create_platform_summary_card(self, platform_data):
        """创建平台摘要卡片"""
        card = QWidget()
        card.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                padding: 8px;
                margin: 2px 0px;
            }
        """)
        
        layout = QHBoxLayout(card)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # 状态图标
        status = platform_data['config'].get('status', 'unconfigured')
        if status == 'connected':
            status_icon = "✅"
        elif status == 'error':
            status_icon = "⚠️"
        else:
            status_icon = "⏳"
        
        status_label = QLabel(status_icon)
        layout.addWidget(status_label)
        
        # 平台信息
        info_layout = QVBoxLayout()
        info_layout.setSpacing(2)
        
        name_label = QLabel(platform_data['ui_config'].get('display_name', platform_data['platform_id']))
        name_label.setStyleSheet("font-weight: 600; font-size: 13px; color: #1F2937;")
        
        detail_text = f"模型: {platform_data['config'].get('default_model', 'N/A')}"
        detail_label = QLabel(detail_text)
        detail_label.setStyleSheet("font-size: 11px; color: #6B7280;")
        
        info_layout.addWidget(name_label)
        info_layout.addWidget(detail_label)
        layout.addLayout(info_layout)
        
        layout.addStretch()
        
        # 操作按钮
        edit_btn = QPushButton("编辑")
        edit_btn.setFixedSize(40, 24)
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #E5E7EB;
                border: none;
                border-radius: 4px;
                font-size: 11px;
                color: #374151;
            }
            QPushButton:hover {
                background-color: #D1D5DB;
            }
        """)
        edit_btn.clicked.connect(
            lambda: self.edit_platform_config(platform_data['platform_id'])
        )
        layout.addWidget(edit_btn)
        
        return card
    
    def edit_platform_config(self, platform_id: str):
        """编辑平台配置"""
        # 这里可以打开编辑模式的对话框
        self.open_platform_dialog(management_mode=True)
    
    def clear_layout(self, layout):
        """清空布局中的所有组件"""
        while layout.count():
            child = layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
    def create_advanced_group(self):
        """创建高级设置组"""
        group = self.create_group_frame("高级设置")
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 调试模式
        self.debug_mode_checkbox = QCheckBox("启用调试模式")
        self.debug_mode_checkbox.setStyleSheet("""
            QCheckBox {
                color: #374151;
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
                border: 1px solid #D1D5DB;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                background-color: #3B82F6;
                border-color: #3B82F6;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTkgMTJMMTEgMTRMMTUgMTAiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
            QCheckBox::indicator:hover {
                border-color: #9CA3AF;
            }
        """)
        self.debug_mode_checkbox.toggled.connect(self.on_debug_mode_changed)
        layout.addWidget(self.debug_mode_checkbox)
        
        # 性能优化
        self.performance_mode_checkbox = QCheckBox("性能优化模式（减少动画效果）")
        self.performance_mode_checkbox.setStyleSheet(self.debug_mode_checkbox.styleSheet())
        self.performance_mode_checkbox.toggled.connect(self.on_performance_mode_changed)
        layout.addWidget(self.performance_mode_checkbox)
        
        # 重置设置按钮
        reset_layout = QHBoxLayout()
        reset_btn = QPushButton("重置所有设置")
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        reset_btn.clicked.connect(self.reset_all_settings)
        
        reset_layout.addWidget(reset_btn)
        reset_layout.addStretch()
        layout.addLayout(reset_layout)
        
        return group
        
    def create_about_group(self):
        """创建关于信息组"""
        group = self.create_group_frame("关于应用")
        layout = QVBoxLayout(group)
        layout.setSpacing(15)
        
        # 应用信息
        app_info_layout = QHBoxLayout()
        
        # 应用图标（如果有的话）
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        icon_label.setStyleSheet("""
            QLabel {
                background-color: #3B82F6;
                border-radius: 8px;
                color: white;
                font-size: 20px;
                font-weight: bold;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setText("PA")
        
        info_layout = QVBoxLayout()
        app_name_label = QLabel("Prompt收藏助手")
        app_name_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        
        version_label = QLabel("版本 1.0.0")
        version_label.setStyleSheet("color: #6B7280; font-size: 13px;")
        
        description_label = QLabel("一个优雅的提示词管理工具")
        description_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        
        info_layout.addWidget(app_name_label)
        info_layout.addWidget(version_label)
        info_layout.addWidget(description_label)
        info_layout.addStretch()
        
        app_info_layout.addWidget(icon_label)
        app_info_layout.addLayout(info_layout)
        app_info_layout.addStretch()
        layout.addLayout(app_info_layout)
        
        # 系统信息
        system_info = QLabel()
        system_info.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 11px;
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        
        import platform
        system_text = f"""系统信息:
操作系统: {platform.system()} {platform.release()}
Python版本: {platform.python_version()}
PySide6版本: {self.get_pyside_version()}"""
        
        system_info.setText(system_text)
        layout.addWidget(system_info)
        
        return group
        
    def get_pyside_version(self):
        """获取PySide6版本"""
        try:
            import PySide6
            return PySide6.__version__
        except:
            return "未知"
            
    def load_settings(self):
        """加载设置"""
            
        # 加载高级设置
        self.debug_mode_checkbox.setChecked(
            self.settings.value("debug_mode", False, type=bool)
        )
        self.performance_mode_checkbox.setChecked(
            self.settings.value("performance_mode", False, type=bool)
        )
        
        # 加载数据库路径
        db_path = self.settings.value("database_path", "prompts.db")
        self.db_path_edit.setText(str(Path(db_path).absolute()))
        
        # 更新存储统计
        self.update_storage_stats()
        
    def update_storage_stats(self):
        """更新存储统计信息"""
        try:
            if self.parent_window and hasattr(self.parent_window, 'model'):
                # 获取提示词数量
                prompts = self.parent_window.model.get_all_prompts(include_deleted=True)
                total_prompts = len(prompts)
                active_prompts = len([p for p in prompts if not hasattr(p, 'is_deleted') or p.is_deleted == 0])
                deleted_prompts = total_prompts - active_prompts
                
                # 获取数据库文件大小
                db_path = Path(self.settings.value("database_path", "prompts.db"))
                if db_path.exists():
                    db_size = db_path.stat().st_size
                    db_size_mb = db_size / (1024 * 1024)
                else:
                    db_size_mb = 0
                
                stats_text = f"""提示词总数: {total_prompts} 个
活跃提示词: {active_prompts} 个
回收站: {deleted_prompts} 个
数据库大小: {db_size_mb:.2f} MB"""
                
                self.stats_text.setText(stats_text)
            else:
                self.stats_text.setText("无法获取统计信息")
        except Exception as e:
            self.stats_text.setText(f"统计信息获取失败: {str(e)}")
    
    # 设置变更处理方法
        

        
    def on_debug_mode_changed(self, checked):
        """调试模式设置变更"""
        self.settings.setValue("debug_mode", checked)
        self.settingsChanged.emit("debug_mode", checked)
        
    def on_performance_mode_changed(self, checked):
        """性能模式设置变更"""
        self.settings.setValue("performance_mode", checked)
        self.settingsChanged.emit("performance_mode", checked)
    
    # 数据管理方法
    def browse_database_location(self):
        """浏览数据库位置"""
        current_path = self.db_path_edit.text()
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "选择数据库位置", 
            current_path,
            "SQLite数据库 (*.db);;所有文件 (*)"
        )
        
        if file_path:
            self.db_path_edit.setText(file_path)
            self.settings.setValue("database_path", file_path)
            self.settingsChanged.emit("database_path", file_path)
            
    def backup_data(self):
        """备份数据"""
        try:
            backup_path, _ = QFileDialog.getSaveFileName(
                self,
                "选择备份位置",
                f"prompt_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db",
                "SQLite数据库 (*.db);;所有文件 (*)"
            )
            
            if backup_path:
                import shutil
                current_db = self.settings.value("database_path", "prompts.db")
                shutil.copy2(current_db, backup_path)
                QMessageBox.information(self, "备份成功", f"数据已备份到: {backup_path}")
        except Exception as e:
            QMessageBox.critical(self, "备份失败", f"备份过程中出错: {str(e)}")
            
    def restore_data(self):
        """恢复数据"""
        reply = QMessageBox.warning(
            self,
            "确认恢复",
            "恢复数据将覆盖当前所有数据，此操作不可撤销！\n确定要继续吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                backup_path, _ = QFileDialog.getOpenFileName(
                    self,
                    "选择备份文件",
                    "",
                    "SQLite数据库 (*.db);;所有文件 (*)"
                )
                
                if backup_path:
                    import shutil
                    current_db = self.settings.value("database_path", "prompts.db")
                    shutil.copy2(backup_path, current_db)
                    QMessageBox.information(self, "恢复成功", "数据恢复完成，请重启应用以生效。")
            except Exception as e:
                QMessageBox.critical(self, "恢复失败", f"恢复过程中出错: {str(e)}")
                
    def clear_cache(self):
        """清理缓存"""
        try:
            import os
            import shutil
            import glob
            from pathlib import Path
            
            # 确认对话框
            reply = QMessageBox.question(
                self,
                "确认清理缓存",
                "将清理以下类型的缓存文件：\n\n"
                "• 缓存\n"
                "• 应用日志文件\n"
                "• 临时导出文件\n"
                "• 其他临时文件\n\n"
                "确定要继续吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
                
            cleaned_files = []
            total_size = 0
            
            # 获取项目根目录
            base_dir = Path.cwd()
            
            # 1. 清理Python字节码缓存
            pycache_dirs = []
            for root, dirs, files in os.walk(base_dir):
                for dir_name in dirs:
                    if dir_name == "__pycache__":
                        pycache_dirs.append(Path(root) / dir_name)
            
            for pycache_dir in pycache_dirs:
                if pycache_dir.exists():
                    # 计算目录大小
                    dir_size = sum(f.stat().st_size for f in pycache_dir.rglob('*') if f.is_file())
                    total_size += dir_size
                    shutil.rmtree(pycache_dir)
                    cleaned_files.append(f"Python缓存目录: {pycache_dir.relative_to(base_dir)}")
            
            # 2. 清理应用日志文件
            log_file = base_dir / "app.log"
            if log_file.exists() and log_file.stat().st_size > 0:
                file_size = log_file.stat().st_size
                total_size += file_size
                log_file.unlink()
                log_file.touch()  # 重新创建空日志文件
                cleaned_files.append(f"日志文件: app.log ({file_size} 字节)")
            
            # 3. 清理临时导出文件
            export_files = [
                "prompts_export.json",
            ]
            for export_file in export_files:
                file_path = base_dir / export_file
                if file_path.exists():
                    file_size = file_path.stat().st_size
                    total_size += file_size
                    file_path.unlink()
                    cleaned_files.append(f"导出文件: {export_file} ({file_size} 字节)")
            
            # 4. 清理.cursor目录中的临时文件（如果存在）
            cursor_dir = base_dir / ".cursor"
            if cursor_dir.exists():
                temp_files = list(cursor_dir.glob("*.tmp")) + list(cursor_dir.glob("*.temp"))
                for temp_file in temp_files:
                    if temp_file.is_file():
                        file_size = temp_file.stat().st_size
                        total_size += file_size
                        temp_file.unlink()
                        cleaned_files.append(f"临时文件: {temp_file.relative_to(base_dir)} ({file_size} 字节)")
            
            # 5. 清理其他可能的临时文件
            temp_patterns = ["*.tmp", "*.temp", "*.bak~", "*.swp"]
            for pattern in temp_patterns:
                for temp_file in base_dir.glob(pattern):
                    if temp_file.is_file():
                        file_size = temp_file.stat().st_size
                        total_size += file_size
                        temp_file.unlink()
                        cleaned_files.append(f"临时文件: {temp_file.name} ({file_size} 字节)")
            
            # 显示清理结果
            if cleaned_files:
                total_size_mb = total_size / (1024 * 1024)
                message = f"缓存清理完成！\n\n"
                message += f"清理了 {len(cleaned_files)} 个文件/目录\n"
                message += f"释放空间: {total_size_mb:.2f} MB\n\n"
                message += "清理详情:\n"
                for item in cleaned_files[:10]:  # 最多显示10项
                    message += f"• {item}\n"
                if len(cleaned_files) > 10:
                    message += f"• ... 以及其他 {len(cleaned_files) - 10} 项"
                
                QMessageBox.information(self, "清理完成", message)
            else:
                QMessageBox.information(self, "清理完成", "没有找到需要清理的缓存文件。")
            
            # 更新存储统计
            self.update_storage_stats()
            
        except PermissionError as e:
            QMessageBox.critical(self, "权限错误", f"权限不足，无法删除某些文件:\n{str(e)}")
        except Exception as e:
            QMessageBox.critical(self, "清理失败", f"清理过程中出错:\n{str(e)}")
            
    def reset_all_settings(self):
        """重置所有设置"""
        reply = QMessageBox.warning(
            self,
            "确认重置",
            "这将重置所有设置到默认值，此操作不可撤销！\n确定要继续吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                self.settings.clear()
                self.load_settings()
                QMessageBox.information(self, "重置完成", "所有设置已重置为默认值。")
            except Exception as e:
                QMessageBox.critical(self, "重置失败", f"重置过程中出错: {str(e)}")


class HelpPage(QWidget):
    """增强的帮助页面 - 现代简约风格，上下布局结构"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_section = 0
        self.tab_buttons = []
        self.section_widgets = []
        self.scroll_area = None
        self.init_ui()
        
    def init_ui(self):
        """初始化UI - 上下布局结构"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 创建顶部导航区域
        top_nav_widget = self.create_top_navigation()
        main_layout.addWidget(top_nav_widget)

        # 创建内容滚动区域
        content_area = self.create_content_area()
        main_layout.addWidget(content_area)

    def create_top_navigation(self):
        """创建顶部导航区域"""
        nav_widget = QWidget()
        nav_widget.setFixedHeight(80)
        nav_widget.setStyleSheet("""
            QWidget {
                background-color: white;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        
        nav_layout = QVBoxLayout(nav_widget)
        nav_layout.setContentsMargins(20, 15, 20, 15)
        nav_layout.setSpacing(12)
        
        # 页面标题
        title_label = QLabel("帮助中心")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                border: none;
            }
        """)
        nav_layout.addWidget(title_label)
        
        # 标签导航栏
        tab_container = QWidget()
        tab_layout = QHBoxLayout(tab_container)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(8)
        
        # 创建标签按钮
        sections = [
            ("📖", "快速入门"),
            ("⚙️", "功能指南"), 
            ("⌨️", "快捷键"),
            ("❓", "常见问题"),
            ("ℹ️", "关于")
        ]
        
        for i, (icon, title) in enumerate(sections):
            tab_button = self.create_tab_button(icon, title, i)
            self.tab_buttons.append(tab_button)
            tab_layout.addWidget(tab_button)
        
        tab_layout.addStretch()
        nav_layout.addWidget(tab_container)
        
        # 设置第一个标签为激活状态
        if self.tab_buttons:
            self.update_active_tab(0)
        
        return nav_widget

    def create_tab_button(self, icon, title, index):
        """创建圆角标签按钮"""
        button = QPushButton(f"{icon} {title}")
        button.setStyleSheet("""
            QPushButton {
                background-color: #F9FAFB;
                color: #374151;
                border: none;
                border-radius: 20px;
                padding: 10px 18px;
                font-size: 15px;
                font-weight: 600;
                min-width: 90px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
                color: #1F2937;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """)
        button.clicked.connect(lambda: self.scroll_to_section(index))
        return button

    def create_content_area(self):
        """创建单一滚动容器"""
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #F9FAFB;
            }
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(20)
        
        # 添加所有章节卡片
        sections = [
            self.create_quick_start_section(),
            self.create_features_section(),
            self.create_shortcuts_section(),
            self.create_faq_section(),
            self.create_about_section()
        ]
        
        for i, section in enumerate(sections):
            section.setObjectName(f"section_{i}")
            self.section_widgets.append(section)
            content_layout.addWidget(section)
        
        content_layout.addStretch()
        self.scroll_area.setWidget(content_widget)
        
        # 连接滚动事件
        self.scroll_area.verticalScrollBar().valueChanged.connect(self.on_scroll_changed)
        
        return self.scroll_area

    def scroll_to_section(self, section_index):
        """平滑滚动到指定章节"""
        if 0 <= section_index < len(self.section_widgets):
            target_widget = self.section_widgets[section_index]
            
            # 计算目标位置
            target_y = target_widget.y()
            
            # 创建平滑滚动动画
            self.scroll_animation = QPropertyAnimation(self.scroll_area.verticalScrollBar(), b"value")
            self.scroll_animation.setDuration(500)
            self.scroll_animation.setEasingCurve(QEasingCurve.OutCubic)
            self.scroll_animation.setStartValue(self.scroll_area.verticalScrollBar().value())
            self.scroll_animation.setEndValue(target_y)
            self.scroll_animation.start()
            
            # 更新活动标签
            self.update_active_tab(section_index)

    def update_active_tab(self, active_index):
        """更新活动标签状态"""
        self.current_section = active_index
        
        for i, button in enumerate(self.tab_buttons):
            if i == active_index:
                # 激活状态样式
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #3B82F6;
                        color: white;
                        border: none;
                        border-radius: 20px;
                        padding: 10px 18px;
                        font-size: 15px;
                        font-weight: 700;
                        min-width: 90px;
                    }
                    QPushButton:hover {
                        background-color: #2563EB;
                    }
                    QPushButton:pressed {
                        background-color: #1D4ED8;
                    }
                """)
            else:
                # 默认状态样式
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #F9FAFB;
                        color: #374151;
                        border: none;
                        border-radius: 20px;
                        padding: 10px 18px;
                        font-size: 15px;
                        font-weight: 600;
                        min-width: 90px;
                    }
                    QPushButton:hover {
                        background-color: #E5E7EB;
                        color: #1F2937;
                    }
                    QPushButton:pressed {
                        background-color: #D1D5DB;
                    }
                """)

    def on_scroll_changed(self, value):
        """滚动事件处理，自动更新活动标签"""
        if not self.section_widgets:
            return
            
        # 获取可视区域的中心位置
        viewport_center = self.scroll_area.viewport().height() // 2
        scroll_position = value + viewport_center
        
        # 找到当前最接近中心的章节
        current_section = 0
        for i, widget in enumerate(self.section_widgets):
            widget_center = widget.y() + widget.height() // 2
            if scroll_position >= widget_center:
                current_section = i
        
        # 只在章节变化时更新标签状态
        if current_section != self.current_section:
            self.update_active_tab(current_section)

    def create_section_card(self, title, content):
        """创建章节卡片的通用方法"""
        card = QFrame()
        card.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(24, 20, 24, 24)
        layout.setSpacing(16)
        
        # 章节标题
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 20px;
                font-weight: 600;
                border: none;
                background: transparent;
                margin-bottom: 8px;
            }
        """)
        layout.addWidget(title_label)
        
        # 章节内容
        if isinstance(content, str):
            content_label = QLabel(content)
            content_label.setWordWrap(True)
            content_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
            content_label.setStyleSheet("""
                QLabel {
                    color: #4B5563;
                    font-size: 14px;
                    line-height: 1.6;
                    border: none;
                    background: transparent;
                }
            """)
            layout.addWidget(content_label)
        else:
            # 如果content是widget，直接添加
            layout.addWidget(content)
        
        return card

    def create_quick_start_section(self):
        """创建快速入门章节"""
        content = """🎯 **欢迎使用Prompt收藏助手**

Prompt收藏助手是一个优雅的提示词管理工具，帮助您高效地组织、管理和使用各种AI提示词。

**主要特点**：
• 直观的卡片式界面设计
• 强大的分类和标签管理
• 支持媒体文件附件
• 便捷的导入导出功能
• 智能搜索和筛选

**基本操作指南**：

📝 **创建提示词**：
1. 点击搜索框右侧的"+"按钮
2. 填写标题、内容、分类和标签
3. 可选择添加媒体文件
4. 点击保存完成创建

🔍 **搜索和筛选**：
• 使用顶部搜索框进行关键词搜索
• 点击工具栏的筛选按钮进行分类筛选
• 支持收藏、分类、标签多重筛选

✏️ **编辑和管理**：
• 右键点击卡片查看操作菜单
• 支持收藏、置顶、编辑、删除等操作
• 删除的提示词会移至回收站"""
        
        return self.create_section_card("快速入门", content)

    def create_features_section(self):
        """创建功能指南章节"""
        content = """⚙️ **提示词管理**

📋 **创建和编辑**：
• 支持富文本内容编辑
• 可设置分类和多个标签
• 支持添加图片、视频等媒体文件
• 自动保存编辑历史

⭐ **收藏和置顶**：
• 收藏重要的提示词便于快速访问
• 置顶功能让重要内容始终显示在前
• 支持批量操作管理

📤 **导出功能**：
• 支持JSON、CSV、Markdown格式
• 可选择导出范围和内容
• 保持数据完整性和格式

📥 **导入功能**：
• 支持多种格式文件导入
• 批量导入和数据验证
• 重复数据智能处理

🔍 **搜索和筛选**：
• 强大的全文搜索功能
• 多维度筛选条件
• 智能排序和分组"""
        
        return self.create_section_card("功能指南", content)

    def create_shortcuts_section(self):
        """创建快捷键章节"""
        content = """⌨️ **基本操作快捷键**

**通用快捷键**：
• `Ctrl + N`：新建提示词
• `Ctrl + S`：保存当前编辑
• `Ctrl + F`：搜索提示词
• `Delete`：删除选中项
• `Escape`：取消选择/关闭对话框

**视图操作**：
• `Ctrl + 1`：切换到卡片视图
• `Ctrl + 2`：切换到列表视图
• `F5`：刷新数据

**导航快捷键**：
• `Ctrl + H`：返回首页
• `Ctrl + T`：打开回收站
• `Ctrl + ,`：打开设置页面
• `F1`：打开帮助页面

**列表操作**：
• `↑/↓`：上下选择项目
• `Enter`：编辑选中项目
• `Space`：切换收藏状态"""
        
        return self.create_section_card("快捷键", content)

    def create_faq_section(self):
        """创建常见问题章节"""
        faq_widget = QWidget()
        layout = QVBoxLayout(faq_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(16)
        
        faqs = [
            {
                "question": "如何备份我的提示词数据？",
                "answer": "进入 **设置 → 数据管理**，点击\"备份数据\"按钮，然后选择一个安全的位置保存您的数据库文件。"
            },
            {
                "question": "提示词被误删了怎么办？",
                "answer": "不用担心，所有删除的提示词都会被移至 **回收站**。您可以在回收站中找到它们并选择恢复。"
            },
            {
                "question": "应用运行缓慢怎么办？",
                "answer": "可以尝试以下方法：\n1. 在 **设置 → 高级设置** 中启用\"性能优化模式\"。\n2. 在 **设置 → 数据管理** 中\"清理缓存\"。\n3. 定期清理回收站和不需要的提示词。"
            },
            {
                "question": "如何自定义分类和标签？",
                "answer": "在创建或编辑提示词时，直接在分类或标签输入框中输入新的名称即可。系统会自动保存并用于后续的自动补全。"
            }
        ]
        
        for faq in faqs:
            faq_item = self.create_faq_item(faq["question"], faq["answer"])
            layout.addWidget(faq_item)
        
        return self.create_section_card("常见问题", faq_widget)

    def create_faq_item(self, question, answer):
        """创建FAQ条目"""
        item_widget = QWidget()
        item_layout = QVBoxLayout(item_widget)
        item_layout.setContentsMargins(0, 0, 0, 12)
        item_layout.setSpacing(8)
        
        # 问题
        question_label = QLabel(f"❓ {question}")
        question_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 15px;
                font-weight: 600;
                border: none;
                background: transparent;
            }
        """)
        item_layout.addWidget(question_label)
        
        # 答案
        answer_label = QLabel(answer)
        answer_label.setWordWrap(True)
        answer_label.setTextInteractionFlags(Qt.TextSelectableByMouse)
        answer_label.setStyleSheet("""
            QLabel {
                color: #4B5563;
                font-size: 14px;
                line-height: 1.6;
                border: none;
                background: transparent;
                padding-left: 24px;
            }
        """)
        item_layout.addWidget(answer_label)
        
        return item_widget

    def create_about_section(self):
        """创建关于章节"""
        content = f"""ℹ️ **关于Prompt收藏助手**

**版本信息**：1.0.0
**开发框架**：PySide6 (Qt6)
**数据库**：SQLite

专为AI时代设计的提示词管理工具，致力于提供简洁、高效、美观的用户体验。

**当前环境**：
• 操作系统：{self.get_system_info()}
• Python版本：{self.get_python_version()}

**联系我们**：
如果您在使用过程中遇到问题或有改进建议，欢迎通过应用内反馈功能联系我们。

感谢您选择Prompt收藏助手！您的支持是我们持续改进的动力。"""
        
        return self.create_section_card("关于", content)

    def get_system_info(self):
        import platform
        return f"{platform.system()} {platform.release()}"
        
    def get_python_version(self):
        import platform
        return platform.python_version()
    
    # ================== AI配置相关方法 ==================
    

    
    def test_api_connection(self, platform_id):
        """测试API连接"""
        try:
            from services import ai_service, AI_SERVICE_AVAILABLE
            
            if not AI_SERVICE_AVAILABLE:
                QMessageBox.warning(self, "测试失败", "AI服务不可用")
                return
            
            # 获取API密钥
            api_key = self.api_key_widgets[platform_id]['edit'].text().strip()
            if not api_key:
                QMessageBox.warning(self, "测试失败", "请先输入API密钥")
                return
            
            # 保存API密钥到服务中
            ai_service.save_api_key(platform_id, api_key)
            
            # 更新按钮状态
            test_btn = self.api_key_widgets[platform_id]['test_btn']
            status_label = self.api_key_widgets[platform_id]['status_label']
            
            test_btn.setEnabled(False)
            test_btn.setText("测试中...")
            status_label.setText("连接测试中...")
            status_label.setStyleSheet("color: #F59E0B; font-size: 11px;")
            
            # 执行连接测试
            result = ai_service.test_connection(platform_id)
            
            # 恢复按钮状态
            test_btn.setEnabled(True)
            test_btn.setText("测试")
            
            if result['success']:
                status_label.setText("✅ 连接成功")
                status_label.setStyleSheet("color: #10B981; font-size: 11px;")
                QMessageBox.information(self, "测试成功", f"{result['message']}\n\n模型: {result['details']['model']}\nTokens: {result['details']['tokens']}")
            else:
                status_label.setText("❌ 连接失败")
                status_label.setStyleSheet("color: #DC2626; font-size: 11px;")
                QMessageBox.warning(self, "测试失败", f"{result['message']}\n\n错误: {result.get('error', '未知错误')}")
                
        except Exception as e:
            # 恢复按钮状态
            if platform_id in self.api_key_widgets:
                test_btn = self.api_key_widgets[platform_id]['test_btn']
                status_label = self.api_key_widgets[platform_id]['status_label']
                test_btn.setEnabled(True)
                test_btn.setText("测试")
                status_label.setText("❌ 测试异常")
                status_label.setStyleSheet("color: #DC2626; font-size: 11px;")
            
            QMessageBox.critical(self, "测试错误", f"连接测试时发生错误:\n{str(e)}")
    

    
    def save_ai_config(self):
        """保存AI配置"""
        try:
            from services import ai_service, AI_SERVICE_AVAILABLE
            
            if not AI_SERVICE_AVAILABLE:
                QMessageBox.warning(self, "保存失败", "AI服务不可用")
                return
            
            # 保存API密钥
            for platform_id, widgets in self.api_key_widgets.items():
                api_key = widgets['edit'].text().strip()
                if api_key:
                    ai_service.save_api_key(platform_id, api_key)
            
            # 保存AI参数到QSettings
            temperature = self.temperature_slider.value() / 100.0
            max_tokens = self.max_tokens_spinbox.value()
            
            self.settings.setValue("ai/temperature", temperature)
            self.settings.setValue("ai/max_tokens", max_tokens)
            self.settings.sync()
            
            # 更新状态
            for platform_id, widgets in self.api_key_widgets.items():
                api_key = widgets['edit'].text().strip()
                if api_key:
                    widgets['status_label'].setText("已保存")
                    widgets['status_label'].setStyleSheet("color: #10B981; font-size: 11px;")
                else:
                    widgets['status_label'].setText("未配置")
                    widgets['status_label'].setStyleSheet("color: #6B7280; font-size: 11px;")
            
            QMessageBox.information(self, "保存成功", "AI配置已保存")
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"保存AI配置时发生错误:\n{str(e)}")
    
    def load_ai_config(self):
        """加载AI配置"""
        try:
            from services import ai_service, AI_SERVICE_AVAILABLE
            
            if not AI_SERVICE_AVAILABLE:
                return
            
            # 加载API密钥
            for platform_id, widgets in self.api_key_widgets.items():
                if platform_id in ai_service._api_keys:
                    api_key = ai_service._api_keys[platform_id]
                    widgets['edit'].setText(api_key)
                    widgets['status_label'].setText("已配置")
                    widgets['status_label'].setStyleSheet("color: #10B981; font-size: 11px;")
                else:
                    widgets['status_label'].setText("未配置")
                    widgets['status_label'].setStyleSheet("color: #6B7280; font-size: 11px;")
            
            # 加载AI参数
            temperature = self.settings.value("ai/temperature", 0.7, float)
            max_tokens = self.settings.value("ai/max_tokens", 1000, int)
            
            self.temperature_slider.setValue(int(temperature * 100))
            self.max_tokens_spinbox.setValue(max_tokens)
            
            # 更新温度显示
            self.temperature_value_label.setText(f"{temperature:.1f}")
            
        except Exception as e:
            print(f"加载AI配置时发生错误: {e}")