#!/usr/bin/env python3
"""
项目依赖安装脚本
确保项目所需的所有依赖包都正确安装
"""
import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def install_from_requirements():
    """从requirements.txt安装依赖"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    """主安装函数"""
    print("正在安装项目所需依赖...")
    
    # 首先尝试从requirements.txt安装
    if os.path.exists("requirements.txt"):
        print("\n📦 从requirements.txt安装依赖...")
        if install_from_requirements():
            print("✅ requirements.txt依赖安装成功")
            
            # 运行依赖检查
            print("\n🔍 运行依赖检查...")
            try:
                subprocess.check_call([sys.executable, "check_dependencies.py"])
                print("\n🎉 所有依赖安装并验证成功！")
                return True
            except subprocess.CalledProcessError:
                print("\n⚠️  依赖检查发现问题，尝试手动安装...")
        else:
            print("❌ requirements.txt安装失败，尝试手动安装...")
    
    # 手动安装关键依赖
    packages = [
        "PySide6>=6.0.0",
        "PySide6-Addons>=6.0.0",
        "opencv-python>=4.5.0.0",
        "Pillow>=10.0.0",
        "aiohttp>=3.8.0",
        "requests>=2.28.0", 
        "openai>=1.0.0",
        "httpx>=0.24.0",
        "cryptography>=40.0.0",
        "python-dotenv>=1.0.0"
    ]
    
    print(f"\n📦 手动安装{len(packages)}个关键依赖包...")
    success_count = 0
    for package in packages:
        print(f"\n安装 {package}...")
        if install_package(package):
            print(f"✅ {package} 安装成功")
            success_count += 1
        else:
            print(f"❌ {package} 安装失败")
    
    print(f"\n安装完成: {success_count}/{len(packages)} 成功")
    
    if success_count == len(packages):
        print("\n🎉 所有依赖安装成功！")
        
        # 运行依赖检查验证
        print("\n🔍 运行最终验证...")
        try:
            subprocess.check_call([sys.executable, "check_dependencies.py"])
            print("\n✅ 依赖验证通过，项目已准备就绪！")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("\n⚠️  无法运行依赖检查，手动验证...")
            try:
                from services import AI_SERVICE_AVAILABLE
                if AI_SERVICE_AVAILABLE:
                    print("✅ AI服务验证通过")
                else:
                    print("⚠️  AI服务不可用，可能需要重启应用")
            except ImportError:
                print("⚠️  AI服务验证失败，可能需要重启应用")
                
        print("\n📋 后续操作:")
        print("1. 启动应用: python main.py")
        print("2. 进入设置页面配置AI密钥")
        print("3. 享受AI增强的提示词管理体验！")
        
    else:
        print("\n❌ 部分依赖安装失败，请检查:")
        print("1. 网络连接是否正常")
        print("2. Python版本是否兼容 (建议3.8+)")
        print("3. 是否有足够的磁盘空间")
        print("4. 防病毒软件是否阻止了安装")

if __name__ == "__main__":
    main() 