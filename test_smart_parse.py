#!/usr/bin/env python3
"""
测试智能解析功能
"""
import sys
from PySide6.QtWidgets import QApplication
from create_prompt_dialog import CreatePromptDialog

def test_smart_parse():
    """测试智能解析功能"""
    app = QApplication(sys.argv)
    
    # 创建对话框
    dialog = CreatePromptDialog()
    
    # 设置测试内容
    test_content = """高质量人物肖像
请生成一张高质量的人物肖像图片，要求：
- 8K超高清分辨率
- 真实感渲染
- 艺术风格
- 现代感

负面提示词：
模糊，低质量，变形，扭曲，不真实"""
    
    dialog.instruction_text.setPlainText(test_content)
    
    # 显示对话框
    dialog.show()
    
    print("测试智能解析功能：")
    print("1. 在对话框中点击'智能解析'按钮")
    print("2. 观察是否自动填充了标题、类型、分类、标签等信息")
    print("3. 检查是否自动添加了负向提示词字段")
    
    sys.exit(app.exec())

if __name__ == "__main__":
    test_smart_parse() 