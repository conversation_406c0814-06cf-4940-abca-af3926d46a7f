#!/usr/bin/env python3
"""
彻底清理main.py中的所有XML标签和工具标签
"""

def clean_all_tags():
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"原始文件大小: {len(content)} 字符")
        
        # 定义所有需要清理的标签模式
        patterns_to_remove = [
            '<replace_in_file>', '</replace_in_file>',
            '<diff>', '</diff>',
            '<execute_command>', '</execute_command>',
            '<search_files>', '</search_files>',
            '<read_file>', '</read_file>',
            '<write_to_file>', '</write_to_file>',
            '<list_files>', '</list_files>',
            '<command>', '</command>',
            '<path>', '</path>',
            '<content>', '</content>',
            '<requires_approval>', '</requires_approval>',
            '<regex>', '</regex>',
            '<file_pattern>', '</file_pattern>',
            '<<<<<<< SEARCH', '>>>>>>> REPLACE',
            '=======',
            '<tool_name>', '</tool_name>',
            '<parameter1_name>', '</parameter1_name>',
            '<parameter2_name>', '</parameter2_name>'
        ]
        
        # 按行处理，删除包含这些标签的行
        lines = content.split('\n')
        cleaned_lines = []
        removed_count = 0
        
        for i, line in enumerate(lines):
            should_remove = False
            
            # 检查是否包含任何需要删除的标签
            for pattern in patterns_to_remove:
                if pattern in line:
                    print(f"删除第{i+1}行: {line.strip()[:80]}...")
                    should_remove = True
                    removed_count += 1
                    break
            
            # 如果行只包含标签内容，也删除
            stripped_line = line.strip()
            if (stripped_line.startswith('<') and stripped_line.endswith('>') and 
                len(stripped_line) < 100):
                print(f"删除标签行第{i+1}行: {stripped_line}")
                should_remove = True
                removed_count += 1
            
            if not should_remove:
                cleaned_lines.append(line)
        
        # 重新组合内容
        cleaned_content = '\n'.join(cleaned_lines)
        
        print(f"清理后文件大小: {len(cleaned_content)} 字符")
        print(f"共删除 {removed_count} 行")
        
        # 写回文件
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        print("彻底清理完成！")
        
    except Exception as e:
        print(f"清理过程中出错: {e}")

if __name__ == '__main__':
    clean_all_tags()