#!/usr/bin/env python3
"""
标签服务模块
提供标签相关业务逻辑，将数据访问与UI分离
"""
from typing import List, Set, Dict, Optional
import time

from model import PromptModel
from utils.error_handler import log_error, DatabaseError, DataError
from interfaces.service_interfaces import ITagService

class TagService(ITagService):
    """标签服务类，处理标签相关业务逻辑"""
    
    def __init__(self, model: PromptModel):
        """
        初始化标签服务
        :param model: 提示词数据模型
        """
        self.model = model
        self._cache = {
            'all_tags': [],
            'last_updated': 0
        }
        self._cache_ttl = 30  # 缓存有效期（秒）
    
    def get_all_tags(self, use_cache: bool = True) -> List[str]:
        """
        获取所有标签
        :param use_cache: 是否使用缓存
        :return: 标签列表
        """
        current_time = time.time()
        
        # 如果启用缓存且缓存未过期，则使用缓存
        if use_cache and self._cache['all_tags'] and \
           current_time - self._cache['last_updated'] < self._cache_ttl:
            return self._cache['all_tags']
        
        try:
            tags = self.model.get_all_tags()
            
            # 更新缓存
            self._cache['all_tags'] = tags
            self._cache['last_updated'] = current_time
            
            return tags
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取标签列表失败", str(e))
    
    def get_tag_counts(self) -> Dict[str, int]:
        """
        获取标签及其使用次数
        :return: 标签及使用次数字典，格式为 {tag: count}
        """
        try:
            # 获取所有提示词
            prompts = self.model.get_all_prompts()
            
            # 计算标签使用次数
            tag_counts = {}
            for prompt in prompts:
                for tag in prompt.tags:
                    if tag in tag_counts:
                        tag_counts[tag] += 1
                    else:
                        tag_counts[tag] = 1
            
            return tag_counts
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取标签使用次数失败", str(e))
    
    def get_related_tags(self, selected_tags: List[str]) -> Dict[str, int]:
        """
        获取与已选标签相关的标签及其关联度
        :param selected_tags: 已选标签列表
        :return: 相关标签及关联度字典，格式为 {tag: count}
        """
        try:
            if not selected_tags:
                return {}
            
            # 获取所有带有选定标签的提示词
            selected_tags_set = set(selected_tags)
            prompts = self.model.get_all_prompts()
            related_prompts = [p for p in prompts if set(p.tags).intersection(selected_tags_set)]
            
            # 统计相关标签
            related_tags = {}
            for prompt in related_prompts:
                for tag in prompt.tags:
                    # 排除已选标签
                    if tag not in selected_tags_set:
                        if tag in related_tags:
                            related_tags[tag] += 1
                        else:
                            related_tags[tag] = 1
            
            return related_tags
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取相关标签失败", str(e))
    
    def clean_unused_tags(self) -> int:
        """
        清理未使用的标签（暂时未实现，因为标签存储在提示词中）
        :return: 清理的标签数量
        """
        # 标签直接存储在提示词中，没有单独的标签表
        # 此方法暂时只是为了API完整性
        return 0
    
    def validate_tag(self, tag: str) -> bool:
        """
        验证标签是否有效
        :param tag: 标签字符串
        :return: 是否有效
        """
        # 标签不能为空
        if not tag.strip():
            return False
        
        # 标签长度限制
        if len(tag) > 50:
            return False
        
        # 可以添加其他验证规则，如不允许特殊字符等
        
        return True
    
    def normalize_tag(self, tag: str) -> str:
        """
        标准化标签（去除首尾空格、转换为小写等）
        :param tag: 原始标签
        :return: 标准化后的标签
        """
        return tag.strip()
    
    def normalize_tags(self, tags: List[str]) -> List[str]:
        """
        标准化标签列表
        :param tags: 原始标签列表
        :return: 标准化后的标签列表
        """
        # 标准化每个标签
        normalized = [self.normalize_tag(tag) for tag in tags]
        
        # 过滤空标签
        normalized = [tag for tag in normalized if tag]
        
        # 去重
        normalized = list(set(normalized))
        
        return normalized
    
    def get_popular_tags(self, limit: int = 10) -> List[str]:
        """
        获取热门标签
        :param limit: 返回的标签数量限制
        :return: 热门标签列表
        """
        try:
            tag_counts = self.get_tag_counts()
            # 按使用次数排序，返回前limit个
            sorted_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
            return [tag for tag, count in sorted_tags[:limit]]
        except Exception as e:
            log_error(e)
            return []
    
    def search_tags(self, keyword: str) -> List[str]:
        """
        搜索标签
        :param keyword: 搜索关键词
        :return: 匹配的标签列表
        """
        try:
            all_tags = self.get_all_tags()
            keyword_lower = keyword.lower()
            # 模糊匹配标签
            return [tag for tag in all_tags if keyword_lower in tag.lower()]
        except Exception as e:
            log_error(e)
            return []
    
    def invalidate_cache(self):
        """清除缓存"""
        self._cache['all_tags'] = []
        self._cache['last_updated'] = 0 