#!/usr/bin/env python3
"""
测试标签多选筛选功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from model import PromptModel, Prompt

def test_tag_multiselect_logic():
    """测试标签多选筛选逻辑"""
    print("=== 测试标签多选筛选逻辑 ===")
    
    # 创建测试数据 - 修正Prompt构造函数参数顺序
    test_prompts = []
    
    # 手动创建Prompt对象并设置属性
    p1 = Prompt(1, "AI助手提示词", "你是一个AI助手", "AI工具")
    p1.tags = ["AI", "助手", "工具"]
    test_prompts.append(p1)
    
    p2 = Prompt(2, "写作助手", "帮助用户写作", "创作工具")
    p2.tags = ["写作", "创作", "工具"]
    test_prompts.append(p2)
    
    p3 = Prompt(3, "编程助手", "帮助编程", "开发工具")
    p3.tags = ["编程", "开发", "AI"]
    test_prompts.append(p3)
    
    p4 = Prompt(4, "翻译工具", "多语言翻译", "语言工具")
    p4.tags = ["翻译", "语言", "工具"]
    test_prompts.append(p4)
    
    p5 = Prompt(5, "创意生成", "生成创意内容", "创作工具")
    p5.tags = ["创意", "生成", "AI"]
    test_prompts.append(p5)
    
    # 测试单标签筛选
    print("\n1. 测试单标签筛选 - 选择'工具'标签:")
    selected_tags = {"工具"}
    filtered = [p for p in test_prompts if selected_tags.issubset(set(p.tags))]
    print(f"   筛选结果: {len(filtered)} 个提示词")
    for p in filtered:
        print(f"   - {p.title}: {p.tags}")
    
    # 测试多标签筛选 - AND逻辑
    print("\n2. 测试多标签筛选 - 选择'AI'和'工具'标签:")
    selected_tags = {"AI", "工具"}
    filtered = [p for p in test_prompts if selected_tags.issubset(set(p.tags))]
    print(f"   筛选结果: {len(filtered)} 个提示词")
    for p in filtered:
        print(f"   - {p.title}: {p.tags}")
    
    # 测试多标签筛选 - 更严格的条件
    print("\n3. 测试多标签筛选 - 选择'创作'和'工具'标签:")
    selected_tags = {"创作", "工具"}
    filtered = [p for p in test_prompts if selected_tags.issubset(set(p.tags))]
    print(f"   筛选结果: {len(filtered)} 个提示词")
    for p in filtered:
        print(f"   - {p.title}: {p.tags}")
    
    # 测试无匹配结果
    print("\n4. 测试无匹配结果 - 选择'不存在'标签:")
    selected_tags = {"不存在"}
    filtered = [p for p in test_prompts if selected_tags.issubset(set(p.tags))]
    print(f"   筛选结果: {len(filtered)} 个提示词")
    
    print("\n=== 测试完成 ===")

def test_tag_extraction():
    """测试标签提取逻辑"""
    print("\n=== 测试标签提取逻辑 ===")
    
    # 手动创建测试数据并设置tags属性
    test_prompts = []
    
    p1 = Prompt(1, "测试1", "内容1", "分类1")
    p1.tags = ["标签A", "标签B", "标签C"]
    test_prompts.append(p1)
    
    p2 = Prompt(2, "测试2", "内容2", "分类2")
    p2.tags = ["标签B", "标签D"]
    test_prompts.append(p2)
    
    p3 = Prompt(3, "测试3", "内容3", "分类1")
    p3.tags = ["标签A", "标签E"]
    test_prompts.append(p3)
    
    p4 = Prompt(4, "测试4", "内容4", "分类3")
    p4.tags = None  # 无标签
    test_prompts.append(p4)
    
    p5 = Prompt(5, "测试5", "内容5", "分类2")
    p5.tags = []    # 空标签列表
    test_prompts.append(p5)
    
    # 提取所有唯一标签
    all_tags = set()
    for prompt in test_prompts:
        if hasattr(prompt, 'tags') and prompt.tags:
            for tag in prompt.tags:
                if tag and tag.strip():
                    all_tags.add(tag.strip())
    
    unique_tags = sorted(list(all_tags))
    print(f"提取到的唯一标签: {unique_tags}")
    print(f"标签总数: {len(unique_tags)}")
    
    print("\n=== 标签提取测试完成 ===")

if __name__ == "__main__":
    test_tag_multiselect_logic()
    test_tag_extraction()