#!/usr/bin/env python3
"""
测试create_action_buttons方法的执行
"""

from PySide6.QtWidgets import QApplication
import sys
from model import PromptModel, Prompt

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🔍 测试create_action_buttons方法的执行...")

# 获取一个测试提示词
model = PromptModel()
prompts = model.get_all_prompts()
if not prompts:
    print("❌ 没有找到测试数据")
    exit(1)

test_prompt = prompts[0]
print(f"📝 测试提示词: ID={test_prompt.id}, 收藏状态={test_prompt.is_favorite}")

# 在PromptCardWidget的create_action_buttons方法中添加调试信息
import main

# 保存原始方法
original_create_action_buttons = main.PromptCardWidget.create_action_buttons

def debug_create_action_buttons(self):
    print(f"🔧 create_action_buttons 被调用，提示词ID: {self.prompt.id}")
    
    # 调用原始方法
    result = original_create_action_buttons(self)
    
    # 检查结果
    if 'favorite' in self.action_buttons:
        btn = self.action_buttons['favorite']
        print(f"   ✅ 收藏按钮已创建")
        print(f"   is_favorited: {getattr(btn, 'is_favorited', '未设置')}")
        
        # 尝试手动调用事件处理器
        print("   🧪 手动调用on_favorite_action...")
        try:
            self.on_favorite_action()
            print("   ✅ on_favorite_action 调用成功")
        except Exception as e:
            print(f"   ❌ on_favorite_action 调用失败: {e}")
    else:
        print("   ❌ 收藏按钮未找到")
    
    return result

# 替换方法
main.PromptCardWidget.create_action_buttons = debug_create_action_buttons

# 创建PromptCardWidget
print("🏗️ 创建PromptCardWidget...")
card = main.PromptCardWidget(test_prompt)
print("✅ PromptCardWidget创建完成")

print("🏁 测试完成") 