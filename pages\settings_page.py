#!/usr/bin/env python3
"""
设置页面组件
从ContentArea提取的设置页面相关逻辑
"""
from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QWidget, QLabel, QPushButton, QFrame
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from pages.base_page import BasePage
from components.style_manager import StyleManager


class SettingsPage(BasePage):
    """设置页面 - 继承BasePage，处理设置相关功能"""
    
    # 定义信号
    setting_changed = Signal(str, object)  # 设置变更信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def load_data(self):
        """加载设置数据 - 设置页面暂无需加载数据"""
        pass
        
    def setup_ui(self):
        """设置UI布局 - 从ContentArea.create_settings_page提取"""
        main_layout = self.get_main_layout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("设置")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setStyleSheet("color: #1F2937; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 设置项容器
        settings_container = QWidget()
        settings_layout = QVBoxLayout(settings_container)
        settings_layout.setSpacing(15)
        
        # 应用StyleManager样式
        StyleManager.apply_transparent_background(settings_container)
        
        # 添加各种设置项
        self.add_database_settings(settings_layout)
        self.add_display_settings(settings_layout)
        self.add_performance_settings(settings_layout)
        self.add_import_export_settings(settings_layout)
        
        # 添加弹簧
        settings_layout.addStretch()
        
        main_layout.addWidget(settings_container)
        
    def add_database_settings(self, layout):
        """添加数据库设置"""
        # 数据库设置组
        db_group = self.create_setting_group("数据库设置")
        db_layout = QVBoxLayout(db_group)
        
        # 数据库路径设置
        db_path_item = self.create_setting_item(
            "数据库路径",
            "设置提示词数据库文件的存储位置"
        )
        db_layout.addWidget(db_path_item)
        
        # 备份设置
        backup_item = self.create_setting_item(
            "自动备份",
            "定期自动备份数据库文件"
        )
        db_layout.addWidget(backup_item)
        
        layout.addWidget(db_group)
        
    def add_display_settings(self, layout):
        """添加显示设置"""
        # 显示设置组
        display_group = self.create_setting_group("显示设置")
        display_layout = QVBoxLayout(display_group)
        
        # 主题设置
        theme_item = self.create_setting_item(
            "主题模式",
            "选择应用程序的主题样式"
        )
        display_layout.addWidget(theme_item)
        
        # 字体大小设置
        font_item = self.create_setting_item(
            "字体大小",
            "调整界面文字的显示大小"
        )
        display_layout.addWidget(font_item)
        
        layout.addWidget(display_group)
        
    def add_performance_settings(self, layout):
        """添加性能设置"""
        # 性能设置组
        perf_group = self.create_setting_group("性能设置")
        perf_layout = QVBoxLayout(perf_group)
        
        # 缓存设置
        cache_item = self.create_setting_item(
            "缓存管理",
            "管理应用程序的缓存数据"
        )
        perf_layout.addWidget(cache_item)
        
        # 内存优化
        memory_item = self.create_setting_item(
            "内存优化",
            "启用内存使用优化"
        )
        perf_layout.addWidget(memory_item)
        
        layout.addWidget(perf_group)
        
    def add_import_export_settings(self, layout):
        """添加导入导出设置"""
        # 导入导出设置组
        ie_group = self.create_setting_group("导入导出")
        ie_layout = QVBoxLayout(ie_group)
        
        # 默认导出格式
        export_format_item = self.create_setting_item(
            "默认导出格式",
            "设置导出提示词时的默认文件格式"
        )
        ie_layout.addWidget(export_format_item)
        
        # 导入重复处理
        import_duplicate_item = self.create_setting_item(
            "导入重复处理",
            "设置导入时遇到重复提示词的处理方式"
        )
        ie_layout.addWidget(import_duplicate_item)
        
        layout.addWidget(ie_group)
        
    def create_setting_group(self, title):
        """创建设置组容器"""
        group = QFrame()
        group.setFrameStyle(QFrame.Box | QFrame.Raised)
        group.setLineWidth(1)
        
        # 应用StyleManager样式
        card_style = StyleManager.get_card_style()
        group.setStyleSheet(f"""
            QFrame {{
                {card_style}
                margin: 5px;
                padding: 10px;
            }}
        """)
        
        # 组标题
        title_label = QLabel(title)
        title_label.setFont(QFont("微软雅黑", 12, QFont.Bold))
        title_label.setStyleSheet("color: #374151; margin: 5px 0px;")
        
        # 主布局
        main_layout = QVBoxLayout(group)
        main_layout.addWidget(title_label)
        
        return group
        
    def create_setting_item(self, title, description):
        """创建设置项 - 从ContentArea.create_setting_item提取"""
        item_widget = QWidget()
        item_layout = QHBoxLayout(item_widget)
        item_layout.setContentsMargins(10, 8, 10, 8)
        
        # 左侧信息
        info_layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("微软雅黑", 10, QFont.Bold))
        title_label.setStyleSheet("color: #1F2937;")
        info_layout.addWidget(title_label)
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setFont(QFont("微软雅黑", 9))
        desc_label.setStyleSheet("color: #6B7280;")
        desc_label.setWordWrap(True)
        info_layout.addWidget(desc_label)
        
        item_layout.addLayout(info_layout)
        item_layout.addStretch()
        
        # 右侧操作按钮
        action_btn = QPushButton("配置")
        action_btn.setFixedSize(60, 28)
        
        # 应用StyleManager按钮样式
        button_style = StyleManager.get_button_style('secondary')
        action_btn.setStyleSheet(button_style)
        
        item_layout.addWidget(action_btn)
        
        # 连接信号
        action_btn.clicked.connect(lambda: self.on_setting_action(title))
        
        # 应用StyleManager背景样式
        StyleManager.apply_transparent_background(item_widget)
        
        return item_widget
        
    def on_setting_action(self, setting_name):
        """处理设置项操作"""
        # 这里可以根据不同的设置项执行不同的操作
        # 例如打开设置对话框、切换开关等
        print(f"配置设置项: {setting_name}")
        
        # 发射设置变更信号
        self.setting_changed.emit(setting_name, None) 