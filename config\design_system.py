#!/usr/bin/env python3
"""
现代化设计系统
统一的颜色、间距、阴影、按钮样式等设计规范
"""

# ==================== 色彩系统 ====================
COLORS = {
    # 主色调
    'primary': '#3B82F6',
    'primary_hover': '#2563EB', 
    'primary_active': '#1D4ED8',
    'primary_light': '#EBF4FF',
    
    # 成功色
    'success': '#10B981',
    'success_hover': '#059669',
    'success_light': '#D1FAE5',
    
    # 警告色
    'warning': '#F59E0B',
    'warning_hover': '#D97706',
    'warning_light': '#FEF3C7',
    
    # 错误色
    'error': '#EF4444',
    'error_hover': '#DC2626',
    'error_light': '#FEE2E2',
    
    # 中性色阶
    'white': '#FFFFFF',
    'gray_50': '#F9FAFB',
    'gray_100': '#F3F4F6',
    'gray_200': '#E5E7EB',
    'gray_300': '#D1D5DB',
    'gray_400': '#9CA3AF',
    'gray_500': '#6B7280',
    'gray_600': '#4B5563',
    'gray_700': '#374151',
    'gray_800': '#1F2937',
    'gray_900': '#111827',
}

# ==================== 间距系统 ====================
SPACING = {
    'xs': '4px',
    'sm': '8px',
    'md': '12px',
    'lg': '16px',
    'xl': '20px',
    'xxl': '24px',
    'xxxl': '32px',
}

# ==================== 圆角系统 ====================
RADIUS = {
    'sm': '6px',
    'md': '8px',
    'lg': '12px',
    'xl': '16px',
    'full': '9999px',
}

# ==================== 阴影系统 ====================
SHADOWS = {
    'sm': '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    'md': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    'lg': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    'xl': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    'card_hover': '0 12px 24px -4px rgba(59, 130, 246, 0.15), 0 4px 8px -2px rgba(59, 130, 246, 0.1)',
}

# ==================== 字体系统 ====================
TYPOGRAPHY = {
    'font_family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    'sizes': {
        'xs': '11px',
        'sm': '12px',
        'base': '14px',
        'lg': '16px',
        'xl': '18px',
        'xxl': '20px',
    },
    'weights': {
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
    },
    'line_heights': {
        'tight': '1.25',
        'normal': '1.5',
        'relaxed': '1.75',
    }
}

# ==================== 按钮样式模板 ====================
BUTTON_STYLES = {
    'primary': f"""
        QPushButton {{
            background-color: {COLORS['primary']};
            color: {COLORS['white']};
            border: none;
            border-radius: {RADIUS['lg']};
            padding: {SPACING['sm']} {SPACING['lg']};
            font-size: {TYPOGRAPHY['sizes']['base']};
            font-weight: {TYPOGRAPHY['weights']['medium']};
            font-family: {TYPOGRAPHY['font_family']};
            min-height: 40px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['primary_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['primary_active']};
        }}
        QPushButton:disabled {{
            background-color: {COLORS['gray_300']};
            color: {COLORS['gray_500']};
        }}
    """,
    
    'primary_compact': f"""
        QPushButton {{
            background-color: {COLORS['primary']};
            color: {COLORS['white']};
            border: none;
            border-radius: {RADIUS['md']};
            padding: {SPACING['xs']} {SPACING['md']};
            font-size: {TYPOGRAPHY['sizes']['sm']};
            font-weight: {TYPOGRAPHY['weights']['medium']};
            font-family: {TYPOGRAPHY['font_family']};
            min-height: 32px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['primary_hover']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['primary_active']};
        }}
        QPushButton:disabled {{
            background-color: {COLORS['gray_300']};
            color: {COLORS['gray_500']};
        }}
    """,
    
    'secondary': f"""
        QPushButton {{
            background-color: {COLORS['gray_100']};
            color: {COLORS['gray_700']};
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['lg']};
            padding: {SPACING['sm']} {SPACING['lg']};
            font-size: {TYPOGRAPHY['sizes']['base']};
            font-weight: {TYPOGRAPHY['weights']['medium']};
            font-family: {TYPOGRAPHY['font_family']};
            min-height: 40px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['gray_200']};
            border-color: {COLORS['gray_400']};
        }}
        QPushButton:pressed {{
            background-color: {COLORS['gray_300']};
        }}
        QPushButton:disabled {{
            background-color: {COLORS['gray_50']};
            color: {COLORS['gray_400']};
            border-color: {COLORS['gray_200']};
        }}
    """,
    
    'success': f"""
        QPushButton {{
            background-color: {COLORS['success']};
            color: {COLORS['white']};
            border: none;
            border-radius: {RADIUS['lg']};
            padding: {SPACING['sm']} {SPACING['lg']};
            font-size: {TYPOGRAPHY['sizes']['base']};
            font-weight: {TYPOGRAPHY['weights']['medium']};
            font-family: {TYPOGRAPHY['font_family']};
            min-height: 40px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['success_hover']};
        }}
        QPushButton:disabled {{
            background-color: {COLORS['gray_300']};
            color: {COLORS['gray_500']};
        }}
    """,
    
    'small': f"""
        QPushButton {{
            background-color: {COLORS['gray_100']};
            color: {COLORS['gray_700']};
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['xs']} {SPACING['sm']};
            font-size: {TYPOGRAPHY['sizes']['sm']};
            font-weight: {TYPOGRAPHY['weights']['medium']};
            font-family: {TYPOGRAPHY['font_family']};
            min-height: 32px;
            max-height: 32px;
        }}
        QPushButton:hover {{
            background-color: {COLORS['gray_200']};
        }}
    """,
}

# ==================== 卡片样式模板 ====================
CARD_STYLES = {
    'default': f"""
        QWidget {{
            background-color: {COLORS['white']};
            border: 1px solid {COLORS['gray_200']};
            border-radius: {RADIUS['lg']};
        }}
    """,
    
    'platform_card': f"""
        QWidget {{
            background-color: {COLORS['white']};
            border: 1px solid {COLORS['gray_200']};
            border-radius: {RADIUS['lg']};
        }}
        QWidget:hover {{
            border-color: {COLORS['primary']};
            background-color: {COLORS['gray_50']};
        }}
    """,
    
    'elevated': f"""
        QWidget {{
            background-color: {COLORS['white']};
            border: 1px solid {COLORS['gray_200']};
            border-radius: {RADIUS['lg']};
        }}
    """,
}

# ==================== 输入框样式模板 ====================
INPUT_STYLES = {
    'default': f"""
        QLineEdit {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['sm']} {SPACING['md']};
            font-size: {TYPOGRAPHY['sizes']['base']};
            font-family: {TYPOGRAPHY['font_family']};
            background-color: {COLORS['white']};
            color: {COLORS['gray_800']};
            min-height: 40px;
        }}
        QLineEdit:focus {{
            border-color: {COLORS['primary']};
            outline: none;
        }}
        QLineEdit:hover {{
            border-color: {COLORS['gray_400']};
        }}
    """,
    
    'default_compact': f"""
        QLineEdit {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['xs']} {SPACING['sm']};
            font-size: {TYPOGRAPHY['sizes']['sm']};
            font-family: {TYPOGRAPHY['font_family']};
            background-color: {COLORS['white']};
            color: {COLORS['gray_800']};
            min-height: 32px;
        }}
        QLineEdit:focus {{
            border-color: {COLORS['primary']};
            outline: none;
        }}
        QLineEdit:hover {{
            border-color: {COLORS['gray_400']};
        }}
    """,
    
    'search': f"""
        QLineEdit {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['lg']};
            padding: {SPACING['sm']} {SPACING['lg']};
            font-size: {TYPOGRAPHY['sizes']['base']};
            font-family: {TYPOGRAPHY['font_family']};
            background-color: {COLORS['white']};
            color: {COLORS['gray_800']};
            min-height: 44px;
        }}
        QLineEdit:focus {{
            border-color: {COLORS['primary']};
            outline: none;
        }}
    """,
}

# ==================== 下拉框样式模板 ====================
COMBOBOX_STYLES = {
    'default': f"""
        QComboBox {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['sm']} {SPACING['md']};
            font-size: {TYPOGRAPHY['sizes']['base']};
            font-family: {TYPOGRAPHY['font_family']};
            background-color: {COLORS['white']};
            color: {COLORS['gray_800']};
            min-height: 40px;
        }}
        QComboBox:focus {{
            border-color: {COLORS['primary']};
        }}
        QComboBox:hover {{
            border-color: {COLORS['gray_400']};
        }}
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        QComboBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {COLORS['gray_500']};
            margin-right: 5px;
        }}
        QComboBox QAbstractItemView {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            background-color: {COLORS['white']};
            selection-background-color: {COLORS['primary_light']};
            selection-color: {COLORS['primary']};
        }}
    """,
    
    'default_compact': f"""
        QComboBox {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            padding: {SPACING['xs']} {SPACING['sm']};
            font-size: {TYPOGRAPHY['sizes']['sm']};
            font-family: {TYPOGRAPHY['font_family']};
            background-color: {COLORS['white']};
            color: {COLORS['gray_800']};
            min-height: 32px;
        }}
        QComboBox:focus {{
            border-color: {COLORS['primary']};
        }}
        QComboBox:hover {{
            border-color: {COLORS['gray_400']};
        }}
        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}
        QComboBox::down-arrow {{
            image: none;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
            border-top: 4px solid {COLORS['gray_500']};
            margin-right: 5px;
        }}
        QComboBox QAbstractItemView {{
            border: 1px solid {COLORS['gray_300']};
            border-radius: {RADIUS['md']};
            background-color: {COLORS['white']};
            selection-background-color: {COLORS['primary_light']};
            selection-color: {COLORS['primary']};
        }}
    """,
}

# ==================== 动画参数 ====================
ANIMATIONS = {
    'duration_fast': 150,
    'duration_normal': 250,
    'duration_slow': 350,
    'easing': 'ease-in-out',
}

# ==================== 平台图标颜色 ====================
PLATFORM_COLORS = {
    'openai': '#10A37F',
    'claude': '#D97706', 
    'gemini': '#4285F4',
    'baidu': '#2932E1',
    'alibaba': '#FF6A00',
    'xunfei': '#3F7EF3',
    'zhipu': '#6366F1',
    'siliconflow': '#3B82F6',
    'volcengine': '#F97316',
    'deepseek': '#8B5CF6',
    'custom': '#6B7280',
} 