#!/usr/bin/env python3
"""
测试分类筛选功能
"""
import sys
from PySide6.QtWidgets import QApplication
from model import PromptModel, Prompt
from main import PromptAssistantRedesigned

def create_test_data():
    """创建测试数据"""
    model = PromptModel("sqlite", "test_category_prompts.db")
    
    # 清除现有数据
    try:
        model.storage.cursor.execute("DELETE FROM prompts")
        model.storage.connection.commit()
    except:
        pass
    
    # 创建不同分类的测试提示词
    test_prompts = [
        # AI绘画分类
        Prompt(
            title="风景画生成提示词",
            content="创建一个美丽的山水风景画，包含青山绿水",
            category="AI绘画",
            tags=["绘画", "风景"],
            is_favorite=1
        ),
        Prompt(
            title="人物肖像提示词",
            content="生成一个优雅的女性肖像画",
            category="AI绘画",
            tags=["绘画", "人物"],
            is_favorite=0
        ),
        
        # 文案写作分类
        Prompt(
            title="营销文案提示词",
            content="为新产品写一篇吸引人的营销文案",
            category="文案写作",
            tags=["写作", "营销"],
            is_favorite=1
        ),
        Prompt(
            title="技术文档提示词",
            content="编写清晰的API使用文档",
            category="文案写作",
            tags=["写作", "技术"],
            is_favorite=0
        ),
        
        # 代码生成分类
        Prompt(
            title="Python函数生成",
            content="生成一个处理数据的Python函数",
            category="代码生成",
            tags=["编程", "Python"],
            is_favorite=1
        ),
        Prompt(
            title="JavaScript组件",
            content="创建一个React组件",
            category="代码生成",
            tags=["编程", "JavaScript"],
            is_favorite=0
        ),
        
        # 学习辅助分类
        Prompt(
            title="数学概念解释",
            content="解释微积分的基本概念",
            category="学习辅助",
            tags=["学习", "数学"],
            is_favorite=0
        ),
        
        # 创意设计分类
        Prompt(
            title="Logo设计提示词",
            content="设计一个现代简约风格的公司logo",
            category="创意设计",
            tags=["设计", "logo"],
            is_favorite=1
        )
    ]
    
    # 添加到数据库
    for prompt in test_prompts:
        model.add_prompt(prompt)
    
    print("分类筛选测试数据创建完成！")
    print(f"总共创建了 {len(test_prompts)} 个提示词")
    
    # 统计各分类数量
    categories = {}
    favorites = 0
    for prompt in test_prompts:
        if prompt.category not in categories:
            categories[prompt.category] = 0
        categories[prompt.category] += 1
        if prompt.is_favorite == 1:
            favorites += 1
    
    print("\n分类统计:")
    for category, count in categories.items():
        print(f"  {category}: {count} 个")
    print(f"\n收藏的提示词: {favorites} 个")
    
    return model

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试数据
    create_test_data()
    
    # 启动应用
    window = PromptAssistantRedesigned()
    window.show()
    
    print("\n=== 分类筛选功能测试说明 ===")
    print("1. 应用启动后会显示所有8个提示词")
    print("2. 点击工具栏中的分类筛选按钮（文件夹图标）")
    print("3. 分类筛选栏会显示/隐藏，包含以下分类按钮：")
    print("   - 全部 (默认选中)")
    print("   - AI绘画 (2个)")
    print("   - 文案写作 (2个)")
    print("   - 代码生成 (2个)")
    print("   - 学习辅助 (1个)")
    print("   - 创意设计 (1个)")
    print("4. 点击任意分类按钮可以筛选对应分类的提示词")
    print("5. 可以同时使用收藏筛选和分类筛选")
    print("6. 状态栏会显示当前筛选状态和数量信息")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()