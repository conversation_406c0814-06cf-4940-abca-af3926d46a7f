#!/usr/bin/env python3
"""
测试历史记录问题的重现脚本
"""

from model import PromptModel, Prompt
from datetime import datetime

print("🧪 历史记录问题测试")
print("=" * 50)

# 创建模型
model = PromptModel()

# 1. 创建提示词（版本1）
print("📝 步骤1：创建提示词（版本1）")
prompt = Prompt(
    title="测试提示词",
    content="版本1",
    category="测试",
    tags=["测试"],
    created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    updated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
)

prompt_id = model.add_prompt(prompt)
print(f"✅ 创建成功，ID={prompt_id}")

# 2. 查看历史记录
print("\n📋 步骤2：查看创建后的历史记录")
history = model.get_prompt_history(prompt_id)
print(f"历史记录数量: {len(history)}")
for i, h in enumerate(history):
    print(f"  版本{h.version}: {h.content} (创建时间: {h.created_at})")

# 3. 编辑提示词（版本2）
print("\n✏️ 步骤3：编辑提示词为版本2")
current_prompt = model.get_prompt(prompt_id)
current_prompt.content = "版本2"
current_prompt.updated_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
model.update_prompt(current_prompt)
print("✅ 编辑完成")

# 4. 再次查看历史记录
print("\n📋 步骤4：查看编辑后的历史记录")
history = model.get_prompt_history(prompt_id)
print(f"历史记录数量: {len(history)}")
for i, h in enumerate(history):
    print(f"  版本{h.version}: {h.content} (创建时间: {h.created_at})")

# 5. 查看当前提示词状态
print("\n📄 步骤5：查看当前提示词状态")
current = model.get_prompt(prompt_id)
print(f"当前内容: {current.content}")

print("\n🔍 问题分析:")
print("- 历史记录是否包含版本1？", any(h.content == "版本1" for h in history))
print("- 历史记录是否包含版本2？", any(h.content == "版本2" for h in history))
print("- 当前提示词内容:", current.content)

if len(history) > 0 and not any(h.content == current.content for h in history):
    print("🐛 问题确认：历史记录中不包含当前版本！")
else:
    print("✅ 历史记录看起来正常")

# 清理测试数据
print(f"\n🧹 清理测试数据...")
model.permanently_delete_prompt(prompt_id)
print("✅ 清理完成")

print("\n" + "=" * 50)
print("🏁 测试完成") 