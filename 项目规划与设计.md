# AI提示词收藏助手 项目规划与设计

---

## 一、项目简介

**项目名称**：AI提示词收藏助手  
**目标**：为用户提供一个本地化、易用的桌面应用，用于收藏、管理和检索各类AI提示词（文本、图片、视频等）。

---

## 二、主要功能模块

1. **用户界面（UI）与交互体验**
   - 主窗口布局（导航栏、提示词列表、详情/编辑区、状态栏）
   - 响应式设计，适配不同屏幕尺寸
   - 主题切换（暗色/亮色）
   - 批量操作与多选模式
   - 拖拽排序、快捷键支持
   - 详细界面设计草图（见后文5.7）
   - 媒体内容展示与交互（图片、视频）
   - 新手引导与帮助中心

2. **提示词管理**
   - 新增、编辑、删除提示词（支持软删除/回收站机制）
   - 支持多类型（文本、图片、视频）
   - 支持自定义标签、分组、系列、组合、收藏夹、描述、来源、用途
   - 支持批量导入/导出（JSON/CSV/Markdown）
   - 一键复制、批量操作（多选、批量编辑、批量添加标签、批量删除等）
   - 编辑历史记录与版本回溯
   - 使用历史追踪（时间、次数、场景等）
   - 自动保存与恢复草稿

3. **分类与标签系统**
   - 自定义分组/系列/组合/收藏夹/标签的增删改查
   - 多标签管理与批量归类、批量移动

4. **搜索与筛选系统**
   - 关键词搜索（标题、内容、标签、描述等）
   - 类型、标签、分组、系列、组合、收藏夹等多维筛选
   - 智能模糊搜索、热门推荐、搜索历史

5. **数据与安全**
   - 本地数据库（SQLite/JSON）存储
   - 数据自动备份与恢复
   - 数据加密（敏感信息）
   - 导入/导出兼容多格式
   - 回收站机制（软删除、自动清理、批量恢复/彻底删除）

6. **媒体内容管理**
   - 图片/视频等媒体文件统一管理与引用
   - 支持批量上传、去重、压缩、清理无用文件
   - 缩略图、预览、交互操作（缩放、复制、右键菜单等）
   - 媒体文件与提示词关联一致性维护

7. **高级与扩展功能**
   - 智能粘贴解析主流AI平台提示词格式
   - 插件/扩展机制，支持第三方开发
   - 本地API接口（RESTful/WebSocket）
   - 与主流AI平台联动（如OpenAI、Midjourney等）
   - 多语言支持

---

## 三、详细功能清单

### 1. 提示词管理
- 新增、编辑、删除提示词
- 支持多类型（文本、图片、视频）
- 支持自定义标签、分组、描述、来源、用途
- 支持批量导入/导出（JSON/CSV）
- 支持一键复制到剪贴板
- **支持批量操作**：多选、批量编辑、批量添加标签、批量删除
- **支持编辑历史记录**：每次编辑自动保存历史版本，可回溯、比较与恢复
- **支持使用历史追踪**：记录每条提示词的使用时间、次数、场景等
- **支持自动保存与恢复**：编辑时自动保存草稿，防止意外关闭导致数据丢失

### 2. 分类与标签系统
- 支持自定义分组/收藏夹/系列/组合
- 支持多标签管理
- 分类、系列、组合、标签、收藏夹可增删改查
- 支持批量移动、批量归类

### 3. 搜索与筛选
- 关键词搜索（标题、内容、标签、描述等）
- 类型筛选（文本/图片/视频）
- 标签筛选
- 分组/系列/组合/收藏夹筛选

### 4. 界面与体验
- 简洁直观的主界面
- 暗色/亮色主题切换
- 拖拽排序、批量操作
- 详细信息展示与编辑
- **导航栏**：
  - 首页（所有提示词/推荐/统计）
  - 分类/系列/组合/标签/收藏夹
  - 回收站（可恢复已删除提示词）
  - 设置（主题、数据管理、快捷键等）
  - 帮助（使用说明、常见问题、反馈）

### 5. 数据与安全
- 本地数据存储（SQLite/JSON）
- 数据自动备份
- 导入/导出功能
- **回收站机制**：删除的提示词先进入回收站，可恢复或彻底删除
- **自动保存与恢复**：编辑时自动保存草稿，异常关闭可恢复

### 6. 高级与扩展
- 与主流AI平台API集成，直接测试提示词
- 云同步/账号系统（可选）
- 多语言支持（可选）

---

## 四、数据库设计（以SQLite为例）

### 1. 表结构设计（补充多对多关系、历史、使用追踪、系列、组合、收藏夹等）

#### 1.1 prompts（提示词表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| title          | TEXT        | NOT NULL       | 标题           |
| content        | TEXT        | NOT NULL       | 提示词内容     |
| type           | TEXT        | NOT NULL, CHECK(type IN ('text','image','video')) | 类型（文本/图片/视频）|
| description    | TEXT        |                | 描述           |
| source         | TEXT        |                | 来源           |
| usage          | TEXT        |                | 用途           |
| is_deleted     | INTEGER     | DEFAULT 0      | 是否已删除（0正常，1回收站）|
| created_at     | DATETIME    | DEFAULT CURRENT_TIMESTAMP | 创建时间       |
| updated_at     | DATETIME    | DEFAULT CURRENT_TIMESTAMP | 更新时间       |

#### 1.2 prompt_history（提示词历史表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 对应提示词ID   |
| content        | TEXT        | NOT NULL       | 历史内容       |
| edited_at      | DATETIME    | DEFAULT CURRENT_TIMESTAMP | 编辑时间       |
| editor         | TEXT        |                | 编辑人（可选） |

#### 1.3 prompt_usage（提示词使用历史表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 对应提示词ID   |
| used_at        | DATETIME    | DEFAULT CURRENT_TIMESTAMP | 使用时间       |
| context        | TEXT        |                | 使用场景/备注  |

#### 1.4 groups（分组表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| name           | TEXT        | UNIQUE NOT NULL | 分组名         |

#### 1.5 series（系列表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| name           | TEXT        | UNIQUE NOT NULL | 系列名         |

#### 1.6 combos（组合表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| name           | TEXT        | UNIQUE NOT NULL | 组合名         |

#### 1.7 favorites（收藏夹表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| name           | TEXT        | UNIQUE NOT NULL | 收藏夹名       |

#### 1.8 tags（标签表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| id             | INTEGER     | PRIMARY KEY AUTOINCREMENT | 主键           |
| name           | TEXT        | UNIQUE NOT NULL | 标签名         |

#### 1.9 prompt_tags（提示词-标签关联表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 提示词ID       |
| tag_id         | INTEGER     | REFERENCES tags(id) ON DELETE CASCADE    | 标签ID         |
| PRIMARY KEY(prompt_id, tag_id) |              | 复合主键       |

#### 1.10 prompt_groups（提示词-分组关联表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 提示词ID       |
| group_id       | INTEGER     | REFERENCES groups(id) ON DELETE CASCADE  | 分组ID         |
| PRIMARY KEY(prompt_id, group_id) |            | 复合主键       |

#### 1.11 prompt_series（提示词-系列关联表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 提示词ID       |
| series_id      | INTEGER     | REFERENCES series(id) ON DELETE CASCADE  | 系列ID         |
| PRIMARY KEY(prompt_id, series_id) |            | 复合主键       |

#### 1.12 prompt_combos（提示词-组合关联表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 提示词ID       |
| combo_id       | INTEGER     | REFERENCES combos(id) ON DELETE CASCADE  | 组合ID         |
| PRIMARY KEY(prompt_id, combo_id) |            | 复合主键       |

#### 1.13 prompt_favorites（提示词-收藏夹关联表）
| 字段名         | 类型        | 约束           | 说明           |
| -------------- | ----------- | -------------- | -------------- |
| prompt_id      | INTEGER     | REFERENCES prompts(id) ON DELETE CASCADE | 提示词ID       |
| favorite_id    | INTEGER     | REFERENCES favorites(id) ON DELETE CASCADE | 收藏夹ID     |
| PRIMARY KEY(prompt_id, favorite_id) |          | 复合主键       |

#### 2. 示例SQL建表语句（补全所有表）

```sql
CREATE TABLE prompts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    type TEXT NOT NULL CHECK(type IN ('text','image','video')),
    description TEXT,
    source TEXT,
    usage TEXT,
    is_deleted INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE prompt_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    edited_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    editor TEXT
);

CREATE TABLE prompt_usage (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    used_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    context TEXT
);

CREATE TABLE groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL
);

CREATE TABLE series (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL
);

CREATE TABLE combos (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL
);

CREATE TABLE favorites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL
);

CREATE TABLE tags (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL
);

CREATE TABLE prompt_tags (
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    tag_id INTEGER REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (prompt_id, tag_id)
);

CREATE TABLE prompt_groups (
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    group_id INTEGER REFERENCES groups(id) ON DELETE CASCADE,
    PRIMARY KEY (prompt_id, group_id)
);

CREATE TABLE prompt_series (
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    series_id INTEGER REFERENCES series(id) ON DELETE CASCADE,
    PRIMARY KEY (prompt_id, series_id)
);

CREATE TABLE prompt_combos (
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    combo_id INTEGER REFERENCES combos(id) ON DELETE CASCADE,
    PRIMARY KEY (prompt_id, combo_id)
);

CREATE TABLE prompt_favorites (
    prompt_id INTEGER REFERENCES prompts(id) ON DELETE CASCADE,
    favorite_id INTEGER REFERENCES favorites(id) ON DELETE CASCADE,
    PRIMARY KEY (prompt_id, favorite_id)
);

-- 索引示例
CREATE INDEX idx_prompts_type ON prompts(type);
CREATE INDEX idx_prompt_tags_tag_id ON prompt_tags(tag_id);
CREATE INDEX idx_prompt_groups_group_id ON prompt_groups(group_id);
CREATE INDEX idx_prompt_series_series_id ON prompt_series(series_id);
CREATE INDEX idx_prompt_combos_combo_id ON prompt_combos(combo_id);
CREATE INDEX idx_prompt_favorites_favorite_id ON prompt_favorites(favorite_id);
```

---

## 五、用户界面

### 5.1 基本规范
- 风格：简约、美观、现代化
- 主题：支持深色和浅色主题，默认为浅色主题
- 默认窗口尺寸：460×800像素
- 响应式设计：适应不同屏幕尺寸
- 快捷键支持
- 批量操作支持
- 组件样式统一：所有按钮、输入框、选择器等组件保持统一设计风格

### 5.2 布局设计
1. **左侧导航栏**
   - 尺寸：60×800像素
   - 内容：SVG图标（支持默认、悬停、激活三种状态）
2. **自定义标题栏**
   - 位置：右侧顶部
   - 按钮：软件置顶、侧边隐藏、最小化、关闭（均为SVG图标）
   - 特殊功能：侧边隐藏功能（类似QQ贴边隐藏，鼠标移过显示）
3. **主内容区**
   - 位置：右侧中部
   - 内容：根据导航切换显示不同页面
4. **状态栏**
   - 位置：右侧底部
   - 显示：当前状态、统计信息等

### 5.3 搜索系统
- 智能搜索：支持模糊搜索
- 高级筛选：按标签、类型、评分等
- 热门搜索推荐
- 搜索历史记录

### 5.4 组件设计原则
- 按钮：统一样式设计，美观、现代化，尽可能使用SVG图标
  - 主要按钮：突出显示（无背景色，使用边框或阴影）
  - 次要按钮：轻量显示（无背景色，使用细边框）
  - 功能按钮：使用SVG图标，简洁明了
- 文字：所有文字不使用背景色（除非特别指明的区域）
- 标题：不显示背景色
- 提示词展示：默认卡片式，支持切换列表式

### 5.5 媒体内容展示
1. **卡片式展示**
   - 显示图片缩略图/视频封面
   - 视频无封面时显示有画面的一帧
2. **图片交互**
   - 点击缩略图：展示大图，跟随鼠标移动
   - 鼠标滚轮：缩放大图
   - 点击大图：关闭大图
   - 右键菜单：复制图片等功能
3. **视频交互**
   - 点击缩略图：循环播放，跟随鼠标移动
   - 空格键：暂停/播放
   - 点击播放窗：关闭视频
4. **缩略图浏览**
   - 鼠标滚动切换下一张缩略图

### 5.6 性能优化设计
- 虚拟滚动（处理大量提示词）
- 媒体文件按需加载
- 搜索索引优化
- 本地缓存策略
- 后台数据处理

### 5.7 界面设计草图

#### 草图一: 主窗口

```
+------------------------------------------------------+
| [左侧导航栏] | [ 右 侧 区 域 ]                        |
|--------------|----------------------------------------|
|              | [ 主内容区 - 顶部控制区 ]              |
| [Icon: 全部] |                                        |
|              | [搜索框....................][新建提示词+] |
|              |                                        |
|              | [按钮: ⭐️ 收藏]  [按钮: 📂 分类 ▼]     |
|              | [按钮: #️⃣ 标签 ▼]                      |
|              |                                        |
| [Icon: 设置] | (点击"分类"后展开) -> [下拉筛选框: 选择分类] |
|              |                                        |
|              | (点击"标签"后展开) -> [多选框: [x]标签A [ ]标签B] |
|              |----------------------------------------|
| [Icon: 帮助] | [ 主内容区 - 提示词列表 (可滚动) ]       |
|              |                                        |
|              | [ 提示词卡片 1 (带图片) ]                |
|              |                                        |
|              | [ 提示词卡片 2 (纯文本) ]                |
|              |                                        |
|              | [ ... ]                                |
+------------------------------------------------------+
```

#### 草图二: 提示词卡片

**状态A: 纯文本提示词卡片**
```
+--------------------------------------------------+
| [📂 分类: 角色设计]      [⭐️][📄][✏️][🕒][🗑️] |
|--------------------------------------------------|
| [标题] 一个戴着VR眼镜的赛博朋克猫咪武士        |
|--------------------------------------------------|
| [内容预览] a cyberpunk cat samurai, wearing a    |
| virtual reality headset, detailed armor, ...     |
|--------------------------------------------------|
| [#科幻] [#动物] [#赛博朋克] [#武士] [#猫]       |
| [#概念艺术] [#高细节]                            |
+--------------------------------------------------+
```

**状态B: 带图片/视频的提示词卡片 (高度更高)**
```
+--------------------------------------------------+
| [📂 分类: 场景渲染]      [⭐️][📄][✏️][🕒][🗑️] |
|--------------------------------------------------|
| [标题] 雨夜的东京涉谷街头                        |
|--------------------------------------------------|
| +-----------+ [内容预览] a rainy night at        |
| |           | Shibuya crossing in Tokyo, neon    |
| |  缩略图   | signs reflecting on wet pavement,  |
| |           | cinematic lighting, ultra real...  |
| +-----------+                                    |
|--------------------------------------------------|
| [#场景] [#城市] [#雨夜] [#东京] [#写实]         |
+--------------------------------------------------+
```

#### 草图三: 添加/编辑提示词界面
```
+--------------------------------------------------+
| [ 窗口标题: "创建新提示词" ]                     |
|--------------------------------------------------|
|                                                  |
|  分类                                            |
| [ 下拉选择框: 选择一个分类 ]                     |
|                                                  |
|  标题                                            |
| [ 文本输入框: 请输入标题 ]                       |
|                                                  |
|  主提示词 * |
| [ 多行文本输入框: 在这里输入你的主提示词... ]      |
|                                                  |
|  标签                                            |
| [ 标签输入框: 选择或创建标签，可多个 ]           |
|                                                  |
|  媒体文件                                        |
| [ 媒体上传区域 (可拖拽文件或点击上传) ]          |
| [ (上传后在此显示缩略图预览) ]                   |
|                                                  |
|--------------------------------------------------|
|                                                  |
| [+ 添加负面提示词]  [+ 添加模型参数]              |
|                                                  |
|--------------------------------------------------|
| [ 右下角: [取消] [保存] 按钮 ]                  |
+--------------------------------------------------+
```

**说明：**
- 默认只展示分类、标题、主提示词、标签、媒体上传五个核心模块。
- 负面提示词和模型参数通过点击按钮按需添加。
- 主提示词输入框支持智能粘贴解析，自动分离主/负面提示词和参数。

---

## 六、提示词创建与编辑

### 6.1 文本提示词
- 主提示词文本输入（默认显示）
- 可选添加负面提示词和模型参数设置

### 6.2 图片/视频提示词
1. **基础文本内容**
   - 主提示词文本输入（默认显示）
   - 负面提示词和模型参数可选添加
2. **媒体内容**
   - 支持添加多张图片/视频
   - 完整显示上传媒体预览

---

## 七、媒体内容存储方案

### 7.1 基础文本内容存储
- 主提示词文本
- 负面提示词文本（可选）
- 模型参数设置（可选，如采样方法、步数、尺寸等）

### 7.2 媒体内容存储
- 参考图片：Base64编码（内嵌数据库）+ 文件路径（本地存储）+ MD5校验值
- 生成结果：存储缩略图（预览用）和原始文件路径
- 媒体元数据（分辨率、格式、时长等）

### 7.3 导出/分享策略
- JSON格式：完整数据包含图片Base64编码，确保跨平台完整性
- Markdown格式：生成包含本地文件引用的MD文档和资源文件夹
- CSV格式：基础文本数据，媒体内容使用引用
- 分享链接：使用UUID生成唯一提示词ID

---

## 八、项目结构建议

基于PySide为核心的技术栈，推荐如下项目目录结构，兼顾模块化、可扩展性与后期维护：

```
aipromptstudio/
├── main.py                  # 程序入口，初始化应用
├── app/                     # 主应用包
│   ├── __init__.py
│   ├── core/                # 核心逻辑（数据管理、业务逻辑等）
│   │   ├── database.py      # 数据库操作与ORM
│   │   ├── models.py        # 数据模型
│   │   ├── settings.py      # 配置与全局设置
│   │   └── backup.py        # 自动备份与恢复逻辑
│   ├── ui/                  # UI相关（PySide界面、QSS样式等）
│   │   ├── main_window.py   # 主窗口
│   │   ├── widgets/         # 自定义控件
│   │   ├── dialogs/         # 各类弹窗
│   │   ├── qss/             # QSS样式表
│   │   └── resources.qrc    # Qt资源文件（图标、SVG等）
│   ├── media/               # 媒体处理（图片、视频、缩略图等）
│   │   ├── image_utils.py
│   │   └── video_utils.py
│   ├── plugins/             # 插件系统（第三方扩展）
│   │   └── ...
│   ├── api/                 # 本地API接口（RESTful、WebSocket等）
│   │   ├── __init__.py
│   │   └── routes.py
│   ├── utils/               # 工具函数与通用模块
│   │   ├── file_utils.py
│   │   ├── encryption.py
│   │   └── ...
│   └── i18n/                # 国际化（多语言支持）
│       └── ...
├── tests/                   # 自动化测试（单元、集成等）
│   └── ...
├── resources/               # 静态资源（图标、图片、SVG等）
├── docs/                    # 项目文档、开发说明
├── requirements.txt         # 依赖列表
├── README.md                # 项目说明
└── setup.py                 # 安装与打包脚本（可选）
```

**结构说明：**
- `main.py`：应用启动入口，负责初始化主窗口和全局配置。
- `app/core/`：核心业务逻辑，包括数据库、数据模型、配置、备份等。
- `app/ui/`：所有PySide界面代码、QSS样式、资源文件，支持UI与逻辑分离。
- `app/media/`：图片、视频等媒体处理相关代码。
- `app/plugins/`：插件扩展目录，支持热加载和第三方开发。
- `app/api/`：本地API接口实现，便于自动化和外部集成。
- `app/utils/`：通用工具函数，如文件、加密、格式转换等。
- `app/i18n/`：多语言支持文件。
- `tests/`：自动化测试用例，保障核心功能稳定。
- `resources/`：静态资源文件夹，集中管理图标、SVG等。
- `docs/`：开发文档、接口说明、用户手册等。
- `requirements.txt`：Python依赖包清单。
- `README.md`：项目简介与使用说明。
- `setup.py`：可选，便于打包和分发。

如需进一步细化某一子模块的结构或内容，请随时告知！

---

## 九、更多建议与扩展设计

### 9.1 用户体验与易用性
- **新手引导与帮助**：首次启动提供功能高亮、引导页或动画，帮助新用户快速上手。帮助中心内置FAQ、快捷键说明、常见问题视频等。
- **操作反馈**：所有重要操作（保存、删除、导入、导出、批量操作）均有明显反馈（如Toast提示、进度条、撤销/恢复入口）。
- **多语言支持**：预留国际化（i18n）接口，便于后续支持多语言。

### 9.2 数据安全与隐私
- **自动备份与恢复**：
  - 本地自动保存草稿，异常关闭可恢复。
  - 定期自动备份到指定目录或云端（如OneDrive、Google Drive），可设置备份周期和最大备份数。
  - 支持一键恢复历史备份。
- **数据加密**：敏感内容（如API Key、私有提示词）本地加密存储。
- **导入导出兼容性**：导入支持多种格式（如旧版JSON、主流AI平台格式），导出可自定义字段。

#### 自动备份实现方案
- 支持设置自动备份周期（如每10分钟、每次重大变更后）。
- 备份文件命名含时间戳，保留最近N份，超出自动清理。
- 支持本地和云端（如WebDAV、OneDrive、Google Drive）多目标备份。
- 提供备份管理界面，可手动恢复、删除、下载备份。

### 9.3 扩展性与生态
- **插件/扩展机制**：
  - 预留插件接口，允许第三方开发者为应用添加新功能。
  - 插件可扩展：导出格式、AI平台直连、批量处理脚本、UI组件等。
  - 插件采用独立目录（如plugins/），每个插件为独立Python包，定义标准入口（如register_plugin(app)）。
  - 插件可通过配置界面启用/禁用，支持热加载。
  - 提供插件开发文档和示例。

#### 插件机制设计示例
```python
# 插件目录结构
plugins/
  ├── my_export_plugin/
  │     ├── __init__.py
  │     └── plugin.py

# 插件入口示例
# plugin.py

def register_plugin(app):
    app.register_export_format('my_format', export_func)
    # 可注册菜单、快捷键、UI组件等
```

- **API接口**：
  - 提供本地RESTful API或命令行接口，便于自动化和第三方集成。
  - 支持基本CRUD、批量操作、导入导出、搜索、标签管理等。
  - 支持本地token鉴权，保障安全。
  - 可选WebSocket接口，支持实时通知（如数据变更、备份完成等）。

#### API接口方案示例
- GET /api/prompts?search=xxx&tag=xxx
- POST /api/prompts （新增提示词）
- PUT /api/prompts/{id} （编辑提示词）
- DELETE /api/prompts/{id} （删除提示词）
- POST /api/batch （批量操作）
- GET /api/backup/list （备份列表）
- POST /api/backup/restore （恢复备份）
- ...

- **与主流AI平台联动**：
  - 支持一键发送提示词到OpenAI、Midjourney、Stable Diffusion等平台，或直接调用API测试效果。
  - 可通过插件扩展更多平台。

### 9.4 媒体与性能
- **媒体管理优化**：支持批量上传、批量删除、自动去重、智能压缩（如图片自动缩略、视频转码）。
- **大数据量优化**：分页、虚拟滚动、索引优化，保证流畅体验。
- **缓存与离线支持**：本地缓存机制，断网下可用。

### 9.5 细节与美观
- **主题与自定义**：支持自定义配色、字体、卡片风格。
- **快捷键与效率**：支持自定义快捷键、命令面板式操作。
- **可访问性**：关注色弱、视障用户体验，保证对比度、键盘操作、语音辅助。

### 9.6 未来发展与社区
- **社区模板与资源库**：支持导入/导出社区模板，内置热门提示词库。
- **用户反馈与更新机制**：内置反馈入口，自动检测更新或一键升级。

### 9.7 开发与维护建议
- **模块化开发**：前后端、UI组件、数据层、媒体处理等分层清晰。
- **自动化测试**：单元测试、集成测试，保证核心功能稳定。
- **文档与开源**：保持良好开发文档，便于新成员加入和社区贡献。

---

## 十、技术栈选型与论证 (基于Python)

项目开发语言确定为Python，UI框架初步选定为PySide。本章节旨在对Python生态内的主流GUI开发方案进行综合评估，以验证并最终确认PySide作为本项目技术栈的合理性与最优性。评估将严格围绕项目核心目标——**本地化**（高性能、原生体验）和**易用**（交互流畅、界面美观现代化）。

### 10.1 Python GUI方案对比

| 技术方案 | 开发难度 | 性能表现 | 打包大小 | 跨平台一致性 | UI现代化与定制能力 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **PySide / PyQt** | **中等**。需学习庞大的Qt框架API和其核心的“信号与槽”机制。可使用Qt Designer可视化设计UI，降低布局难度。 | **高**。基于性能卓越的C++ Qt库，应用响应速度快，能轻松处理复杂界面和大量数据，是原生性能的代表。 | **中到大**。需打包Python解释器及庞大的Qt核心库，最终制品体积较大。 | **极高**。Qt是顶级的跨平台框架，能确保应用在Windows, macOS, Linux上拥有一致的外观和行为。 | **高**。可通过QSS（Qt Style Sheets）进行深度定制，但要实现文档中要求的现代Web感UI，需要投入较高的学习和开发成本。 |
| **Eel (Web前端方案)** | **低**。后端是纯Python，前端是标准Web技术（HTML/CSS/JS）。非常适合熟悉Web开发的Python工程师，上手极快。 | **中等**。后端Python性能取决于代码质量。UI性能依赖于系统原生的WebView，非原生渲染，复杂动效可能不如PySide流畅。 | **小到中**。打包体积相对PySide更小，因为它不包含庞大的UI渲染库。 | **高**（UI层面）。Web界面在所有平台的WebView中表现高度一致。窗口边框等为原生样式。 | **极高**。可直接利用所有现代前端框架（Vue/React）、CSS库和JS动效库，是实现现代化UI最简单、最灵活的途径。 |
| **Flet** | **低到中**。采用类似Flutter的声明式UI写法，Python开发者无需掌握Web技术即可构建界面，学习曲线平缓。 | **良好**。其底层使用性能优异的Flutter进行渲染。 | **中到大**。需要打包Python及Flutter渲染引擎。 | **极高**。Flutter自绘UI，能做到像素级完美的跨平台一致性。 | **高**。自带Material Design风格组件，易于构建外观现代的应用。但定制深度和生态丰富度尚不及Web前端。 |
| **Tkinter** | **低**。Python标准库，无需安装，API简单。是Python GUI入门的首选。 | **中等**。适合简单工具，当UI逻辑变得复杂时，性能和响应速度会成为瓶颈。 | **小**。只需打包Python解释器，是所有方案中体积最小的。 | **低**。在不同操作系统上会渲染成原生控件，导致外观风格差异巨大，难以统一。 | **低**。默认控件外观老旧，极难实现现代化设计，与本项目“美观、现代化”的目标严重不符。 |

### 10.2 选型决策与理由

综合以上分析，并结合您已有的倾向，我们正式确认并推荐 **PySide** 作为本项目的开发框架。

**最终选择：PySide**

**陈述理由：**

1.  **最佳的“本地化”体验**：PySide在性能上是纯Python方案中的佼佼者。它基于C++ Qt构建，能提供最接近原生应用的响应速度和稳定性。对于一个需要流畅处理大量提示词（包括媒体文件）的本地管理工具来说，这种高性能表现至关重要，它完美契合了“本地化”的核心诉求。

2.  **功能强大且成熟稳定**：Qt框架身经百战，功能库包罗万象，从基础控件、多媒体处理到复杂的图形视图，应有尽有。这意味着在未来进行功能扩展（如集成AI平台API、高级媒体处理）时，PySide都能提供稳定、可靠的底层支持，无需担心框架能力不足。

3.  **高度的专业性与可控性**：选择PySide代表选择了一条专业、严谨的桌面应用开发路线。虽然UI定制（QSS）比Web前端有更陡峭的曲线，但它提供了对控件外观和行为的深度控制能力。对于追求极致细节和专业级品质的应用来说，这种可控性是巨大优势。投入精力精通QSS后，完全可以实现设计文档中规划的现代化界面。

**需要注意的权衡：**

选择PySide也意味着我们需要直面其最大的挑战：**UI的现代化开发成本**。

与Eel方案相比，Eel能用更低的成本、更快的速度构建出华丽的UI。而PySide要达到同样效果，则需要在QSS样式表和控件自定义上投入显著的开发时间。

**最终结论：**

**PySide是一个以性能换取部分UI开发便利性的选择，这完全符合一个专业级本地工具的定位。** 它将确保我们的“AI提示词收藏助手”拥有一个坚如磐石的性能核心和稳定表现，这对于用户长期、重度的使用至关重要。

因此，我们确认**PySide**为最终技术栈。项目后续的重点之一，将是投入资源进行QSS的学习和实践，以确保在发挥PySide强大性能的同时，也能完美实现项目规划中的现代化、易用的用户界面。

---

## 十一、核心业务逻辑流程定义

### 11.1 智能粘贴解析（功能规格说明）

**目标**：自动识别并解析主流AI平台的提示词格式，将主提示词、负面提示词、模型参数等自动分离。

**支持平台与格式示例：**
- **Midjourney**：如`a cat --ar 16:9 --v 5 --style raw`，参数以`--`开头。
- **Stable Diffusion WebUI**：如`Prompt: ... Negative prompt: ... Steps: 20, Sampler: Euler a, CFG scale: 7, Seed: 123456`。

**解析规则：**
1. **主提示词与负面提示词分割**：
   - 关键词分割：如遇到`Negative prompt:`、`负面提示词:`等，前为主提示词，后为负面提示词。
   - 分隔符分割：如`|`、`；`等（可配置）。
2. **参数提取**：
   - Midjourney风格：以`--`开头的为参数，按空格分割，支持`--ar 16:9`、`--v 5`等。
   - Stable Diffusion风格：`参数名: 值`，如`Steps: 20`，用逗号或换行分隔。
3. **粘贴时自动识别**：
   - 监听粘贴事件，自动检测上述关键词或格式。
   - 解析后自动填充到主提示词、负面提示词、参数输入框。
4. **可扩展性**：
   - 解析规则可配置，便于后续支持新平台。

**伪代码流程：**
```
if "Negative prompt:" in text:
    main, negative = text.split("Negative prompt:", 1)
    # 继续提取参数
elif "--" in text:
    # Midjourney风格，按空格分割，提取--参数
    ...
# 其他规则
```

### 11.2 回收站机制

**行为定义：**
- 删除提示词时，实际为“软删除”，仅将`is_deleted`字段设为1，数据仍保留。
- 回收站页面仅显示`is_deleted=1`的项目。
- **自动清理**：回收站内项目保留30天，超期自动彻底删除。
- **彻底删除**：用户手动“清空回收站”或对单条执行“彻底删除”时，需二次确认。
- **恢复操作**：
  - 恢复后`is_deleted`设为0。
  - 恢复时，提示词自动回到原有的分组/系列/组合/收藏夹（通过多对多关联表恢复，无需额外操作）。
- **批量操作**：支持批量恢复、批量彻底删除。

### 11.3 媒体文件管理

**存储位置与路径管理：**
- 所有媒体文件（图片、视频等）统一存储在应用数据目录下的`media/`文件夹。
- 支持用户自定义媒体根目录，但推荐使用默认的相对路径（如`media/xxx.png`），便于迁移和备份。
- 数据库中仅存储相对路径（如`media/xxx.png`），不存绝对路径。
- 迁移或备份时，只需整体复制数据目录即可。
- 支持媒体文件的批量导入、导出、去重、清理无用文件。

**媒体引用与一致性：**
- 删除提示词时，若其关联的媒体文件无其他提示词引用，可自动清理。
- 支持媒体文件的MD5校验，防止重复存储。


---

## 十二、用户体验（UX）具体化

### 12.1 首次启动流程（Onboarding）

**用户故事：**
- 用户第一次打开“AI提示词收藏助手”，看到一个简洁的欢迎界面。
- 欢迎界面包含应用Logo、简要介绍、主要功能亮点。
- 下方有“开始使用”按钮，点击后进入引导流程。

**引导流程建议：**
1. **欢迎页**  
   - 展示应用Logo、名称、一句slogan（如“高效管理你的AI提示词”）。
   - “开始使用”按钮。

2. **新手引导（分步高亮/遮罩）**  
   - 第一步：高亮左侧导航栏，提示“这里可以切换分类、标签、回收站等”。
   - 第二步：高亮“新建提示词”按钮，提示“点击这里添加你的第一个提示词”。
   - 第三步：弹窗引导用户“创建第一个分类”或“导入示例提示词库”（可选）。
   - 第四步：提示用户“设置数据备份路径”，可跳过或立即设置。

3. **示例数据导入（可选）**  
   - 提供“导入示例数据”按钮，帮助用户快速体验功能。
   - 导入后，主界面自动展示示例提示词和分类。

4. **引导结束**  
   - 显示“恭喜完成初始设置，开始你的AI提示词管理之旅！”  
   - 自动进入主界面。

**细节补充：**
- 引导流程可随时跳过，后续可在“帮助”菜单中重新查看。
- 首次启动状态通过本地配置文件记录，非首次启动则直接进入主界面。

---

### 12.2 响应式布局行为

**具体规则：**

- **窗口宽度 ≥ 1024px（桌面常规）**
  - 左侧导航栏全展开，显示图标+文字。
  - 主内容区为多列网格卡片布局（如3-4列）。
  - 右侧详情/编辑区可并排显示。

- **窗口宽度 768px ~ 1023px（中等宽度）**
  - 左侧导航栏收缩为仅图标，鼠标悬浮时自动展开显示文字。
  - 主内容区为2列网格卡片布局。
  - 详情/编辑区以弹窗或下拉方式显示。

- **窗口宽度 < 768px（窄屏/小窗口/平板）**
  - 左侧导航栏默认收起，仅显示一排图标，鼠标悬浮或点击时浮出完整导航。
  - 主内容区卡片列表切换为单列纵向排列，卡片宽度自适应。
  - 顶部操作区（如搜索框、新建按钮）自动收缩为下拉菜单或浮动按钮。
  - 底部状态栏自动隐藏，仅在需要时浮现。
  - 弹窗、对话框自适应为全屏显示。

**伪代码示例：**
- `if window.width < 768:`
    - `navBar.collapse()`
    - `cardList.setLayout('singleColumn')`
    - `showFloatingActionButton()`
- `elif window.width < 1024:`
    - `navBar.iconOnly()`
    - `cardList.setLayout('twoColumns')`
- `else:`
    - `navBar.expand()`
    - `cardList.setLayout('multiColumns')`

---

### 12.3 批量操作的界面流程

**用户故事：**
- 用户想要批量删除、移动或添加标签给多个提示词。

**具体流程：**
1. **进入多选/批量模式**
   - 方式一：在提示词卡片右上角点击“复选框”图标，卡片进入可多选状态。
   - 方式二：长按某一张卡片（适用于触控/平板），自动进入多选模式。
   - 方式三：点击主界面顶部“批量操作”按钮，所有卡片出现复选框。

2. **多选卡片**
   - 用户点击多个卡片的复选框，选中目标提示词。
   - 已选卡片高亮显示，右上角显示勾选标记。

3. **批量操作栏浮现**
   - 底部自动浮现批量操作栏（悬浮/吸底），显示已选数量和可用操作按钮：
     - “批量删除”
     - “批量移动到分类”
     - “批量添加标签”
     - “批量导出”
     - “取消多选”
   - 操作栏可随时收起/展开。

4. **执行批量操作**
   - 用户点击相应按钮，弹出对应操作对话框（如选择目标分类、输入标签等）。
   - 操作完成后，自动退出多选模式，界面恢复正常。

5. **退出多选模式**
   - 点击“取消多选”按钮，或点击空白区域，或完成操作后自动退出。

**界面细节：**
- 进入多选模式时，顶部可显示“已选X项”提示。
- 支持全选/反选操作。
- 批量操作栏风格与整体UI一致，按钮图标清晰，操作有动画反馈。
- 支持快捷键（如Ctrl+A全选，Delete批量删除）。
