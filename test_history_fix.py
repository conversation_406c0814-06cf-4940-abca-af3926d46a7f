#!/usr/bin/env python3
"""
验证历史记录修复的测试脚本
"""

from model import PromptModel, Prompt
from datetime import datetime

print("🧪 历史记录修复验证测试")
print("=" * 50)

model = PromptModel()

# 创建初始提示词
print("📝 步骤1：创建提示词（版本1）")
prompt = Prompt(
    title="修复测试",
    content="这是版本1",
    category="测试",
    created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    updated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
)

prompt_id = model.add_prompt(prompt)
print(f"✅ 创建成功，ID={prompt_id}")

# 检查初始状态
print("\n📋 步骤2：点击历史记录按钮")
history = model.get_prompt_history(prompt_id)
current = model.get_prompt(prompt_id)
print(f"当前内容: '{current.content}'")
print("历史记录:")
for h in history:
    print(f"  版本{h.version}: '{h.content}'")

current_in_history = any(h.content == current.content for h in history)
print(f"✅ 当前版本在历史记录中: {current_in_history}")

# 编辑提示词
print("\n✏️ 步骤3：编辑提示词（版本1 → 版本2）")
current.content = "这是版本2"
current.updated_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
model.update_prompt(current)
print("✅ 编辑保存完成")

# 检查编辑后状态
print("\n📋 步骤4：再次点击历史记录按钮")
history_after = model.get_prompt_history(prompt_id)
current_after = model.get_prompt(prompt_id)
print(f"当前内容: '{current_after.content}'")
print("历史记录:")
for h in history_after:
    print(f"  版本{h.version}: '{h.content}'")

# 关键验证
print("\n🔍 关键验证:")
has_version1 = any(h.content == "这是版本1" for h in history_after)
has_version2 = any(h.content == "这是版本2" for h in history_after)
current_is_version2 = current_after.content == "这是版本2"

print(f"1. 历史记录包含版本1: {has_version1}")
print(f"2. 历史记录包含版本2: {has_version2}")
print(f"3. 当前是版本2: {current_is_version2}")

# 期望行为验证
if has_version1 and not has_version2 and current_is_version2:
    print("✅ 修复成功！历史记录逻辑正确：")
    print("   - 历史记录保存了旧版本（版本1）")
    print("   - 当前版本是新版本（版本2）")
    print("   - 历史记录不重复保存当前版本")
else:
    print("❌ 修复可能有问题")

# 再次编辑测试
print("\n✏️ 步骤5：再次编辑（版本2 → 版本3）")
current_after.content = "这是版本3"
current_after.updated_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
model.update_prompt(current_after)

# 最终验证
print("\n📋 步骤6：最终历史记录状态")
final_history = model.get_prompt_history(prompt_id)
final_current = model.get_prompt(prompt_id)
print(f"当前内容: '{final_current.content}'")
print("历史记录:")
for h in final_history:
    print(f"  版本{h.version}: '{h.content}'")

print("\n🎯 最终验证:")
has_v1 = any(h.content == "这是版本1" for h in final_history)
has_v2 = any(h.content == "这是版本2" for h in final_history)
has_v3 = any(h.content == "这是版本3" for h in final_history)
current_is_v3 = final_current.content == "这是版本3"

print(f"历史记录有版本1: {has_v1}")
print(f"历史记录有版本2: {has_v2}")
print(f"历史记录有版本3: {has_v3}")
print(f"当前是版本3: {current_is_v3}")

if has_v1 and has_v2 and not has_v3 and current_is_v3:
    print("🎉 完美！历史记录功能完全修复：")
    print("   - 所有旧版本都在历史记录中")
    print("   - 当前版本是最新的，不在历史记录中")
    print("   - 用户可以看到完整的版本历史")
else:
    print("⚠️ 可能还需要进一步调整")

# 清理
print(f"\n🧹 清理测试数据...")
model.permanently_delete_prompt(prompt_id)
print("✅ 清理完成")

print("\n" + "=" * 50)
print("🏁 测试完成") 