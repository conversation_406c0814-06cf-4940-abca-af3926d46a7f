#!/usr/bin/env python3
"""
排序按钮组件模块
从main.py提取的SortButton组件
"""
from PySide6.QtWidgets import QPushButton, QMenu, QApplication
from PySide6.QtCore import Qt, Signal, QSettings, QSize, QPoint
from PySide6.QtGui import QCursor, QIcon, QPixmap, QPainter
from PySide6.QtSvg import QSvgRenderer

from components.button_factory import ButtonFactory
from components.style_manager import StyleManager


class SortButton(QPushButton):
    """排序按钮 - 带下拉菜单"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_content_area = parent
        self.setFixedSize(32, 28)
        
        # 定义排序图标
        self.sort_icons = {
            'updated_desc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 4H16M3 8H12M3 12H9M13 12L17 8M17 8L21 12M17 8V20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 更新时间降序（最新的在前）
            'updated_asc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 4H16M3 8H12M3 12H9M13 20L17 16M17 16L21 20M17 16V4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 更新时间升序（最旧的在前）
            'created_desc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 10H13M21 6H13M21 14H13M21 18H13M5 10V18M5 18L9 14M5 18L1 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 创建时间降序（最新的在前）
            'created_asc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 10H13M21 6H13M21 14H13M21 18H13M5 14V6M5 6L9 10M5 6L1 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 创建时间升序（最旧的在前）
            'title_asc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 5L14 19M14 5L18 9M14 5L10 9M5 15V9M5 9L3 11M5 9L7 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 标题升序（A-Z）
            'title_desc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 19L14 5M14 19L18 15M14 19L10 15M5 9V15M5 15L3 13M5 15L7 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 标题降序（Z-A）
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 20H8M8 20H12M8 20V4M16 12H12M16 12H20M16 12V4M16 12V20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''   # 按类别
        }
        
        # 排序方式和提示文字
        self.sort_tooltips = {
            'updated_desc': '按更新时间（最新的在前）',
            'updated_asc': '按更新时间（最旧的在前）',
            'created_desc': '按创建时间（最新的在前）',
            'created_asc': '按创建时间（最旧的在前）',
            'title_asc': '按标题排序（A-Z）',
            'title_desc': '按标题排序（Z-A）',
            'category': '按类别排序'
        }
        
        # 加载设置中的排序方式，默认为'updated_desc'
        settings = QSettings("PromptAssistant", "AppSettings")
        self.current_sort = settings.value("sort_method", "updated_desc")
        
        # 设置样式和图标
        self.update_style()
        self.update_icon()
        
        # 设置工具提示
        self.setToolTip(self.sort_tooltips[self.current_sort])
        
        # 创建菜单
        self.menu = None
        
        # 连接点击事件
        self.clicked.connect(self.show_menu)
        
        # 标记是否正在处理排序变更
        self.is_processing = False
        
    def update_style(self):
        """更新按钮样式"""
        # 使用StyleManager获取按钮样式
        button_style = StyleManager.get_button_style('sort')
        
        # 设置按钮样式
        self.setStyleSheet("""
            QPushButton {
                background-color: #F9FAFB;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #9CA3AF;
            }
            QPushButton:pressed {
                background-color: #E5E7EB;
            }
        """)
        
    def update_icon(self):
        """更新按钮图标"""
        svg_content = self.sort_icons[self.current_sort]
        
        # 设置图标颜色
        svg_data = svg_content.replace("currentColor", "#4B5563")
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def setup_menu(self):
        """设置下拉菜单"""
        # 设置菜单样式
        self.menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
                margin: 2px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
                min-width: 180px;
            }
            QMenu::item:selected {
                background-color: #3B82F6;
                color: white;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 添加排序选项
        sort_options = [
            ('updated_desc', '按更新时间（最新的在前）'),
            ('updated_asc', '按更新时间（最旧的在前）'),
            ('created_desc', '按创建时间（最新的在前）'),
            ('created_asc', '按创建时间（最旧的在前）'),
            ('title_asc', '按标题排序（A-Z）'),
            ('title_desc', '按标题排序（Z-A）'),
            ('category', '按类别排序')
        ]
        
        for sort_key, sort_text in sort_options:
            action = self.menu.addAction(sort_text)
            action.setData(sort_key)
            
            # 设置图标
            icon_svg = self.sort_icons[sort_key]
            svg_data = icon_svg.replace("currentColor", "#4B5563")
            
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(16, 16)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            action.setIcon(QIcon(pixmap))
            
            # 标记当前选中的排序方式
            if sort_key == self.current_sort:
                # 设置字体为粗体
                font = action.font()
                font.setBold(True)
                action.setFont(font)
        
        # 连接菜单项点击事件
        self.menu.triggered.connect(self.on_sort_method_changed)
        
    def show_menu(self):
        """显示排序菜单"""
        try:
            # 每次显示菜单前都创建新菜单
            if self.menu:
                self.menu.deleteLater()
                
            self.menu = QMenu(self)
            self.menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 5px;
                    margin: 2px;
                }
                QMenu::item {
                    padding: 6px 25px 6px 10px;
                    border-radius: 4px;
                    margin: 2px 5px;
                    color: #1F2937;
                    font-size: 12px;
                    min-width: 180px;
                }
                QMenu::item:selected {
                    background-color: #3B82F6;
                    color: white;
                    font-weight: bold;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #E5E7EB;
                    margin: 5px 10px;
                }
            """)
            
            # 添加排序选项
            sort_options = [
                ('updated_desc', '按更新时间（最新的在前）'),
                ('updated_asc', '按更新时间（最旧的在前）'),
                ('created_desc', '按创建时间（最新的在前）'),
                ('created_asc', '按创建时间（最旧的在前）'),
                ('title_asc', '按标题排序（A-Z）'),
                ('title_desc', '按标题排序（Z-A）'),
                ('category', '按类别排序')
            ]
            
            for sort_key, sort_text in sort_options:
                action = self.menu.addAction(sort_text)
                action.setData(sort_key)
                
                # 设置图标
                icon_svg = self.sort_icons[sort_key]
                svg_data = icon_svg.replace("currentColor", "#4B5563")
                
                renderer = QSvgRenderer()
                renderer.load(svg_data.encode())
                
                pixmap = QPixmap(16, 16)
                pixmap.fill(Qt.transparent)
                
                painter = QPainter(pixmap)
                renderer.render(painter)
                painter.end()
                
                action.setIcon(QIcon(pixmap))
                
                # 标记当前选中的排序方式
                if sort_key == self.current_sort:
                    # 设置字体为粗体
                    font = action.font()
                    font.setBold(True)
                    action.setFont(font)
            
            # 连接菜单项点击事件
            self.menu.triggered.connect(self.on_sort_method_changed)
            
            # 显示菜单
            self.menu.exec(self.mapToGlobal(QPoint(0, self.height())))
        except Exception as e:
            print(f"显示排序菜单时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def on_sort_method_changed(self, action):
        """排序方式变更处理"""
        # 防止重复处理
        if self.is_processing:
            return
            
        try:
            self.is_processing = True
            
            sort_key = action.data()
            if sort_key != self.current_sort:
                self.current_sort = sort_key
                
                # 保存排序设置
                settings = QSettings("PromptAssistant", "AppSettings")
                settings.setValue("sort_method", self.current_sort)
                settings.sync()  # 确保设置被立即保存
                
                # 更新图标和工具提示
                self.update_icon()
                self.setToolTip(self.sort_tooltips[self.current_sort])
                
                # 应用排序
                if self.parent_content_area:
                    QApplication.processEvents()  # 处理可能的挂起事件
                    self.parent_content_area.apply_sort()
                    
                    # 更新状态栏
                    if (hasattr(self.parent_content_area, 'parent_window') and 
                        self.parent_content_area.parent_window):
                        self.parent_content_area.parent_window.status_bar.set_status(
                            f"已按{self.sort_tooltips[self.current_sort]}排序")
        except Exception as e:
            print(f"处理排序方法变更时出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_processing = False 