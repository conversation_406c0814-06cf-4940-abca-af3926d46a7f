#!/usr/bin/env python3
"""
完整实现分类筛选功能
一次性完成所有6个步骤的修改
"""

def complete_category_filter_implementation():
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始完整实现分类筛选功能...")
    
    # 第一步：修改 active_filters 初始化
    print("第一步：添加分类筛选状态管理")
    content = content.replace(
        "self.active_filters = {'favorite': False}  # 跟踪当前激活的筛选器",
        "self.active_filters = {'favorite': False, 'category': '全部'}  # 跟踪当前激活的筛选器\n        self.category_buttons = {}  # 存储分类按钮的引用"
    )
    
    # 第二步：修改 apply_filters_and_update_views 方法，添加分类筛选逻辑
    print("第二步：添加分类筛选逻辑")
    old_filter_logic = """        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]"""
    
    new_filter_logic = """        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]"""
    
    content = content.replace(old_filter_logic, new_filter_logic)
    
    # 第三步：添加新方法
    print("第三步：添加分类按钮点击处理器和状态更新方法")
    
    # 找到 apply_filters_and_update_views 方法的结束位置并插入新方法
    lines = content.split('\n')
    method_start = -1
    method_end = -1
    
    for i, line in enumerate(lines):
        if "def apply_filters_and_update_views(self):" in line:
            method_start = i
            break
    
    if method_start != -1:
        # 找到方法结束位置
        indent_level = len(lines[method_start]) - len(lines[method_start].lstrip())
        for i in range(method_start + 1, len(lines)):
            line = lines[i]
            if line.strip() and not line.startswith(' ' * (indent_level + 4)):
                method_end = i
                break
        
        if method_end != -1:
            # 插入新方法
            new_methods = [
                "",
                "    def on_category_button_clicked(self, category_name):",
                '        """分类按钮点击处理器"""',
                "        # 更新分类筛选状态",
                "        self.active_filters['category'] = category_name",
                "        ",
                "        # 更新按钮状态",
                "        self.update_category_button_states()",
                "        ",
                "        # 应用筛选并更新视图",
                "        self.apply_filters_and_update_views()",
                "",
                "    def update_category_button_states(self):",
                '        """更新分类按钮的激活状态"""',
                "        current_category = self.active_filters.get('category', '全部')",
                "        ",
                "        for category_name, button in self.category_buttons.items():",
                "            if category_name == current_category:",
                "                # 激活状态样式",
                "                button.setStyleSheet('''",
                "                    QPushButton {",
                "                        background-color: #3B82F6;",
                "                        color: white;",
                "                        border: none;",
                "                        border-radius: 4px;",
                "                        padding: 6px 12px;",
                "                        font-size: 12px;",
                "                        margin: 2px;",
                "                    }",
                "                    QPushButton:hover {",
                "                        background-color: #2563EB;",
                "                    }",
                "                ''')",
                "            else:",
                "                # 默认状态样式",
                "                button.setStyleSheet('''",
                "                    QPushButton {",
                "                        background-color: #F3F4F6;",
                "                        color: #374151;",
                "                        border: 1px solid #D1D5DB;",
                "                        border-radius: 4px;",
                "                        padding: 6px 12px;",
                "                        font-size: 12px;",
                "                        margin: 2px;",
                "                    }",
                "                    QPushButton:hover {",
                "                        background-color: #E5E7EB;",
                "                        border-color: #9CA3AF;",
                "                    }",
                "                ''')"
            ]
            
            lines[method_end:method_end] = new_methods
            content = '\n'.join(lines)
    
    # 第四步：修改 populate_category_filters 方法
    print("第四步：修改 populate_category_filters 方法")
    
    # 查找并替换整个方法
    old_populate_method = '''    def populate_category_filters(self):
        """动态填充分类筛选按钮"""
        
        # 清除现有按钮
        for i in reversed(range(self.category_filter_layout.count())):
            child = self.category_filter_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 获取所有分类
        categories = self.get_all_categories()
        
        # 添加"全部"按钮
        all_button = QPushButton("全部")
        all_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        self.category_filter_layout.addWidget(all_button)
        
        # 添加各分类按钮
        for category in categories:
            button = QPushButton(category)
            button.setStyleSheet("""
                QPushButton {
                    background-color: #F3F4F6;
                    color: #374151;
                    border: 1px solid #D1D5DB;
                    border-radius: 4px;
                    padding: 6px 12px;
                    font-size: 12px;
                    margin: 2px;
                }
                QPushButton:hover {
                    background-color: #E5E7EB;
                    border-color: #9CA3AF;
                }
            """)
            self.category_filter_layout.addWidget(button)
        
        # 添加弹性空间
        self.category_filter_layout.addStretch()'''
    
    new_populate_method = '''    def populate_category_filters(self):
        """动态填充分类筛选按钮"""
        
        # 清除现有按钮和引用
        self.category_buttons.clear()
        for i in reversed(range(self.category_filter_layout.count())):
            child = self.category_filter_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 获取所有分类
        categories = self.get_all_categories()
        
        # 添加"全部"按钮
        all_button = QPushButton("全部")
        all_button.clicked.connect(lambda: self.on_category_button_clicked("全部"))
        self.category_buttons["全部"] = all_button
        self.category_filter_layout.addWidget(all_button)
        
        # 添加各分类按钮
        for category in categories:
            button = QPushButton(category)
            button.clicked.connect(lambda checked, cat=category: self.on_category_button_clicked(cat))
            self.category_buttons[category] = button
            self.category_filter_layout.addWidget(button)
        
        # 添加弹性空间
        self.category_filter_layout.addStretch()
        
        # 更新按钮状态
        self.update_category_button_states()'''
    
    content = content.replace(old_populate_method, new_populate_method)
    
    # 第五步：修改 on_filter_button_clicked 方法中的分类筛选隐藏逻辑
    print("第五步：添加分类筛选隐藏时的重置逻辑")
    
    # 查找并修改分类筛选的隐藏逻辑
    old_category_toggle = '''            elif filter_key == "category":
                self.toggle_category_filter()'''
    
    new_category_toggle = '''            elif filter_key == "category":
                if self.category_filter_bar.isVisible():
                    # 隐藏时重置为"全部"
                    self.active_filters['category'] = '全部'
                    self.category_filter_bar.hide()
                    self.apply_filters_and_update_views()
                else:
                    self.populate_category_filters()
                    self.category_filter_bar.show()'''
    
    content = content.replace(old_category_toggle, new_category_toggle)
    
    # 第六步：移除不再需要的 toggle_category_filter 方法（如果存在）
    print("第六步：清理不再需要的方法")
    
    # 查找并移除 toggle_category_filter 方法
    lines = content.split('\n')
    new_lines = []
    skip_method = False
    method_indent = 0
    
    for line in lines:
        if "def toggle_category_filter(self):" in line:
            skip_method = True
            method_indent = len(line) - len(line.lstrip())
            continue
        
        if skip_method:
            current_indent = len(line) - len(line.lstrip()) if line.strip() else float('inf')
            if line.strip() and current_indent <= method_indent:
                skip_method = False
                new_lines.append(line)
            # 跳过方法内容
        else:
            new_lines.append(line)
    
    content = '\n'.join(new_lines)
    
    # 写回文件
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("分类筛选功能完整实现完成！")
    print("已完成的修改：")
    print("1. ✅ 添加了分类筛选状态管理 (active_filters 和 category_buttons)")
    print("2. ✅ 添加了分类筛选逻辑 (apply_filters_and_update_views)")
    print("3. ✅ 添加了分类按钮点击处理器 (on_category_button_clicked)")
    print("4. ✅ 添加了按钮状态更新方法 (update_category_button_states)")
    print("5. ✅ 修改了 populate_category_filters 方法 (按钮引用和信号连接)")
    print("6. ✅ 修改了 on_filter_button_clicked 方法 (隐藏时重置逻辑)")
    print("\n现在分类筛选功能应该完全可用了！")

if __name__ == '__main__':
    complete_category_filter_implementation()