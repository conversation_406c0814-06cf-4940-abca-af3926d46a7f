#!/usr/bin/env python3
"""
基础页面组件
为所有页面组件提供统一的基类和通用功能
"""
from PySide6.QtWidgets import QWidget, QVBoxLayout
from abc import ABC, abstractmethod, ABCMeta
from typing import Optional


class QWidgetMeta(type(QWidget), ABCMeta):
    """解决QWidget和ABC的metaclass冲突"""
    pass


class BasePage(QWidget, ABC, metaclass=QWidgetMeta):
    """所有页面组件的基类"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self._is_initialized = False
        self._main_layout = None
        self._setup_base_properties()
        self.setup_ui()
        self.load_data()
        self._is_initialized = True
    
    def _setup_base_properties(self):
        """设置基础属性"""
        # 启用鼠标跟踪，使enterEvent和leaveEvent生效
        self.setMouseTracking(True)
        
        # 设置属性，可用于样式表
        self.setProperty("class", self.__class__.__name__)
        
        # 创建主布局
        self._main_layout = QVBoxLayout(self)
        self._main_layout.setContentsMargins(0, 0, 0, 0)
        self._main_layout.setSpacing(0)
    
    def get_main_layout(self) -> QVBoxLayout:
        """获取主布局"""
        return self._main_layout
    
    @abstractmethod
    def setup_ui(self) -> None:
        """设置UI - 子类必须实现"""
        pass
    
    @abstractmethod  
    def load_data(self) -> None:
        """加载数据 - 子类必须实现"""
        pass
    
    def apply_page_style(self) -> None:
        """应用页面样式 - 子类可以重写"""
        self.setStyleSheet("""
            QWidget {
                background-color: white;
            }
        """)
    
    def refresh(self) -> None:
        """刷新页面 - 子类可以重写"""
        if self._is_initialized:
            self.load_data()
    
    def is_initialized(self) -> bool:
        """检查页面是否已初始化"""
        return self._is_initialized 