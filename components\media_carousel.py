#!/usr/bin/env python3
"""
媒体轮播组件
从main.py提取的ThumbnailCarousel类
"""
from pathlib import Path
from typing import Optional, List, Tuple

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QLabel, QPushButton)
from PySide6.QtCore import Qt, QPoint, Signal, QEvent
from PySide6.QtGui import QPixmap, QPainter, QColor, QPen, QImage

# 导入视频缩略图处理功能
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV已成功加载，视频缩略图功能可用")
except ImportError:
    OPENCV_AVAILABLE = False
    print("OpenCV未安装，视频缩略图功能不可用")


def extract_video_thumbnail(video_path: str, max_size: Tuple[int, int] = (150, 150)) -> Optional['QPixmap']:
    """
    提取视频缩略图（第一帧）- 复制自main.py中的函数
    
    Args:
        video_path: 视频文件路径
        max_size: 缩略图最大尺寸
        
    Returns:
        缩略图QPixmap或None
    """
    if not OPENCV_AVAILABLE:
        return None
    
    try:
        import cv2
        import numpy as np
        
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        
        # 检查是否成功打开
        if not cap.isOpened():
            print(f"无法打开视频文件: {video_path}")
            return None
        
        # 读取第一帧
        ret, frame = cap.read()
        
        # 释放视频对象
        cap.release()
        
        if not ret or frame is None:
            print(f"无法读取视频帧: {video_path}")
            return None
        
        # 将BGR格式转换为RGB格式
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 调整大小
        height, width = rgb_frame.shape[:2]
        max_width, max_height = max_size
        
        # 计算缩放比例
        scale = min(max_width / width, max_height / height)
        
        if scale < 1:
            new_width = int(width * scale)
            new_height = int(height * scale)
            rgb_frame = cv2.resize(rgb_frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # 创建QImage
        height, width, channel = rgb_frame.shape
        bytes_per_line = channel * width
        q_img = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
        
        # 创建QPixmap
        pixmap = QPixmap.fromImage(q_img)
        
        return pixmap
        
    except Exception as e:
        print(f"提取视频缩略图时出错: {e}")
        return None


class ThumbnailCarousel(QWidget):
    """缩略图轮播组件，用于在提示词卡片中显示可滑动的缩略图 - 提取自main.py"""
    
    # 自定义信号，点击缩略图时发射，传递当前图片索引和媒体文件列表
    thumbnailClicked = Signal(int, list)
    
    def __init__(self, media_files=None, parent=None):
        super().__init__(parent)
        self.media_files = media_files if media_files else []
        self.current_index = 0
        self.drag_start_x = 0
        self.is_dragging = False
        self.is_hovered = False  # 添加悬停状态变量
        self.animation = None
        
        # 设置固定大小
        self.setFixedSize(80, 80)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        # 主布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # 缩略图容器
        self.thumbnail_container = QWidget()
        self.thumbnail_container.setFixedSize(80, 80)
        self.thumbnail_container.setStyleSheet("""
            QWidget {
                background-color: #F3F4F6;
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
        """)
        
        # 缩略图标签
        self.thumbnail_label = QLabel(self.thumbnail_container)
        self.thumbnail_label.setFixedSize(78, 78)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: none;
            }
        """)
        # 不使用setScaledContents，而是在加载图片时手动按比例缩放
        
        # 左箭头按钮
        self.left_button = QPushButton("<", self.thumbnail_container)
        self.left_button.setFixedSize(20, 20)
        self.left_button.move(5, 30)
        self.left_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.7);
            }
        """)
        self.left_button.clicked.connect(self.show_previous)
        self.left_button.hide()  # 初始隐藏
        
        # 右箭头按钮
        self.right_button = QPushButton(">", self.thumbnail_container)
        self.right_button.setFixedSize(20, 20)
        self.right_button.move(55, 30)
        self.right_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.7);
            }
        """)
        self.right_button.clicked.connect(self.show_next)
        self.right_button.hide()  # 初始隐藏
        
        # 图片索引指示器
        self.indicator_label = QLabel(self.thumbnail_container)
        self.indicator_label.setFixedSize(40, 16)
        self.indicator_label.move(20, 60)
        self.indicator_label.setAlignment(Qt.AlignCenter)
        self.indicator_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
            }
        """)
        self.indicator_label.hide()  # 初始隐藏
        
        self.layout.addWidget(self.thumbnail_container)
        
        # 安装事件过滤器，用于处理鼠标悬停事件
        self.thumbnail_container.installEventFilter(self)
        
        # 如果有媒体文件，加载第一个
        if self.media_files:
            self.load_current_thumbnail()
            self.update_controls_visibility()
            
    def clear(self):
        """清空媒体文件列表"""
        self.media_files = []
        self.current_index = 0
        self.thumbnail_label.setText("📷")
        self.thumbnail_label.setStyleSheet("""
            font-size: 24px;
            background-color: transparent;
            border: none;
        """)
        self.update_controls_visibility()
        
    def add_item(self, media_path):
        """添加单个媒体文件到列表"""
        if media_path == "无媒体文件":
            # 特殊情况：显示提示文本
            self.clear()
            self.thumbnail_label.setText("无媒体文件")
            self.thumbnail_label.setStyleSheet("""
                font-size: 12px;
                background-color: transparent;
                border: none;
                color: #6B7280;
            """)
            return
            
        self.media_files.append(media_path)
        # 如果这是添加的第一个媒体文件，则加载它
        if len(self.media_files) == 1:
            self.current_index = 0
            self.load_current_thumbnail()
        self.update_controls_visibility()
    
    def set_media_files(self, media_files):
        """设置媒体文件列表"""
        self.media_files = media_files if media_files else []
        self.current_index = 0
        self.load_current_thumbnail()
        self.update_controls_visibility()
        
    def load_current_thumbnail(self):
        """加载当前索引的缩略图"""
        if not self.media_files or self.current_index >= len(self.media_files):
            # 没有媒体文件，显示默认图标
            self.thumbnail_label.setText("📷")
            self.thumbnail_label.setStyleSheet("""
                font-size: 24px;
                background-color: transparent;
                border: none;
            """)
            return
            
        media_path = self.media_files[self.current_index]
        file_path = Path(media_path)
        
        if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片文件
            pixmap = QPixmap(media_path)
            if not pixmap.isNull():
                # 获取原始图片尺寸
                orig_width = pixmap.width()
                orig_height = pixmap.height()
                
                # 计算缩放后的尺寸，保持原始比例
                container_size = 78  # 缩略图容器的大小
                
                if orig_width >= orig_height:
                    # 宽图
                    new_width = container_size
                    new_height = int(orig_height * container_size / orig_width)
                else:
                    # 高图
                    new_height = container_size
                    new_width = int(orig_width * container_size / orig_height)
                
                # 按比例缩放图片
                scaled_pixmap = pixmap.scaled(new_width, new_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                
                # 创建一个透明背景的图片，大小与容器相同
                result_pixmap = QPixmap(container_size, container_size)
                result_pixmap.fill(Qt.transparent)
                
                # 在透明背景上绘制缩放后的图片，使其居中
                painter = QPainter(result_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                painter.setRenderHint(QPainter.SmoothPixmapTransform)
                
                # 计算居中位置
                x = (container_size - new_width) // 2
                y = (container_size - new_height) // 2
                
                # 绘制图片
                painter.drawPixmap(x, y, scaled_pixmap)
                painter.end()
                
                # 设置缩略图
                self.thumbnail_label.setPixmap(result_pixmap)
                self.thumbnail_label.setStyleSheet("""
                    background-color: transparent;
                    border: none;
                """)
            else:
                # 加载失败，显示占位符
                self.thumbnail_label.setText("🖼️")
                self.thumbnail_label.setStyleSheet("""
                    font-size: 24px;
                    background-color: transparent;
                    border: none;
                """)
        elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件 - 尝试提取第一帧作为缩略图
            container_size = 78  # 缩略图容器的大小
            result_pixmap = None
            
            print(f"ThumbnailCarousel - 正在处理视频文件: {file_path}, OpenCV可用: {OPENCV_AVAILABLE}")
            if OPENCV_AVAILABLE:
                # 尝试使用OpenCV提取视频缩略图
                print(f"ThumbnailCarousel - 开始提取视频缩略图: {file_path}")
                thumb_pixmap = extract_video_thumbnail(file_path, (container_size, container_size))
                print(f"ThumbnailCarousel - 提取结果: {'成功' if thumb_pixmap and not thumb_pixmap.isNull() else '失败'}")
                
                if thumb_pixmap and not thumb_pixmap.isNull():
                    # 创建一个透明背景的图片，大小与容器相同
                    result_pixmap = QPixmap(container_size, container_size)
                    result_pixmap.fill(Qt.transparent)
                    
                    # 在透明背景上绘制缩略图和播放图标
                    painter = QPainter(result_pixmap)
                    painter.setRenderHint(QPainter.Antialiasing)
                    
                    # 先绘制视频缩略图
                    painter.drawPixmap(0, 0, thumb_pixmap)
                    
                    # 只在悬停时绘制播放按钮
                    if self.is_hovered:
                        # 绘制半透明遮罩
                        painter.setBrush(QColor(0, 0, 0, 100))  # 半透明黑色
                        painter.setPen(Qt.NoPen)
                        painter.drawRect(0, 0, container_size, container_size)
                        
                        # 绘制播放图标
                        painter.setPen(QColor(255, 255, 255))
                        painter.setBrush(QColor(255, 255, 255))
                        
                        # 绘制三角形播放图标
                        play_size = 30
                        center_x = container_size // 2
                        center_y = container_size // 2
                        
                        # 创建三角形的三个点
                        points = [
                            QPoint(center_x - play_size // 3, center_y - play_size // 2),
                            QPoint(center_x + play_size // 2, center_y),
                            QPoint(center_x - play_size // 3, center_y + play_size // 2)
                        ]
                        
                        # 绘制三角形
                        painter.drawPolygon(points)
                    
                    painter.end()
            
            # 如果无法提取缩略图，则使用默认的播放图标
            if result_pixmap is None:
                result_pixmap = QPixmap(container_size, container_size)
                result_pixmap.fill(Qt.transparent)
                
                # 在透明背景上绘制播放图标
                painter = QPainter(result_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # 绘制视频图标背景
                painter.setBrush(QColor(0, 0, 0, 128))
                painter.setPen(Qt.NoPen)
                painter.drawRect(0, 0, container_size, container_size)
                
                # 只在悬停时绘制播放图标
                if self.is_hovered:
                    # 绘制播放图标
                    painter.setPen(QColor(255, 255, 255))
                    painter.setBrush(QColor(255, 255, 255))
                    
                    # 绘制三角形播放图标
                    play_size = 30
                    center_x = container_size // 2
                    center_y = container_size // 2
                    
                    # 创建三角形的三个点
                    points = [
                        QPoint(center_x - play_size // 3, center_y - play_size // 2),
                        QPoint(center_x + play_size // 2, center_y),
                        QPoint(center_x - play_size // 3, center_y + play_size // 2)
                    ]
                    
                    # 绘制三角形
                    painter.drawPolygon(points)
                
                painter.end()
            
            # 设置缩略图
            self.thumbnail_label.setPixmap(result_pixmap)
            self.thumbnail_label.setStyleSheet("""
                background-color: transparent;
                border: none;
            """)
        else:
            # 其他文件
            self.thumbnail_label.setText("📄")
            self.thumbnail_label.setStyleSheet("""
                font-size: 24px;
                background-color: transparent;
                border: none;
            """)
            
        # 更新指示器文本
        self.indicator_label.setText(f"{self.current_index + 1}/{len(self.media_files)}")
        
    def show_next(self):
        """显示下一张缩略图"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index + 1) % len(self.media_files)
        self.load_current_thumbnail()
        
    def show_previous(self):
        """显示上一张缩略图"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index - 1) % len(self.media_files)
        self.load_current_thumbnail()
        
    def update_controls_visibility(self):
        """更新控件可见性"""
        has_multiple_files = len(self.media_files) > 1
        self.left_button.setVisible(has_multiple_files)
        self.right_button.setVisible(has_multiple_files)
        self.indicator_label.setVisible(has_multiple_files)
        
    def update_thumbnail_display(self):
        """更新缩略图显示（重新绘制以反映悬停状态）"""
        if self.media_files:
            self.load_current_thumbnail()
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理鼠标事件"""
        if obj == self.thumbnail_container:
            if event.type() == QEvent.MouseButtonPress and event.button() == Qt.LeftButton:
                # 记录拖动起始位置
                self.drag_start_x = event.position().x()
                self.is_dragging = True
                return True
                
            elif event.type() == QEvent.MouseMove and self.is_dragging:
                # 处理拖动事件
                if len(self.media_files) > 1:
                    delta = event.position().x() - self.drag_start_x
                    if abs(delta) > 30:  # 拖动距离超过阈值
                        if delta > 0:
                            self.show_previous()
                        else:
                            self.show_next()
                        self.is_dragging = False
                return True
                
            elif event.type() == QEvent.MouseButtonRelease:
                # 如果不是拖动，则视为点击
                if self.is_dragging and abs(event.position().x() - self.drag_start_x) < 10:
                    self.thumbnailClicked.emit(self.current_index, self.media_files)
                self.is_dragging = False
                return True
                
            elif event.type() == QEvent.Enter:
                # 鼠标进入时显示控件和播放按钮
                self.is_hovered = True
                self.update_controls_visibility()
                self.update_thumbnail_display()
                return True
                
            elif event.type() == QEvent.Leave:
                # 鼠标离开时隐藏控件和播放按钮
                self.is_hovered = False
                if len(self.media_files) > 1:
                    self.left_button.hide()
                    self.right_button.hide()
                    self.indicator_label.hide()
                self.update_thumbnail_display()
                return True
                
        return super().eventFilter(obj, event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 点击缩略图时发射信号
            self.thumbnailClicked.emit(self.current_index, self.media_files)
        super().mousePressEvent(event) 