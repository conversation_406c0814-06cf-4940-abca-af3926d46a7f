#!/usr/bin/env python3
"""
导入提示词对话框
从main.py提取的ImportPromptDialog组件
"""
import os
import base64
import json
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QTextEdit, QPushButton, QMessageBox)
from PySide6.QtCore import Qt

from views.base_components import BaseDialog


class ImportPromptDialog(BaseDialog):
    """导入提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        
        self.setWindowTitle("获取提示词")
        self.setFixedSize(400, 250)
        
        # 应用统一的对话框样式
        from components.style_manager import StyleManager
        self.setStyleSheet(StyleManager.get_dialog_style('import'))
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("导入分享的提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("请粘贴分享链接（支持多个链接，每行一个）:")
        desc_label.setStyleSheet("color: #374151; font-size: 14px;")
        layout.addWidget(desc_label)
        
        # 链接输入框
        self.link_input = QTextEdit()
        self.link_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
                font-size: 13px;
            }
        """)
        self.link_input.setFixedHeight(120)
        self.link_input.setPlaceholderText("粘贴以 promptshare:// 开头的分享链接\n支持多个链接，每行一个\n支持批量分享链接（promptshare://batch/）")
        layout.addWidget(self.link_input)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.import_btn = self.create_primary_button("导入")
        self.import_btn.clicked.connect(self.import_prompt)
        
        self.batch_import_btn = self.create_secondary_button("批量导入")
        self.batch_import_btn.clicked.connect(self.import_batch_prompts)
        
        cancel_btn = self.create_outline_button("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.batch_import_btn)
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
    def import_prompt(self):
        """导入提示词"""
        link = self.link_input.toPlainText().strip()
        
        # 检查链接格式
        if not link.startswith("promptshare://"):
            QMessageBox.warning(self, "格式错误", "无效的分享链接格式，请确保链接以 promptshare:// 开头")
            return
        
        try:
            # 提取分享代码
            share_code = link.replace("promptshare://", "")
            prompt_dict = self.decode_share_code(share_code)
            
            if not prompt_dict:
                QMessageBox.warning(self, "导入失败", "无法解析分享链接")
                return
                
            # 创建Prompt对象
            from model import Prompt
            new_prompt = Prompt(
                title=prompt_dict.get('title', ''),
                content=prompt_dict.get('content', ''),
                tags=prompt_dict.get('tags', []),
                category=prompt_dict.get('category', ''),
                media_files=prompt_dict.get('media_files', []),
                type=prompt_dict.get('type', '文本')
            )
            
            # 检查是否有媒体文件但文件不存在
            missing_media = []
            for media_file in new_prompt.media_files:
                if not os.path.exists(media_file):
                    missing_media.append(media_file)
            
            if missing_media:
                media_list = "\n".join(missing_media)
                QMessageBox.warning(self, "媒体文件缺失", 
                                   f"以下媒体文件在您的系统上不存在，将无法显示：\n{media_list}")
            
            # 添加到数据库
            if self.model:
                prompt_id = self.model.add_prompt(new_prompt)
                if prompt_id:
                    QMessageBox.information(self, "导入成功", f"已成功导入提示词: {new_prompt.title}")
                    self.accept()
                else:
                    QMessageBox.warning(self, "导入失败", "无法保存提示词到数据库")
            else:
                QMessageBox.warning(self, "导入失败", "数据模型不可用")
        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入过程中出错: {str(e)}")
    
    def import_batch_prompts(self):
        """批量导入提示词"""
        links_text = self.link_input.toPlainText().strip()
        if not links_text:
            QMessageBox.warning(self, "输入错误", "请先输入分享链接")
            return
        
        # 解析多个链接
        links = self.parse_multiple_links(links_text)
        if not links:
            QMessageBox.warning(self, "格式错误", "未找到有效的分享链接")
            return
        
        # 开始批量导入
        success_count = 0
        failed_count = 0
        failed_links = []
        
        for i, link in enumerate(links):
            try:
                # 检查链接格式
                if not link.startswith("promptshare://"):
                    failed_links.append(f"链接 {i+1}: 无效格式")
                    failed_count += 1
                    continue
                
                # 处理批量分享链接
                if link.startswith("promptshare://batch/"):
                    batch_result = self.import_batch_share_link(link)
                    if batch_result:
                        success_count += batch_result
                    else:
                        failed_links.append(f"链接 {i+1}: 批量链接解析失败")
                        failed_count += 1
                else:
                    # 处理单个分享链接
                    share_code = link.replace("promptshare://", "")
                    prompt_dict = self.decode_share_code(share_code)
                    
                    if not prompt_dict:
                        failed_links.append(f"链接 {i+1}: 解码失败")
                        failed_count += 1
                        continue
                    
                    # 创建Prompt对象
                    from model import Prompt
                    new_prompt = Prompt(
                        title=prompt_dict.get('title', ''),
                        content=prompt_dict.get('content', ''),
                        tags=prompt_dict.get('tags', []),
                        category=prompt_dict.get('category', ''),
                        media_files=prompt_dict.get('media_files', []),
                        type=prompt_dict.get('type', '文本')
                    )
                    
                    # 添加到数据库
                    if self.model:
                        prompt_id = self.model.add_prompt(new_prompt)
                        if prompt_id:
                            success_count += 1
                        else:
                            failed_links.append(f"链接 {i+1}: 保存失败")
                            failed_count += 1
                    else:
                        failed_links.append(f"链接 {i+1}: 数据模型不可用")
                        failed_count += 1
                        
            except Exception as e:
                failed_links.append(f"链接 {i+1}: {str(e)}")
                failed_count += 1
        
        # 显示导入结果
        result_message = f"批量导入完成！\n\n成功导入: {success_count} 个提示词\n失败: {failed_count} 个"
        if failed_links:
            result_message += f"\n\n失败详情:\n" + "\n".join(failed_links[:5])  # 只显示前5个失败项
            if len(failed_links) > 5:
                result_message += f"\n... 还有 {len(failed_links) - 5} 个失败项"
        
        if success_count > 0:
            QMessageBox.information(self, "批量导入成功", result_message)
            self.accept()
        else:
            QMessageBox.warning(self, "批量导入失败", result_message)
    
    def parse_multiple_links(self, links_text):
        """解析多个分享链接"""
        lines = links_text.strip().split('\n')
        links = []
        for line in lines:
            line = line.strip()
            if line and line.startswith("promptshare://"):
                links.append(line)
        return links
    
    def import_batch_share_link(self, batch_link):
        """导入批量分享链接"""
        try:
            # 提取批量分享代码
            batch_code = batch_link.replace("promptshare://batch/", "")
            prompts_data = self.decode_batch_share_code(batch_code)
            
            if not prompts_data:
                return 0
            
            success_count = 0
            for prompt_dict in prompts_data:
                try:
                    # 创建Prompt对象
                    from model import Prompt
                    new_prompt = Prompt(
                        title=prompt_dict.get('title', ''),
                        content=prompt_dict.get('content', ''),
                        tags=prompt_dict.get('tags', []),
                        category=prompt_dict.get('category', ''),
                        media_files=prompt_dict.get('media_files', []),
                        type=prompt_dict.get('type', '文本')
                    )
                    
                    # 添加到数据库
                    if self.model:
                        prompt_id = self.model.add_prompt(new_prompt)
                        if prompt_id:
                            success_count += 1
                except Exception as e:
                    print(f"导入批量提示词时出错: {e}")
                    continue
            
            return success_count
        except Exception as e:
            print(f"解析批量分享链接时出错: {e}")
            return 0
    
    def decode_batch_share_code(self, batch_code):
        """解码批量分享代码"""
        try:
            # Base64解码
            json_str = base64.urlsafe_b64decode(batch_code).decode('utf-8')
            
            # JSON反序列化
            prompts_data = json.loads(json_str)
            return prompts_data
        except Exception as e:
            print(f"解码批量分享代码时出错: {e}")
            return None
    
    def decode_share_code(self, share_code):
        """解码分享代码"""
        try:
            # Base64解码
            json_str = base64.urlsafe_b64decode(share_code).decode('utf-8')
            
            # JSON反序列化
            prompt_dict = json.loads(json_str)
            return prompt_dict
        except Exception as e:
            print(f"解码分享代码时出错: {e}")
            return None 