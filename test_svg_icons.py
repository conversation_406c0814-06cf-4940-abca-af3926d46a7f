#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SVG图标加载功能
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import QSize, Qt
from PySide6.QtGui import QIcon, QPainter, QPixmap
from PySide6.QtSvg import QSvgRenderer

class SVGIconTest(QMainWindow):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def load_svg_icon(self, svg_file_path, size=24):
        """加载SVG图标并转换为QIcon"""
        try:
            icon_path = Path(svg_file_path)
            if icon_path.exists():
                renderer = QSvgRenderer(str(icon_path))
                pixmap = QPixmap(size, size)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                renderer.render(painter)
                painter.end()
                return QIcon(pixmap)
            else:
                print(f"SVG图标文件不存在: {svg_file_path}")
                return QIcon()
        except Exception as e:
            print(f"加载SVG图标失败: {e}")
            return QIcon()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("SVG图标测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        # 恢复选中提示词按钮
        restore_btn = QPushButton("恢复选中提示词")
        restore_icon = self.load_svg_icon("icons/restore.svg", 16)
        restore_btn.setIcon(restore_icon)
        restore_btn.setIconSize(QSize(16, 16))
        layout.addWidget(restore_btn)
        
        # 彻底删除选中提示词按钮
        delete_btn = QPushButton("彻底删除选中提示词")
        delete_icon = self.load_svg_icon("icons/delete-forever.svg", 16)
        delete_btn.setIcon(delete_icon)
        delete_btn.setIconSize(QSize(16, 16))
        layout.addWidget(delete_btn)
        
        # 清空回收站按钮
        clear_btn = QPushButton("清空回收站")
        clear_icon = self.load_svg_icon("icons/trash-empty.svg", 16)
        clear_btn.setIcon(clear_icon)
        clear_btn.setIconSize(QSize(16, 16))
        layout.addWidget(clear_btn)

def main():
    app = QApplication(sys.argv)
    window = SVGIconTest()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()