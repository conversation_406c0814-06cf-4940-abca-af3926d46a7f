#!/usr/bin/env python3
"""
测试ThumbnailCarousel信号连接修复
"""
import sys
from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel
from main import PromptCardWidget
from model import PromptModel
from pathlib import Path

def test_fix():
    """测试修复效果"""
    print("🔧 测试ThumbnailCarousel信号连接修复...")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication.instance() or QApplication(sys.argv)
    
    # 获取有图片的prompt
    model = PromptModel("sqlite", "prompts.db")
    prompts = model.get_all_prompts()
    
    test_prompt = None
    for prompt in prompts:
        if prompt.title == "测试图片提示词":
            test_prompt = prompt
            break
    
    if not test_prompt:
        print("❌ 未找到测试图片提示词")
        return False
    
    print(f"✅ 找到测试提示词: {test_prompt.title}")
    print(f"📁 媒体文件数量: {len(test_prompt.media_files)}")
    
    # 创建窗口
    window = QWidget()
    window.setWindowTitle("修复效果测试")
    window.resize(500, 400)
    
    layout = QVBoxLayout(window)
    
    # 添加说明
    info_label = QLabel(f"""
修复测试
================

提示词: {test_prompt.title}
媒体文件: {len(test_prompt.media_files)} 个

修复内容:
• 在setup_ui中添加_find_and_connect_thumbnail_carousel()
• 递归查找ThumbnailCarousel组件
• 连接thumbnailClicked信号到on_thumbnail_clicked

请点击下方卡片中的缩略图测试
    """)
    info_label.setStyleSheet("""
        QLabel {
            font-size: 11px;
            padding: 10px;
            background-color: #f0f8ff;
            border: 1px solid #0066cc;
            border-radius: 5px;
        }
    """)
    layout.addWidget(info_label)
    
    # 创建PromptCardWidget
    print("🏗️ 创建PromptCardWidget...")
    card = PromptCardWidget(test_prompt, window)
    
    # 检查thumbnail_carousel是否存在
    if hasattr(card, 'thumbnail_carousel'):
        print(f"✅ card.thumbnail_carousel存在: {card.thumbnail_carousel}")
        print(f"📡 信号已连接到on_thumbnail_clicked")
    else:
        print("❌ card.thumbnail_carousel不存在")
    
    layout.addWidget(card)
    
    print("🖥️ 显示测试窗口...")
    print("📝 请点击缩略图，应该能正常打开图片查看器")
    window.show()
    
    return True

if __name__ == "__main__":
    test_fix() 