#!/usr/bin/env python3
"""
分享提示词对话框
从main.py提取的SharePromptDialog组件
"""
import base64
import json
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QComboBox, QListWidget, QListWidgetItem, QTextEdit, 
                              QPushButton, QMessageBox, QApplication)
from PySide6.QtCore import Qt
from PySide6.QtGui import QClipboard

from views.base_components import BaseDialog
from components.style_manager import StyleManager


class SharePromptDialog(BaseDialog):
    """分享提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        self.selected_prompt = None
        
        # 初始化筛选相关属性
        self.all_prompts = []
        self.filtered_prompts = []
        self.current_category_filter = "全部分类"
        self.current_tag_filter = "全部标签"
        
        self.setWindowTitle("分享提示词")
        self.setFixedSize(450, 600)
        
        # 应用StyleManager统一样式
        dialog_style = StyleManager.get_dialog_style('share')
        self.setStyleSheet(dialog_style)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("选择要分享的提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 筛选区域
        filter_layout = QVBoxLayout()
        filter_label = QLabel("筛选条件:")
        filter_label.setStyleSheet("color: #374151; font-size: 14px; font-weight: 500;")
        filter_layout.addWidget(filter_label)
        
        # 分类筛选
        category_layout = QHBoxLayout()
        category_label = QLabel("分类:")
        category_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        category_label.setFixedWidth(40)
        
        self.category_combo = QComboBox()
        combobox_style = StyleManager.get_combobox_style()
        self.category_combo.setStyleSheet(combobox_style)
        self.category_combo.addItem("全部分类")
        self.category_combo.currentTextChanged.connect(self.on_category_filter_changed)
        
        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_combo)
        category_layout.addStretch()
        
        # 标签筛选
        tag_layout = QHBoxLayout()
        tag_label = QLabel("标签:")
        tag_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        tag_label.setFixedWidth(40)
        
        self.tag_combo = QComboBox()
        self.tag_combo.setStyleSheet(combobox_style)
        self.tag_combo.addItem("全部标签")
        self.tag_combo.currentTextChanged.connect(self.on_tag_filter_changed)
        
        tag_layout.addWidget(tag_label)
        tag_layout.addWidget(self.tag_combo)
        tag_layout.addStretch()
        
        filter_layout.addLayout(category_layout)
        filter_layout.addLayout(tag_layout)
        layout.addLayout(filter_layout)
        
        # 提示词列表
        prompt_layout = QVBoxLayout()
        prompt_label = QLabel("选择提示词:")
        prompt_label.setStyleSheet("color: #374151; font-size: 14px; font-weight: 500;")
        
        # 选择控制按钮
        selection_layout = QHBoxLayout()
        self.select_all_btn = self.create_outline_button("全选")
        self.select_all_btn.clicked.connect(self.select_all_prompts)
        
        self.deselect_all_btn = self.create_outline_button("取消全选")
        self.deselect_all_btn.clicked.connect(self.deselect_all_prompts)
        
        self.selection_count_label = QLabel("已选择: 0 个提示词")
        self.selection_count_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        
        selection_layout.addWidget(self.select_all_btn)
        selection_layout.addWidget(self.deselect_all_btn)
        selection_layout.addStretch()
        selection_layout.addWidget(self.selection_count_label)
        
        self.prompt_list = QListWidget()
        self.prompt_list.setSelectionMode(QListWidget.MultiSelection)
        self.prompt_list.setMinimumHeight(180)
        list_style = """
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #F9FAFB;
            }
        """
        self.prompt_list.setStyleSheet(list_style)
        self.prompt_list.itemSelectionChanged.connect(self.on_prompt_selection_changed)
        
        prompt_layout.addWidget(prompt_label)
        prompt_layout.addLayout(selection_layout)
        prompt_layout.addWidget(self.prompt_list)
        layout.addLayout(prompt_layout)
        
        # 分享链接区域
        link_layout = QVBoxLayout()
        link_label = QLabel("分享链接:")
        link_label.setStyleSheet("color: #374151; font-size: 14px; font-weight: 500;")
        self.link_text = QTextEdit()
        self.link_text.setReadOnly(True)
        self.link_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: #F9FAFB;
                padding: 8px;
                font-size: 12px;
                color: #4B5563;
            }
        """)
        self.link_text.setFixedHeight(60)
        self.link_text.setPlaceholderText("选择提示词后将显示分享链接")
        
        link_layout.addWidget(link_label)
        link_layout.addWidget(self.link_text)
        layout.addLayout(link_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.copy_btn = self.create_primary_button("复制链接", enabled=False)
        self.copy_btn.clicked.connect(self.copy_link)
        
        self.batch_share_btn = self.create_secondary_button("批量分享")
        self.batch_share_btn.setEnabled(False)
        self.batch_share_btn.clicked.connect(self.copy_batch_link)
        
        close_btn = self.create_outline_button("关闭")
        close_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.batch_share_btn)
        button_layout.addWidget(self.copy_btn)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        # 加载提示词列表
        self.load_prompts()
        
    def on_category_filter_changed(self, category):
        """分类筛选变化处理"""
        self.current_category_filter = category
        self.apply_filters()
        
    def on_tag_filter_changed(self, tag):
        """标签筛选变化处理"""
        self.current_tag_filter = tag
        self.apply_filters()
        
    def apply_filters(self):
        """应用筛选条件"""
        self.filtered_prompts = []
        
        for prompt in self.all_prompts:
            # 分类筛选
            if self.current_category_filter != "全部分类":
                prompt_category = getattr(prompt, 'category', '通用')
                if prompt_category != self.current_category_filter:
                    continue
                    
            # 标签筛选
            if self.current_tag_filter != "全部标签":
                prompt_tags = getattr(prompt, 'tags', '')
                if isinstance(prompt_tags, str):
                    prompt_tags = [tag.strip() for tag in prompt_tags.split(',') if tag.strip()]
                else:
                    prompt_tags = list(prompt_tags) if prompt_tags else []
                    
                if self.current_tag_filter not in prompt_tags:
                    continue
                    
            self.filtered_prompts.append(prompt)
            
        self.update_prompt_list()
        
    def update_prompt_list(self):
        """更新提示词列表显示"""
        self.prompt_list.clear()
        
        for prompt in self.filtered_prompts:
            title = getattr(prompt, 'title', '无标题')
            item = QListWidgetItem(title)
            item.setData(Qt.UserRole, prompt)
            self.prompt_list.addItem(item)
            
        # 更新选择计数
        self.on_prompt_selection_changed()
        
    def populate_filters(self):
        """填充筛选选项"""
        if not self.all_prompts:
            return
            
        # 获取所有分类
        categories = set()
        for prompt in self.all_prompts:
            category = getattr(prompt, 'category', '通用')
            categories.add(category)
            
        # 更新分类下拉框
        self.category_combo.clear()
        self.category_combo.addItem("全部分类")
        for category in sorted(categories):
            self.category_combo.addItem(category)
            
        # 获取所有标签
        tags = set()
        for prompt in self.all_prompts:
            prompt_tags = getattr(prompt, 'tags', '')
            if isinstance(prompt_tags, str):
                prompt_tags = [tag.strip() for tag in prompt_tags.split(',') if tag.strip()]
            else:
                prompt_tags = list(prompt_tags) if prompt_tags else []
                
            for tag in prompt_tags:
                tags.add(tag)
                
        # 更新标签下拉框
        self.tag_combo.clear()
        self.tag_combo.addItem("全部标签")
        for tag in sorted(tags):
            self.tag_combo.addItem(tag)
        
    def select_all_prompts(self):
        """全选所有提示词"""
        for i in range(self.prompt_list.count()):
            self.prompt_list.item(i).setSelected(True)
    
    def deselect_all_prompts(self):
        """取消全选所有提示词"""
        for i in range(self.prompt_list.count()):
            self.prompt_list.item(i).setSelected(False)
    
    def on_prompt_selection_changed(self):
        """处理提示词选择变化"""
        selected_items = self.prompt_list.selectedItems()
        selected_count = len(selected_items)
        
        # 更新选择数量显示
        self.selection_count_label.setText(f"已选择: {selected_count} 个提示词")
        
        # 更新按钮状态
        has_selection = selected_count > 0
        self.copy_btn.setEnabled(has_selection)
        self.batch_share_btn.setEnabled(selected_count > 1)
        
        # 如果只选择了一个，显示单个分享链接
        if selected_count == 1:
            item = selected_items[0]
            prompt = item.data(Qt.UserRole)
            if prompt:
                self.selected_prompt = prompt
                share_code = self.generate_share_code(prompt)
                share_link = f"promptshare://{share_code}"
                self.link_text.setText(share_link)
        elif selected_count > 1:
            # 多个选择时，生成批量分享链接
            selected_prompts = []
            for item in selected_items:
                prompt = item.data(Qt.UserRole)
                if prompt:
                    selected_prompts.append(prompt)
            
            if selected_prompts:
                batch_share_code = self.generate_batch_share_code(selected_prompts)
                batch_share_link = f"promptshare://batch/{batch_share_code}"
                self.link_text.setText(batch_share_link)
        else:
            # 没有选择时，清空链接显示
            self.link_text.setText("")
        
    def load_prompts(self):
        """加载提示词列表"""
        if self.model:
            # 只获取未删除的提示词
            self.all_prompts = self.model.get_all_prompts(include_deleted=False)
            
            # 初始化筛选后的提示词列表
            self.filtered_prompts = self.all_prompts.copy()
            
            # 填充筛选选项
            self.populate_filters()
            
            # 更新列表显示
            self.update_prompt_list()
                
    def generate_share_code(self, prompt):
        """生成分享代码"""
        # 将提示词转换为JSON字符串
        prompt_dict = {
            'title': prompt.title,
            'content': prompt.content,
            'tags': prompt.tags if hasattr(prompt, 'tags') else [],
            'category': prompt.category if hasattr(prompt, 'category') else '',
            'media_files': prompt.media_files if hasattr(prompt, 'media_files') else [],
            'type': prompt.type if hasattr(prompt, 'type') else '文本'
        }
        
        # JSON序列化
        json_str = json.dumps(prompt_dict, ensure_ascii=False)
        
        # Base64编码
        share_code = base64.urlsafe_b64encode(json_str.encode('utf-8')).decode('utf-8')
        return share_code
    
    def generate_batch_share_code(self, prompts):
        """生成批量分享代码"""
        # 将多个提示词转换为JSON数组
        prompts_data = []
        for prompt in prompts:
            prompt_dict = {
                'title': prompt.title,
                'content': prompt.content,
                'tags': prompt.tags if hasattr(prompt, 'tags') else [],
                'category': prompt.category if hasattr(prompt, 'category') else '',
                'media_files': prompt.media_files if hasattr(prompt, 'media_files') else [],
                'type': prompt.type if hasattr(prompt, 'type') else '文本'
            }
            prompts_data.append(prompt_dict)
        
        # JSON序列化
        json_str = json.dumps(prompts_data, ensure_ascii=False)
        
        # Base64编码
        share_code = base64.urlsafe_b64encode(json_str.encode('utf-8')).decode('utf-8')
        return share_code
    
    def copy_batch_link(self):
        """复制批量分享链接"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.link_text.toPlainText())
        
        # 显示复制成功提示
        selected_count = len(self.prompt_list.selectedItems())
        QMessageBox.information(self, "复制成功", f"批量分享链接已复制到剪贴板，包含 {selected_count} 个提示词")
    
    def copy_link(self):
        """复制分享链接"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.link_text.toPlainText())
        
        # 显示复制成功提示
        QMessageBox.information(self, "复制成功", "分享链接已复制到剪贴板") 