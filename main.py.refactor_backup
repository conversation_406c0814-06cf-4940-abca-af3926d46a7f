#!/usr/bin/env python3
"""
Prompt Assistant - 重构版桌面应用界面
采用460x800固定尺寸，无标题栏设计
"""
import sys
import os
import base64
import json
import tempfile
import csv
from datetime import datetime
from pathlib import Path

# 添加视频缩略图处理功能 - 放在所有导入前，以避免循环导入问题
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV已成功加载，视频缩略图功能可用")
except ImportError:
    OPENCV_AVAILABLE = False
    print("未检测到OpenCV，视频缩略图功能不可用，请安装: pip install opencv-python")

def extract_video_thumbnail(video_path, max_size=(150, 150)):
    """
    从视频文件提取第一帧作为缩略图
    
    参数:
        video_path: 视频文件路径
        max_size: 缩略图最大尺寸 (宽, 高)
        
    返回:
        QPixmap对象或None（如果失败）
    """
    if not OPENCV_AVAILABLE:
        print(f"无法提取视频缩略图: OpenCV不可用")
        return None
    
    try:
        print(f"尝试提取视频缩略图: {video_path}")
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        
        # 检查是否成功打开
        if not cap.isOpened():
            print(f"无法打开视频文件: {video_path}")
            return None
        
        # 读取第一帧
        ret, frame = cap.read()
        if not ret:
            print(f"无法读取视频第一帧: {video_path}")
            return None
        
        # 释放视频对象
        cap.release()
        
        # 将BGR转换为RGB（OpenCV使用BGR格式）
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 计算缩放比例，保持宽高比
        height, width, _ = frame_rgb.shape
        scale_w = max_size[0] / width
        scale_h = max_size[1] / height
        scale = min(scale_w, scale_h)
        
        # 缩放图像
        if scale < 1:
            new_width = int(width * scale)
            new_height = int(height * scale)
            frame_rgb = cv2.resize(frame_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # 转换为QPixmap
        from PySide6.QtGui import QImage, QPixmap
        height, width, channel = frame_rgb.shape
        bytes_per_line = channel * width
        q_img = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)
        
        print(f"成功提取视频缩略图: {video_path}, 尺寸: {pixmap.width()}x{pixmap.height()}")
        return pixmap
        
    except Exception as e:
        print(f"提取视频缩略图时出错: {e}")
        return None

from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QPushButton, QLabel, QFrame, 
                               QStackedWidget, QListWidget, QTextEdit, 
                               QLineEdit, QScrollArea, QMessageBox, QGraphicsDropShadowEffect,
                               QDialog, QSizePolicy, QLayout, QMenu, QGraphicsColorizeEffect,
                               QSlider, QListWidgetItem, QComboBox, QFileDialog)
from PySide6.QtCore import Qt, QSize, QPoint, QRect, QSettings, QByteArray, QPropertyAnimation, QEasingCurve, QEvent, Signal, Property, QBuffer, QIODevice, QUrl, QTimer
from PySide6.QtGui import QIcon, QPalette, QColor, QFont, QPixmap, QPainter, QPen, QImage, QTransform
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QSvgWidget
from model import PromptModel, Prompt
from create_prompt_dialog import CreatePromptDialog
from flow_layout import FlowLayout
from prompt_history_dialog import PromptHistoryDialog
from services.color_service import ColorService

class CustomTitleBar(QWidget):
    """自定义标题栏组件 (400x50)"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(400, 50)
        self.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        self.init_ui()
        
        # 用于拖拽窗口
        self.drag_position = QPoint()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 0, 10, 0)
        layout.setSpacing(10)
        
        # 标题标签
        self.title_label = QLabel("Prompt收藏助手")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(self.title_label)
        
        layout.addStretch()
        
        # 窗口控制按钮
        self.create_control_buttons(layout)
        
    def create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: #6B7280;
                font-size: 16px;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setStyleSheet(button_style)
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        close_style = button_style.replace("#E5E7EB", "#FEE2E2").replace("#D1D5DB", "#FECACA")
        close_style = close_style.replace("color: #6B7280;", "color: #DC2626;")
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet(close_style)
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def minimize_window(self):
        if self.parent_window:
            self.parent_window.showMinimized()
            
    def close_window(self):
        if self.parent_window:
            self.parent_window.close()
            
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)

class AddIconButton(QPushButton):
    """方形SVG新建按钮 - 三种状态"""
    
    def __init__(self, svg_content, tooltip, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.setFixedSize(36, 36)  # 方形尺寸
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(4)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(1)
        self.shadow_effect.setColor(QColor(59, 130, 246, 40))  # 蓝色阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        # 检查shadow_effect是否仍然有效
        if not hasattr(self, 'shadow_effect') or self.shadow_effect is None:
            return
        try:
            if state == "active":
                # 激活状态：更深的蓝色阴影
                self.shadow_effect.setBlurRadius(6)
                self.shadow_effect.setYOffset(2)
                self.shadow_effect.setColor(QColor(29, 78, 216, 80))
            elif state == "hover":
                # 悬停状态：增强的蓝色阴影
                self.shadow_effect.setBlurRadius(5)
                self.shadow_effect.setYOffset(1)
                self.shadow_effect.setColor(QColor(37, 99, 235, 60))
            else:
                # 默认状态：轻微蓝色阴影
                self.shadow_effect.setBlurRadius(4)
                self.shadow_effect.setYOffset(1)
                self.shadow_effect.setColor(QColor(59, 130, 246, 40))
        except RuntimeError:
            # Qt对象已被删除，忽略此操作
            pass
        
    def update_icon(self, state="default"):
        """更新图标状态"""
        # 三种状态的颜色和背景
        if state == "active":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#1D4ED8"  # 深蓝色背景
        elif state == "hover":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#2563EB"  # 中蓝色背景
        else:
            color = "#FFFFFF"  # 白色图标
            bg_color = "#3B82F6"  # 默认蓝色背景
            
        # 设置按钮样式
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: none;
                border-radius: 6px;
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(20, 20))
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.update_icon("hover")
        self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon("default")
        self.update_shadow("default")
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("active")
            self.update_shadow("active")
        super().mousePressEvent(event)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("hover")
            self.update_shadow("hover")
        super().mouseReleaseEvent(event)

class ThumbnailCarousel(QWidget):
    """缩略图轮播组件，用于在提示词卡片中显示可滑动的缩略图"""
    
    # 自定义信号，点击缩略图时发射，传递当前图片索引和媒体文件列表
    thumbnailClicked = Signal(int, list)
    
    def __init__(self, media_files=None, parent=None):
        super().__init__(parent)
        self.media_files = media_files if media_files else []
        self.current_index = 0
        self.drag_start_x = 0
        self.is_dragging = False
        self.is_hovered = False  # 添加悬停状态变量
        self.animation = None
        
        # 设置固定大小
        self.setFixedSize(80, 80)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        # 主布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # 缩略图容器
        self.thumbnail_container = QWidget()
        self.thumbnail_container.setFixedSize(80, 80)
        self.thumbnail_container.setStyleSheet("""
            QWidget {
                background-color: #F3F4F6;
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
        """)
        
        # 缩略图标签
        self.thumbnail_label = QLabel(self.thumbnail_container)
        self.thumbnail_label.setFixedSize(78, 78)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: none;
            }
        """)
        # 不使用setScaledContents，而是在加载图片时手动按比例缩放
        
        # 左箭头按钮
        self.left_button = QPushButton("<", self.thumbnail_container)
        self.left_button.setFixedSize(20, 20)
        self.left_button.move(5, 30)
        self.left_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.7);
            }
        """)
        self.left_button.clicked.connect(self.show_previous)
        self.left_button.hide()  # 初始隐藏
        
        # 右箭头按钮
        self.right_button = QPushButton(">", self.thumbnail_container)
        self.right_button.setFixedSize(20, 20)
        self.right_button.move(55, 30)
        self.right_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.7);
            }
        """)
        self.right_button.clicked.connect(self.show_next)
        self.right_button.hide()  # 初始隐藏
        
        # 图片索引指示器
        self.indicator_label = QLabel(self.thumbnail_container)
        self.indicator_label.setFixedSize(40, 16)
        self.indicator_label.move(20, 60)
        self.indicator_label.setAlignment(Qt.AlignCenter)
        self.indicator_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
            }
        """)
        self.indicator_label.hide()  # 初始隐藏
        
        self.layout.addWidget(self.thumbnail_container)
        
        # 安装事件过滤器，用于处理鼠标悬停事件
        self.thumbnail_container.installEventFilter(self)
        
        # 如果有媒体文件，加载第一个
        if self.media_files:
            self.load_current_thumbnail()
            self.update_controls_visibility()
            
    def clear(self):
        """清空媒体文件列表"""
        self.media_files = []
        self.current_index = 0
        self.thumbnail_label.setText("📷")
        self.thumbnail_label.setStyleSheet("""
            font-size: 24px;
            background-color: transparent;
            border: none;
        """)
        self.update_controls_visibility()
        
    def add_item(self, media_path):
        """添加单个媒体文件到列表"""
        if media_path == "无媒体文件":
            # 特殊情况：显示提示文本
            self.clear()
            self.thumbnail_label.setText("无媒体文件")
            self.thumbnail_label.setStyleSheet("""
                font-size: 12px;
                background-color: transparent;
                border: none;
                color: #6B7280;
            """)
            return
            
        self.media_files.append(media_path)
        # 如果这是添加的第一个媒体文件，则加载它
        if len(self.media_files) == 1:
            self.current_index = 0
            self.load_current_thumbnail()
        self.update_controls_visibility()
    
    def set_media_files(self, media_files):
        """设置媒体文件列表"""
        self.media_files = media_files if media_files else []
        self.current_index = 0
        self.load_current_thumbnail()
        self.update_controls_visibility()
        
    def load_current_thumbnail(self):
        """加载当前索引的缩略图"""
        if not self.media_files or self.current_index >= len(self.media_files):
            # 没有媒体文件，显示默认图标
            self.thumbnail_label.setText("📷")
            self.thumbnail_label.setStyleSheet("""
                font-size: 24px;
                background-color: transparent;
                border: none;
            """)
            return
            
        media_path = self.media_files[self.current_index]
        file_path = Path(media_path)
        
        if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片文件
            pixmap = QPixmap(media_path)
            if not pixmap.isNull():
                # 获取原始图片尺寸
                orig_width = pixmap.width()
                orig_height = pixmap.height()
                
                # 计算缩放后的尺寸，保持原始比例
                container_size = 78  # 缩略图容器的大小
                
                if orig_width >= orig_height:
                    # 宽图
                    new_width = container_size
                    new_height = int(orig_height * container_size / orig_width)
                else:
                    # 高图
                    new_height = container_size
                    new_width = int(orig_width * container_size / orig_height)
                
                # 按比例缩放图片
                scaled_pixmap = pixmap.scaled(new_width, new_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                
                # 创建一个透明背景的图片，大小与容器相同
                result_pixmap = QPixmap(container_size, container_size)
                result_pixmap.fill(Qt.transparent)
                
                # 在透明背景上绘制缩放后的图片，使其居中
                painter = QPainter(result_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                painter.setRenderHint(QPainter.SmoothPixmapTransform)
                
                # 计算居中位置
                x = (container_size - new_width) // 2
                y = (container_size - new_height) // 2
                
                # 绘制图片
                painter.drawPixmap(x, y, scaled_pixmap)
                painter.end()
                
                # 设置缩略图
                self.thumbnail_label.setPixmap(result_pixmap)
                self.thumbnail_label.setStyleSheet("""
                    background-color: transparent;
                    border: none;
                """)
            else:
                # 加载失败，显示占位符
                self.thumbnail_label.setText("🖼️")
                self.thumbnail_label.setStyleSheet("""
                    font-size: 24px;
                    background-color: transparent;
                    border: none;
                """)
        elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件 - 尝试提取第一帧作为缩略图
            container_size = 78  # 缩略图容器的大小
            result_pixmap = None
            
            print(f"ThumbnailCarousel - 正在处理视频文件: {file_path}, OpenCV可用: {OPENCV_AVAILABLE}")
            if OPENCV_AVAILABLE:
                # 尝试使用OpenCV提取视频缩略图
                print(f"ThumbnailCarousel - 开始提取视频缩略图: {file_path}")
                thumb_pixmap = extract_video_thumbnail(file_path, (container_size, container_size))
                print(f"ThumbnailCarousel - 提取结果: {'成功' if thumb_pixmap and not thumb_pixmap.isNull() else '失败'}")
                
                if thumb_pixmap and not thumb_pixmap.isNull():
                    # 创建一个透明背景的图片，大小与容器相同
                    result_pixmap = QPixmap(container_size, container_size)
                    result_pixmap.fill(Qt.transparent)
                    
                    # 在透明背景上绘制缩略图和播放图标
                    painter = QPainter(result_pixmap)
                    painter.setRenderHint(QPainter.Antialiasing)
                    
                    # 先绘制视频缩略图
                    painter.drawPixmap(0, 0, thumb_pixmap)
                    
                    # 只在悬停时绘制播放按钮
                    if self.is_hovered:
                        # 绘制半透明遮罩
                        painter.setBrush(QColor(0, 0, 0, 100))  # 半透明黑色
                        painter.setPen(Qt.NoPen)
                        painter.drawRect(0, 0, container_size, container_size)
                        
                        # 绘制播放图标
                        painter.setPen(QColor(255, 255, 255))
                        painter.setBrush(QColor(255, 255, 255))
                        
                        # 绘制三角形播放图标
                        play_size = 30
                        center_x = container_size // 2
                        center_y = container_size // 2
                        
                        # 创建三角形的三个点
                        points = [
                            QPoint(center_x - play_size // 3, center_y - play_size // 2),
                            QPoint(center_x + play_size // 2, center_y),
                            QPoint(center_x - play_size // 3, center_y + play_size // 2)
                        ]
                        
                        # 绘制三角形
                        painter.drawPolygon(points)
                    
                    painter.end()
            
            # 如果无法提取缩略图，则使用默认的播放图标
            if result_pixmap is None:
                result_pixmap = QPixmap(container_size, container_size)
                result_pixmap.fill(Qt.transparent)
                
                # 在透明背景上绘制播放图标
                painter = QPainter(result_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # 绘制视频图标背景
                painter.setBrush(QColor(0, 0, 0, 128))
                painter.setPen(Qt.NoPen)
                painter.drawRect(0, 0, container_size, container_size)
                
                # 只在悬停时绘制播放图标
                if self.is_hovered:
                    # 绘制播放图标
                    painter.setPen(QColor(255, 255, 255))
                    painter.setBrush(QColor(255, 255, 255))
                    
                    # 绘制三角形播放图标
                    play_size = 30
                    center_x = container_size // 2
                    center_y = container_size // 2
                    
                    # 创建三角形的三个点
                    points = [
                        QPoint(center_x - play_size // 3, center_y - play_size // 2),
                        QPoint(center_x + play_size // 2, center_y),
                        QPoint(center_x - play_size // 3, center_y + play_size // 2)
                    ]
                    
                    # 绘制三角形
                    painter.drawPolygon(points)
                
                painter.end()
            
            # 设置缩略图
            self.thumbnail_label.setPixmap(result_pixmap)
            self.thumbnail_label.setStyleSheet("""
                background-color: transparent;
                border: none;
            """)
        else:
            # 其他文件
            self.thumbnail_label.setText("📄")
            self.thumbnail_label.setStyleSheet("""
                font-size: 24px;
                background-color: transparent;
                border: none;
            """)
            
        # 更新指示器文本
        self.indicator_label.setText(f"{self.current_index + 1}/{len(self.media_files)}")
        
    def show_next(self):
        """显示下一张缩略图"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index + 1) % len(self.media_files)
        self.load_current_thumbnail()
        
    def show_previous(self):
        """显示上一张缩略图"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index - 1) % len(self.media_files)
        self.load_current_thumbnail()
        
    def update_controls_visibility(self):
        """更新控件可见性"""
        has_multiple_files = len(self.media_files) > 1
        self.left_button.setVisible(has_multiple_files)
        self.right_button.setVisible(has_multiple_files)
        self.indicator_label.setVisible(has_multiple_files)
        
    def update_thumbnail_display(self):
        """更新缩略图显示（重新绘制以反映悬停状态）"""
        if self.media_files:
            self.load_current_thumbnail()
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理鼠标事件"""
        if obj == self.thumbnail_container:
            if event.type() == QEvent.MouseButtonPress and event.button() == Qt.LeftButton:
                # 记录拖动起始位置
                self.drag_start_x = event.position().x()
                self.is_dragging = True
                return True
                
            elif event.type() == QEvent.MouseMove and self.is_dragging:
                # 处理拖动事件
                if len(self.media_files) > 1:
                    delta = event.position().x() - self.drag_start_x
                    if abs(delta) > 30:  # 拖动距离超过阈值
                        if delta > 0:
                            self.show_previous()
                        else:
                            self.show_next()
                        self.is_dragging = False
                return True
                
            elif event.type() == QEvent.MouseButtonRelease:
                # 如果不是拖动，则视为点击
                if self.is_dragging and abs(event.position().x() - self.drag_start_x) < 10:
                    self.thumbnailClicked.emit(self.current_index, self.media_files)
                self.is_dragging = False
                return True
                
            elif event.type() == QEvent.Enter:
                # 鼠标进入时显示控件和播放按钮
                self.is_hovered = True
                self.update_controls_visibility()
                self.update_thumbnail_display()
                return True
                
            elif event.type() == QEvent.Leave:
                # 鼠标离开时隐藏控件和播放按钮
                self.is_hovered = False
                if len(self.media_files) > 1:
                    self.left_button.hide()
                    self.right_button.hide()
                    self.indicator_label.hide()
                self.update_thumbnail_display()
                return True
                
        return super().eventFilter(obj, event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 点击缩略图时发射信号
            self.thumbnailClicked.emit(self.current_index, self.media_files)
        super().mousePressEvent(event)

class PromptCardWidget(QFrame):
    """提示词卡片组件 - 自适应宽度，高度自适应"""
    
    def __init__(self, prompt, parent=None):
        super().__init__(parent)
        self.prompt = prompt
        
        # 初始化颜色服务
        self.color_service = ColorService()
        
        # 根据提示词是否包含媒体文件设置不同的最小高度
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        if has_media:
            self.setMinimumHeight(160)  # 带媒体文件的提示词卡片高度更高
        else:
            self.setMinimumHeight(120)  # 纯文本提示词卡片保持原有高度
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)  # 水平扩展，垂直最小
        
        # 设置基础框架样式
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        
        # 初始化鼠标悬停状态
        self.hovered = False
        # 初始化选中状态
        self.is_selected = False
        
        # 创建右键菜单
        self.context_menu = QMenu(self)
        
        self.setup_ui()
        self.setup_context_menu()
        
        # 设置接受右键菜单事件
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def setup_context_menu(self):
        """设置右键菜单"""
        # 设置菜单样式
        self.context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
                margin: 2px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
                min-width: 120px;
            }
            QMenu::item:selected {
                background-color: #3B82F6;
                color: white;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 创建删除操作
        delete_action = self.context_menu.addAction("删除提示词")
        delete_action.triggered.connect(self.on_delete_action)
        
        # 创建编辑操作
        edit_action = self.context_menu.addAction("编辑提示词")
        edit_action.triggered.connect(self.on_edit_action)
        
        # 创建复制操作
        copy_action = self.context_menu.addAction("复制提示词")
        copy_action.triggered.connect(self.on_copy_action)
        
        # 创建收藏/取消收藏操作
        is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
        favorite_text = "取消收藏" if is_favorited else "收藏提示词"
        favorite_action = self.context_menu.addAction(favorite_text)
        favorite_action.triggered.connect(self.on_favorite_action)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        # 更新收藏菜单项文本
        for action in self.context_menu.actions():
            if action.text() in ["收藏提示词", "取消收藏"]:
                is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
                action.setText("取消收藏" if is_favorited else "收藏提示词")
                break
        
        # 显示菜单 - 使用exec代替exec_以解决弃用警告
        self.context_menu.exec(self.mapToGlobal(position))
    
    def on_delete_action(self):
        """处理删除操作（移至回收站）"""
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area:
            # 如果有选中的卡片，则删除所有选中的卡片
            if content_area.selected_cards:
                content_area.delete_selected_prompts()
            else:
                # 否则只删除当前卡片
                content_area.delete_prompt(self.prompt.id)
    
    def on_edit_action(self):
        """处理编辑操作"""
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
        
        if parent and hasattr(parent, 'content_area') and hasattr(parent.content_area, 'add_prompt'):
            # 显示编辑对话框 - 打开创建提示词对话框并传入当前提示词
            parent.status_bar.set_status("打开编辑提示词对话框...")
            
            # 导入对话框类
            from create_prompt_dialog import CreatePromptDialog
            
            # 创建编辑提示词对话框，传入当前提示词对象
            dialog = CreatePromptDialog(parent, edit_prompt=self.prompt)
            
            # 连接对话框的accepted和rejected信号
            dialog.accepted.connect(lambda: self.on_dialog_accepted(parent, dialog))
            dialog.rejected.connect(lambda: self.on_dialog_rejected(parent, dialog))
            
            # 保存对话框引用并显示（非模态）
            parent.content_area.active_dialogs.append(dialog)
            dialog.show()
            
    def on_dialog_accepted(self, parent, dialog):
        """对话框接受时的处理"""
        parent.status_bar.set_status("提示词更新成功")
        # 刷新提示词列表
        if hasattr(parent.content_area, 'refresh_prompt_list'):
            parent.content_area.refresh_prompt_list()
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_dialog_rejected(self, parent, dialog):
        """对话框拒绝时的处理"""
        parent.status_bar.set_status("取消编辑提示词")
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_history_action(self):
        """处理历史操作"""
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
                
        if parent and hasattr(parent, 'model'):
            # 显示历史对话框
            parent.status_bar.set_status("打开历史版本对话框...")
            
            # 导入历史对话框类
            from prompt_history_dialog import PromptHistoryDialog
            
            # 创建历史版本对话框
            try:
                dialog = PromptHistoryDialog(self.prompt.id, parent)
                
                # 连接对话框的accepted和rejected信号
                dialog.accepted.connect(lambda: self.on_history_dialog_accepted(parent, dialog))
                dialog.rejected.connect(lambda: self.on_history_dialog_rejected(parent, dialog))
                
                # 保存对话框引用并显示（非模态）
                parent.content_area.active_dialogs.append(dialog)
                dialog.show()
            except Exception as e:
                parent.status_bar.set_status(f"显示历史版本出错: {e}")
                
    def on_history_dialog_accepted(self, parent, dialog):
        """历史对话框接受时的处理"""
        parent.status_bar.set_status("历史版本操作已完成")
        # 刷新提示词列表
        if hasattr(parent, 'content_area') and hasattr(parent.content_area, 'refresh_prompt_list'):
            parent.content_area.refresh_prompt_list()
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_history_dialog_rejected(self, parent, dialog):
        """历史对话框拒绝时的处理"""
        parent.status_bar.set_status("取消历史版本操作")
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_copy_action(self):
        """处理复制操作"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.prompt.content)
        
        # 取消选中状态
        self.clear_selection()
        
        # 显示状态提示
        parent = self
        while parent and not hasattr(parent, 'status_bar'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
        
        if parent and hasattr(parent, 'status_bar'):
            parent.status_bar.set_status("提示词内容已复制到剪贴板")
    
    def on_favorite_action(self):
        """处理收藏操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
                
        if parent and parent.model:
            # 调用模型的toggle_favorite方法
            if parent.model.toggle_favorite(self.prompt.id):
                # 更新本地提示词的收藏状态
                self.prompt.is_favorite = 1 if self.prompt.is_favorite == 0 else 0
                
                # 更新收藏按钮状态
                if 'favorite' in self.action_buttons:
                    # 设置按钮状态
                    self.action_buttons['favorite'].setProperty('is_active', self.prompt.is_favorite == 1)
                    # 更新按钮样式
                    self.action_buttons['favorite'].style().polish(self.action_buttons['favorite'])
                    
                # 更新状态栏
                if hasattr(parent, 'status_bar'):
                    status = "已收藏提示词" if self.prompt.is_favorite == 1 else "已取消收藏提示词"
                    parent.status_bar.set_status(status)
                    
                # 刷新提示词列表，以更新收藏状态显示
                if hasattr(parent.content_area, 'refresh_prompt_list'):
                    parent.content_area.refresh_prompt_list()
    
    def on_pin_action(self):
        """处理置顶操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
                
        if parent and parent.model:
            # 调用模型的toggle_pin方法
            if parent.model.toggle_pin(self.prompt.id):
                # 更新本地提示词的置顶状态
                self.prompt.is_pinned = 1 if self.prompt.is_pinned == 0 else 0
                
                # 更新置顶按钮状态
                if 'pin' in self.action_buttons:
                    # 设置按钮状态
                    self.action_buttons['pin'].setProperty('is_active', self.prompt.is_pinned == 1)
                    # 更新按钮样式
                    self.action_buttons['pin'].style().polish(self.action_buttons['pin'])
                    
                # 更新状态栏
                if hasattr(parent, 'status_bar'):
                    status = "已置顶提示词" if self.prompt.is_pinned == 1 else "已取消置顶提示词"
                    parent.status_bar.set_status(status)
                    
                # 刷新提示词列表，以重新排序
                if hasattr(parent.content_area, 'refresh_prompt_list'):
                    parent.content_area.refresh_prompt_list()
                
    def get_content_area(self):
        """获取父级ContentArea对象"""
        parent = self
        while parent and not isinstance(parent, ContentArea):
            parent = parent.parent()
        return parent
    
    def setup_ui(self):
        """设置UI布局"""
        # 背景颜色设置
        self.setStyleSheet("""
            QFrame {
                background-color: white;
            }
        """)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 10, 12, 10)  # 保持左、上、右的内边距，减少底部内边距
        main_layout.setSpacing(8)
        
        # 顶部区域：分类 + 操作按钮
        top_widget = self.create_top_section()
        main_layout.addWidget(top_widget)
        
        # 标题区域
        title_widget = self.create_title_section()
        main_layout.addWidget(title_widget)
        
        # 内容区域
        content_widget = self.create_content_section()
        main_layout.addWidget(content_widget)
        
        # 底部标签区域
        tags_widget = self.create_tags_section()
        main_layout.addWidget(tags_widget)
        
        # 移除这一行，不再添加底部额外空间
        # main_layout.addStretch()
    
    def paintEvent(self, event):
        """重写绘制事件，手动绘制边框"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        
        # 检查置顶状态和媒体状态
        is_pinned = hasattr(self.prompt, 'is_pinned') and self.prompt.is_pinned == 1
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        # 设置画笔
        pen = QPen()
        if self.is_selected:
            # 选中状态：蓝色边框
            pen.setColor(QColor("#3B82F6"))
            pen.setWidth(2)
        elif is_pinned:
            # 置顶状态：浅蓝色边框
            pen.setColor(QColor("#93C5FD"))
            pen.setWidth(2)
        elif has_media:
            # 带媒体文件状态：紫色边框
            pen.setColor(QColor("#C4B5FD"))
            pen.setWidth(2)
        elif self.hovered:
            pen.setColor(QColor("#9ca3af"))  # 悬停时深灰色
            pen.setWidth(2)
        else:
            pen.setColor(QColor("#e0e0e0"))  # 默认浅灰色
            pen.setWidth(1)
        
        painter.setPen(pen)
        
        # 设置背景填充
        if is_pinned and not self.is_selected:
            # 置顶状态：淡蓝色背景
            painter.setBrush(QColor("#F0F7FF"))
        elif has_media and not self.is_selected and not is_pinned:
            # 带媒体文件状态：淡紫色背景
            painter.setBrush(QColor("#F5F3FF"))
        else:
            painter.setBrush(Qt.NoBrush)  # 不填充
        
        # 绘制圆角矩形
        rect = self.rect().adjusted(1, 1, -1, -1)  # 调整绘制区域，避免边缘被裁剪
        painter.drawRoundedRect(rect, 6, 6)
        
    def create_top_section(self):
        """创建顶部区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 左侧分类信息 - 应用颜色服务
        category_name = getattr(self.prompt, 'category', '通用')
        category_color = self.color_service.get_category_color(category_name)
        
        category_label = QLabel(category_name)
        category_label.setStyleSheet(f"""
            QLabel {{
                background-color: {category_color};
                color: #1F2937;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }}
        """)
        layout.addWidget(category_label)
        
        layout.addStretch()
        
        # 右侧操作按钮组
        buttons_widget = self.create_action_buttons()
        layout.addWidget(buttons_widget)
        
        return widget
        
    def create_action_buttons(self):
        """创建操作按钮组"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 定义按钮图标SVG
        button_icons = {
            'favorite': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'favorite_active': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin_active': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="#3B82F6" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="#2563EB" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'copy': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" stroke-width="2"/>
            </svg>''',
            'edit': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'history': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'delete': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
        button_tooltips = {
            'favorite': '收藏',
            'favorite_active': '取消收藏',
            'pin': '置顶', 
            'pin_active': '取消置顶',
            'copy': '复制',
            'edit': '编辑',
            'history': '历史',
            'delete': '删除'
        }
        
        # 存储按钮引用
        self.action_buttons = {}
        
        for key, svg in button_icons.items():
            # 跳过添加active状态的图标，它们只是用于显示激活状态
            if key == 'favorite_active' or key == 'pin_active':
                continue
                
            # 为带状态的按钮创建特殊按钮
            if key == 'favorite':
                # 检查提示词是否已收藏
                is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
                
                # 创建按钮并传递是否已收藏信息
                btn = self.create_favorite_button(
                    button_icons['favorite'], 
                    button_icons['favorite_active'],
                    button_tooltips[key],
                    is_favorited
                )
            elif key == 'pin':
                # 检查提示词是否已置顶
                is_pinned = hasattr(self.prompt, 'is_pinned') and self.prompt.is_pinned == 1
                
                # 创建按钮并传递是否已置顶信息
                btn = self.create_pin_button(
                    button_icons['pin'], 
                    button_icons['pin_active'],
                    button_tooltips[key],
                    is_pinned
                )
            else:
                # 其他按钮正常创建
                btn = self.create_small_icon_button(svg, button_tooltips[key])
                
                # 为按钮添加点击事件
                if key == 'delete':
                    btn.clicked.connect(self.on_delete_action)
                elif key == 'copy':
                    btn.clicked.connect(self.on_copy_action)
                elif key == 'edit':
                    btn.clicked.connect(self.on_edit_action)
                elif key == 'history':
                    btn.clicked.connect(self.on_history_action)
            
            # 存储按钮引用
            self.action_buttons[key] = btn
            layout.addWidget(btn)
            
        return widget
        
    def create_small_icon_button(self, svg_content, tooltip):
        """创建小图标按钮"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
        # 设置图标
        self.set_button_icon(btn, svg_content, "#6B7280")
        
        # 悬停效果
        def on_enter():
            self.set_button_icon(btn, svg_content, "#3B82F6")
        def on_leave():
            self.set_button_icon(btn, svg_content, "#6B7280")
            
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        
        return btn
    
    def create_favorite_button(self, normal_svg, active_svg, tooltip, is_favorited=False):
        """创建收藏按钮（带状态）"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setCursor(Qt.PointingHandCursor)
        
        # 设置自定义属性，用于跟踪状态
        btn.setProperty('is_active', is_favorited)
        
        # 创建效果对象
        effect = QGraphicsColorizeEffect()
        effect.setColor(QColor("#6B7280"))  # 默认灰色
        btn.setGraphicsEffect(effect)
        
        # 设置样式
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
        """)
        
        # 创建SVG渲染器
        normal_renderer = QSvgRenderer(QByteArray(normal_svg.encode()))
        active_renderer = QSvgRenderer(QByteArray(active_svg.encode()))
        
        # 设置初始图标
        icon = QIcon()
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        
        # 根据状态选择渲染器
        if is_favorited:
            active_renderer.render(painter)
        else:
            normal_renderer.render(painter)
        
        painter.end()
        icon.addPixmap(pixmap)
        btn.setIcon(icon)
        btn.setIconSize(QSize(12, 12))
        
        # 监听鼠标事件，处理悬停和点击效果
        def on_enter():
            # 设置悬停效果
            effect = btn.graphicsEffect()
            if effect:
                effect.setColor(QColor("#374151"))
                
        def on_leave():
            # 恢复默认效果
            effect = btn.graphicsEffect()
            if effect:
                effect.setColor(QColor("#6B7280"))
                
        # 点击事件
        def on_clicked():
            # 切换收藏状态
            self.on_favorite_action()
            
        btn.enterEvent = lambda event: on_enter()
        btn.leaveEvent = lambda event: on_leave()
        btn.clicked.connect(on_clicked)
        
        return btn
        
    def create_pin_button(self, normal_svg, active_svg, tooltip, is_pinned=False):
        """创建置顶按钮（带状态）"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setCursor(Qt.PointingHandCursor)
        
        # 设置自定义属性，用于跟踪状态
        btn.setProperty('is_active', is_pinned)
        
        # 创建效果对象
        effect = QGraphicsColorizeEffect()
        effect.setColor(QColor("#6B7280"))  # 默认灰色
        btn.setGraphicsEffect(effect)
        
        # 设置样式
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
        """)
        
        # 创建SVG渲染器
        normal_renderer = QSvgRenderer(QByteArray(normal_svg.encode()))
        active_renderer = QSvgRenderer(QByteArray(active_svg.encode()))
        
        # 设置初始图标
        icon = QIcon()
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        
        # 根据状态选择渲染器
        if is_pinned:
            active_renderer.render(painter)
        else:
            normal_renderer.render(painter)
        
        painter.end()
        icon.addPixmap(pixmap)
        btn.setIcon(icon)
        btn.setIconSize(QSize(12, 12))
        
        # 监听鼠标事件，处理悬停和点击效果
        def on_enter():
            # 设置悬停效果
            effect = btn.graphicsEffect()
            if effect:
                effect.setColor(QColor("#374151"))
                
        def on_leave():
            # 恢复默认效果
            effect = btn.graphicsEffect()
            if effect:
                effect.setColor(QColor("#6B7280"))
                
        # 点击事件
        def on_clicked():
            # 切换置顶状态
            self.on_pin_action()
            
        btn.enterEvent = lambda event: on_enter()
        btn.leaveEvent = lambda event: on_leave()
        btn.clicked.connect(on_clicked)
        
        return btn
        
    def set_button_icon(self, button, svg_content, color):
        """设置按钮图标"""
        if color:
            svg_data = svg_content.replace("currentColor", color)
        else:
            # 如果没有指定颜色，直接使用SVG内容（已经包含颜色信息）
            svg_data = svg_content
            
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(12, 12)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(12, 12))
        
    def create_title_section(self):
        """创建标题区域"""
        title_label = QLabel(self.prompt.title)
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)  # 最多显示2行标题
        return title_label
        
    def create_content_section(self):
        """创建内容区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 检查是否有媒体文件
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        if has_media:
            # 状态B：带媒体布局
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(8)
            
            # 左侧缩略图轮播
            self.thumbnail_carousel = ThumbnailCarousel(self.prompt.media_files, self)
            self.thumbnail_carousel.thumbnailClicked.connect(self.on_thumbnail_clicked)
            layout.addWidget(self.thumbnail_carousel)
            
            # 右侧内容预览
            content_layout = QVBoxLayout()
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(4)
            
            # 添加"内容预览"标签
            preview_label = QLabel("内容预览")
            preview_label.setStyleSheet("""
                QLabel {
                    color: #4B5563;
                    font-size: 11px;
                    font-weight: bold;
                    background: transparent;
                    border: none;
                }
            """)
            content_layout.addWidget(preview_label)
            
            # 添加内容预览
            content_label = self.create_content_preview()
            content_layout.addWidget(content_label)
            
            layout.addLayout(content_layout, 1)  # 1表示拉伸因子，使内容区域占据更多空间
            
        else:
            # 状态A：纯文本布局
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        return widget
        
    def on_thumbnail_clicked(self, index, media_files):
        """处理缩略图点击事件"""
        if not media_files:
            return
            
        media_path = media_files[index]
        file_path = Path(media_path)
        
        print(f"缩略图被点击: {file_path}")
        
        if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件，使用系统默认播放器打开
            print(f"尝试使用系统播放器打开视频: {file_path}")
            success = open_video_with_system_player(file_path)
            if not success:
                print(f"打开视频失败: {file_path}")
                QMessageBox.warning(self.window(), "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
            else:
                print(f"成功打开视频: {file_path}")
        else:
            # 图片文件，打开图片查看对话框
            print(f"打开图片查看对话框: {file_path}")
            dialog = ImageViewerDialog(media_files, index, self.window())
            dialog.exec()
        
    def create_content_preview(self):
        """创建内容预览标签"""
        # 检查是否有媒体文件
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        # 截取内容前100个字符作为预览
        preview_text = self.prompt.content[:100] + "..." if len(self.prompt.content) > 100 else self.prompt.content
        
        content_label = QLabel(preview_text)
        
        if has_media:
            # 带媒体文件的提示词，预览文本可以更紧凑
            content_label.setStyleSheet("""
                QLabel {
                    color: #6B7280;
                    font-size: 11px;
                    line-height: 1.3;
                    background: transparent;
                    border: none;
                }
            """)
            content_label.setWordWrap(True)
            content_label.setMaximumHeight(55)  # 较小的高度
        else:
            # 纯文本提示词，预览文本可以更宽松
            content_label.setStyleSheet("""
                QLabel {
                    color: #6B7280;
                    font-size: 12px;
                    line-height: 1.4;
                    background: transparent;
                    border: none;
                }
            """)
            content_label.setWordWrap(True)
            content_label.setMaximumHeight(60)  # 标准高度
        
        content_label.setAlignment(Qt.AlignTop)
        
        return content_label
        
    def create_tags_section(self):
        """创建标签区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 使用FlowLayout代替HBoxLayout以实现标签自动换行
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 获取标签 - 确保标签正确显示
        tags = []
        
        if hasattr(self.prompt, 'tags'):
            if self.prompt.tags is not None:
                # 如果是字符串，按逗号分割
                if isinstance(self.prompt.tags, str):
                    tags = [tag.strip() for tag in self.prompt.tags.split(',') if tag.strip()]
                else:
                    # 确保是列表类型
                    tags = list(self.prompt.tags)
                    
        # 仅当tags完全不存在或为None时才使用默认标签
        if not tags and not hasattr(self.prompt, 'tags'):
            tags = ['AI助手', '提示词', '创意']
        
        # 最多显示5个标签
        tag_count = 0
        for tag in tags:
            if tag_count >= 5:
                break
                
            tag_label = self.create_tag_label(tag)
            layout.addWidget(tag_label)
            tag_count += 1
            
        # 如果有更多标签，显示+N标签
        if len(tags) > 5:
            more_label = self.create_more_tags_label(len(tags) - 5)
            layout.addWidget(more_label)
            
        layout.addStretch()
        return widget
    

        
    def create_tag_label(self, tag_text):
        """创建单个标签"""
        label = QLabel(tag_text)
        
        # 获取标签颜色（字体色）
        tag_color = self.color_service.get_tag_color(tag_text)
        
        label.setStyleSheet(f"""
            QLabel {{
                background-color: #F3F4F6;
                color: {tag_color};
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }}
        """)
        return label
        
    def create_more_tags_label(self, count):
        """创建显示更多标签的标签"""
        label = QLabel(f"+{count}")
        label.setStyleSheet("""
            QLabel {
                background-color: #F3F4F6;
                color: #6B7280;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        """)
        return label
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.hovered = True
        self.update()  # 触发重绘
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.hovered = False
        self.update()  # 触发重绘
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标点击事件，支持正确的选中状态管理"""
        if event.button() == Qt.LeftButton:
            # 获取当前键盘修饰符
            modifiers = QApplication.keyboardModifiers()
            
            # 检查是否按住了Ctrl键
            if modifiers & Qt.ControlModifier:
                # Ctrl+点击：切换当前卡片的选中状态（多选模式）
                self.is_selected = not self.is_selected
                self.update()  # 触发重绘
                
                # 通知父容器更新选中状态
                parent = self.parent()
                while parent and not hasattr(parent, 'on_card_selection_changed'):
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break
                        
                if parent and hasattr(parent, 'on_card_selection_changed'):
                    parent.on_card_selection_changed(self, self.is_selected)
            else:
                # 普通点击：正确的单选逻辑
                parent = self.parent()
                while parent and not hasattr(parent, 'on_card_selection_changed'):
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break
                
                if parent and hasattr(parent, 'on_card_selection_changed'):
                    # 检查是否在全选状态下
                    is_all_selected = hasattr(parent, 'selected_cards') and len(parent.selected_cards) > 1
                    
                    if self.is_selected and not is_all_selected:
                        # 如果当前卡片已选中且不是全选状态，则取消选中
                        self.is_selected = False
                        self.update()  # 触发重绘
                        parent.on_card_selection_changed(self, False)
                    else:
                        # 如果当前卡片未选中，或者是全选状态下的点击，则选中当前卡片并取消其他所有卡片
                        # 普通点击始终是单选逻辑：清除所有其他卡片，选中当前卡片
                        if hasattr(parent, 'selected_cards'):
                            # 清除所有其他卡片的选中状态
                            for card in parent.selected_cards[:]:  # 使用切片创建副本
                                if card != self:
                                    card.is_selected = False
                                    card.update()
                            parent.selected_cards.clear()
                            parent.selected_cards.append(self)
                            parent.update_selection_status()
                        
                        # 选中当前卡片
                        self.is_selected = True
                        self.update()  # 触发重绘
                        parent.on_card_selection_changed(self, True)
        else:
            super().mousePressEvent(event)
    
    def set_selected(self, selected):
        """设置选中状态"""
        if self.is_selected != selected:
            self.is_selected = selected
            self.update()  # 触发重绘
    
    def clear_selection(self):
        """取消当前卡片的选中状态"""
        if self.is_selected:
            self.is_selected = False
            self.update()  # 触发重绘
            
            # 通知父容器更新选中状态
            parent = self.parent()
            while parent and not hasattr(parent, 'on_card_selection_changed'):
                if hasattr(parent, 'parent'):
                    parent = parent.parent()
                else:
                    break
                    
            if parent and hasattr(parent, 'on_card_selection_changed'):
                parent.on_card_selection_changed(self, False)
            
            # 如果是回收站卡片，还需要从回收站选中列表中移除
            content_area = self.get_content_area()
            if content_area and hasattr(content_area, 'trash_selected_cards'):
                if self in content_area.trash_selected_cards:
                    content_area.trash_selected_cards.remove(self)

class FilterToggleButton(QPushButton):
    """筛选切换按钮 - 通用筛选按钮类"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        
        # 为收藏按钮添加激活状态下的特殊SVG
        if key == "favorite":
            self.active_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        else:
            self.active_svg = None
            
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            if self.key == "favorite":
                # 收藏按钮激活状态：透明背景，黄色边框
                bg_color = "transparent"
                border_color = "#FBBF24"
            else:
                # 其他按钮激活状态：蓝色背景，白色图标
                bg_color = "#3B82F6"
                border_color = "#3B82F6"
            icon_color = "#FFFFFF"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"transparent" if self.is_active and self.key == "favorite" else "#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#F59E0B" if self.is_active and self.key == "favorite" else "#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"transparent" if self.is_active and self.key == "favorite" else "#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        # 收藏按钮激活状态使用特殊SVG
        if self.is_active and self.key == "favorite" and self.active_svg:
            svg_data = self.active_svg
        else:
            svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class ToolboxButton(FilterToggleButton):
    """工具箱按钮 - 带下拉菜单功能"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(svg_content, tooltip, key, parent)
        self.menu = None
        self.parent_content_area = parent
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.showMenu()
        super().mousePressEvent(event)
    
    def showMenu(self):
        """显示下拉菜单"""
        self.menu = QMenu(self)
        self.menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
            }
            QMenu::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 添加分享功能菜单项
        share_action = self.menu.addAction("分享提示词")
        share_action.triggered.connect(self.on_share_action)
        
        # 添加导入功能菜单项
        import_action = self.menu.addAction("获取提示词")
        import_action.triggered.connect(self.on_import_action)
        
        # 添加分隔线
        self.menu.addSeparator()
        
        # 添加导入导出功能菜单项
        export_action = self.menu.addAction("导出提示词")
        export_action.triggered.connect(self.on_export_action)
        
        import_file_action = self.menu.addAction("从文件导入")
        import_file_action.triggered.connect(self.on_import_file_action)
        
        # 添加分隔线
        self.menu.addSeparator()
        
        # 添加清空列表选项
        clear_action = self.menu.addAction("清空列表")
        clear_action.triggered.connect(self.on_clear_action)
        
        # 菜单位置计算：在按钮下方居中
        pos = self.mapToGlobal(QPoint(0, self.height()))
        self.menu.popup(pos)
    
    def on_share_action(self):
        """分享提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.share_prompts()
    
    def on_import_action(self):
        """获取提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.import_prompt()
    
    def on_clear_action(self):
        """清空列表事件处理"""
        if self.parent_content_area:
            self.parent_content_area.clear_all_prompts()
    
    def on_export_action(self):
        """导出提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.export_prompts()
    
    def on_import_file_action(self):
        """从文件导入提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.import_prompts_from_file()

class ViewToggleButton(QPushButton):
    """视图切换按钮 - 28x28px尺寸"""
    
    def __init__(self, svg_content, tooltip, view_type, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.view_type = view_type
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class SvgIconButton(QPushButton):
    """自定义SVG图标按钮"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
        # 设置按钮样式
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
    def update_icon(self):
        """更新图标状态"""
        # 根据状态选择颜色 - 避免白色系，确保在深色背景下清晰可见
        if self.is_active:
            color = "#3B82F6"  # 激活状态：蓝色
        else:
            color = "#6B7280"  # 默认状态：中灰色
            
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(24, 24))
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(8)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        # 检查shadow_effect是否仍然有效
        if not hasattr(self, 'shadow_effect') or self.shadow_effect is None:
            return
        try:
            if state == "active":
                # 激活状态：蓝色阴影，更明显
                self.shadow_effect.setBlurRadius(12)
                self.shadow_effect.setYOffset(3)
                self.shadow_effect.setColor(QColor(59, 130, 246, 100))  # 蓝色阴影
            elif state == "hover":
                # 悬停状态：稍微增强的阴影
                self.shadow_effect.setBlurRadius(10)
                self.shadow_effect.setYOffset(2)
                self.shadow_effect.setColor(QColor(0, 0, 0, 80))  # 深一点的阴影
            else:
                # 默认状态：轻微阴影
                self.shadow_effect.setBlurRadius(8)
                self.shadow_effect.setYOffset(2)
                self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        except RuntimeError:
            # Qt对象已被删除，忽略此操作
            pass
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_icon()
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态：亮灰色，避免白色系
            svg_data = self.svg_content.replace("currentColor", "#9CA3AF")
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            self.setIcon(QIcon(pixmap))
            self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon()
        if self.is_active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        super().leaveEvent(event)

class NavigationBar(QWidget):
    """左侧导航栏组件 (60x800)"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(60, 800)
        self.current_page = "home"
        self.nav_buttons = {}
        self.init_ui()
        
    def get_svg_icons(self):
        """获取SVG图标定义"""
        return {
            "home": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "trash": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "settings": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "help": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
    def init_ui(self):
        self.setStyleSheet("""
            QWidget {
                background-color: #1F2937;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(0)
        
        # Logo区域
        logo_label = QLabel("PA")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: #3B82F6;
                font-size: 18px;
                font-weight: bold;
                background-color: #374151;
                border-radius: 8px;
                padding: 8px;
                margin: 0 10px 30px 10px;
            }
        """)
        layout.addWidget(logo_label)
        
        # 主要导航按钮区域（居中显示）
        main_nav_layout = QVBoxLayout()
        main_nav_layout.setSpacing(15)
        main_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 主要导航项（首页和回收站）
        main_nav_items = [
            ("home", "首页"),
            ("trash", "回收站")
        ]
        
        svg_icons = self.get_svg_icons()
        
        for key, tooltip in main_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            main_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(main_nav_layout)
        layout.addStretch()
        
        # 底部功能按钮区域
        bottom_nav_layout = QVBoxLayout()
        bottom_nav_layout.setSpacing(15)
        bottom_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 底部导航项（设置和帮助）
        bottom_nav_items = [
            ("settings", "设置"),
            ("help", "帮助")
        ]
        
        for key, tooltip in bottom_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            bottom_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(bottom_nav_layout)
        
        # 设置默认选中状态
        self.set_active_button("home")
        
    def create_svg_button(self, svg_content, tooltip, key):
        """创建SVG图标按钮"""
        btn = SvgIconButton(svg_content, tooltip, key, self)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
        """)
        btn.clicked.connect(lambda: self.on_nav_clicked(key))
        return btn
            
    def on_nav_clicked(self, key):
        """导航按钮点击事件"""
        self.set_active_button(key)
        if self.parent_window:
            self.parent_window.switch_page(key)
            
    def set_active_button(self, key):
        """设置活动按钮"""
        self.current_page = key
        for btn_key, btn in self.nav_buttons.items():
            is_active = btn_key == key
            btn.set_active(is_active)

class StatusBar(QWidget):
    """底部状态栏组件 (400x32)"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 32)  # 调整高度从50px到32px，更符合移动端设计规范
        self.default_status = "就绪"
        self.status_timer = QTimer()
        self.status_timer.setSingleShot(True)
        self.status_timer.timeout.connect(self.restore_default_status)
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet("""
            QWidget {
                background-color: #F3F4F6;
                border-top: 1px solid #E5E7EB;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)
        layout.setAlignment(Qt.AlignVCenter)  # 设置整个布局垂直居中
        
        # 状态信息标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        self.status_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 右侧信息
        self.info_label = QLabel("")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        """)
        self.info_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.info_label)
        
    def set_status(self, message, timeout=None):
        """设置状态信息"""
        # 停止当前的定时器
        if self.status_timer.isActive():
            self.status_timer.stop()
        
        # 设置状态文本
        self.status_label.setText(message)
        
        # 如果提供了超时时间，启动定时器
        if timeout is not None:
            self.status_timer.start(timeout)
        
    def set_info(self, info):
        """设置右侧信息"""
        self.info_label.setText(info)
    
    def restore_default_status(self):
        """恢复默认状态"""
        self.status_timer.stop()
        self.status_label.setText(self.default_status)

class ContentArea(QWidget):
    """内容显示区域 (400x718)"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.all_prompts = []  # 所有提示词
        self.active_filters = {
            'favorite': False,  # 收藏筛选
            'category': '全部',  # 分类筛选
            'tags': set(),  # 标签筛选（多选）
            'search': ''  # 搜索关键词筛选
        }
        
        # 初始化颜色服务
        self.color_service = ColorService()
        
        # 从设置中读取排序和视图设置
        settings = QSettings("PromptAssistant", "AppSettings")
        self.current_sort = settings.value("sort_method", "updated_desc")  # 默认排序方式：更新时间降序
        self.current_view = settings.value("view_type", "card")  # 默认视图类型：卡片视图
        self.selected_cards = []  # 当前选中的卡片
        self.category_buttons = {}  # 分类按钮字典
        self.tag_buttons = {}  # 标签按钮字典
        self.filter_buttons = {}  # 筛选按钮字典
        self.active_dialogs = []  # 活动对话框列表
        
        # 设置焦点策略，确保可以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        self.init_ui()
    
    def load_svg_icon(self, svg_file_path, size=24):
        """加载SVG图标"""
        if self.parent_window and hasattr(self.parent_window, 'load_svg_icon'):
            return self.parent_window.load_svg_icon(svg_file_path, size)
        else:
            # 如果没有父窗口或父窗口没有load_svg_icon方法，使用默认实现
            try:
                with open(svg_file_path, 'r', encoding='utf-8') as f:
                    svg_content = f.read()
                return svg_content
            except FileNotFoundError:
                print(f"SVG文件未找到: {svg_file_path}")
                return ""
        
    def get_active_filter_button_style(self):
        """获取激活状态的筛选按钮样式"""
        return """
            QPushButton {
                background-color: #EBF4FF;
                color: #1E40AF;
                border: 1px solid #BFDBFE;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
                min-width: 0px;
            }
            QPushButton:hover {
                background-color: #DBEAFE;
                border-color: #93C5FD;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """
        
    def get_filter_button_style(self):
        """获取默认状态的筛选按钮样式"""
        return """
            QPushButton {
                background-color: white;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
                min-width: 0px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #9CA3AF;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """
    def init_ui(self):
        self.setStyleSheet("""
            QWidget {
                background-color: white;
            }
        """)
        
        # 使用堆叠布局来切换不同页面
        self.stacked_widget = QStackedWidget()
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stacked_widget)
        
        # 创建各个页面
        self.create_pages()
        
    def create_pages(self):
        """创建各个页面"""
        # 首页
        self.home_page = self.create_home_page()
        self.stacked_widget.addWidget(self.home_page)
        
        # 回收站页面
        self.trash_page = self.create_trash_page()
        self.stacked_widget.addWidget(self.trash_page)
        
        # 设置页面
        self.settings_page = self.create_settings_page()
        self.stacked_widget.addWidget(self.settings_page)
        
        # 帮助页面
        self.help_page = self.create_help_page()
        self.stacked_widget.addWidget(self.help_page)
        
    def create_home_page(self):
        """创建首页"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 8, 15, 15)  # 减少顶部间距从15px到8px
        layout.setSpacing(12)  # 减少整体间距从15px到12px
        
        # 导入SearchBox类
        from search_box import SearchBox
        
        # 搜索框和新建按钮区域 - 调整到内容区域顶部
        search_layout = QHBoxLayout()
        
        # 使用自定义搜索框组件
        self.search_input = SearchBox()
        # 连接搜索信号到处理方法
        self.search_input.searchTextChanged.connect(self.on_search_text_changed)
        search_layout.addWidget(self.search_input)
        
        # 创建方形SVG图标按钮替换文本按钮
        add_btn = self.create_add_button()
        search_layout.addWidget(add_btn)
        
        layout.addLayout(search_layout)
        
        # 工具栏
        toolbar_widget = self.create_toolbar()
        layout.addWidget(toolbar_widget)
        
        # 分类筛选栏容器（默认隐藏）
        self.category_filter_bar = QWidget()
        self.category_filter_bar.setFixedHeight(150)  # 从50px增加到150px（3倍）
        self.category_filter_bar.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 0px;
            }
        """)
        
        # 为分类筛选栏设置流式布局容器
        category_scroll = QScrollArea(self.category_filter_bar)
        category_scroll.setWidgetResizable(True)
        category_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        category_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        category_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """)
        
        # 创建分类筛选栏的主布局
        category_main_layout = QVBoxLayout(self.category_filter_bar)
        category_main_layout.setContentsMargins(0, 0, 0, 0)
        category_main_layout.addWidget(category_scroll)
        
        # 创建可滚动的内容容器
        self.category_content_widget = QWidget()
        self.category_filter_layout = FlowLayout(self.category_content_widget)
        self.category_filter_layout.setContentsMargins(12, 8, 12, 8)
        self.category_filter_layout.setSpacing(8)
        
        category_scroll.setWidget(self.category_content_widget)
        
        # 添加分类筛选栏到布局
        layout.addWidget(self.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.tag_filter_area = QScrollArea()
        self.tag_filter_area.setFixedHeight(150)  # 从50px增加到150px（3倍）
        self.tag_filter_area.setWidgetResizable(True)
        self.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 改为按需显示垂直滚动条
        self.tag_filter_area.setStyleSheet("""
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #BAE6FD;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #93C5FD;
            }
        """)
        self.tag_filter_area.hide()
        
        # 标签筛选栏内容容器 - 使用流式布局
        tag_filter_widget = QWidget()
        self.tag_filter_layout = FlowLayout(tag_filter_widget)
        self.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.tag_filter_layout.setSpacing(6)
        self.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.tag_filter_area)
        
        # 初始状态下隐藏分类筛选栏
        self.category_filter_bar.hide()
        
        # 创建堆叠容器来切换列表视图和卡片视图
        self.view_stack = QStackedWidget()
        
        # 列表视图
        self.prompt_list = QListWidget()
        self.prompt_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #F9FAFB;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
        """)
        self.view_stack.addWidget(self.prompt_list)
        
        # 卡片视图
        self.card_view = self.create_card_view()
        self.view_stack.addWidget(self.card_view)
        
        # 根据保存的设置显示相应视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
        
        layout.addWidget(self.view_stack)
        
        return page
        
    def create_trash_page(self):
        """创建回收站页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        # 回收站页面标题和操作区域
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 10)
        
        title = QLabel("回收站")
        title.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
            }
        """)
        header_layout.addWidget(title)
        header_layout.addStretch()
        
        # 恢复选中提示词按钮
        restore_selected_btn = QPushButton()
        restore_selected_btn.setCursor(Qt.PointingHandCursor)
        restore_selected_btn.setToolTip("恢复选中提示词")
        # 添加SVG图标
        restore_icon = self.load_svg_icon("icons/restore.svg", 20)
        restore_selected_btn.setIcon(restore_icon)
        restore_selected_btn.setIconSize(QSize(20, 20))
        restore_selected_btn.setFixedSize(32, 32)
        restore_selected_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #EBF5FF;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        restore_selected_btn.clicked.connect(self.restore_selected_prompts)
        header_layout.addWidget(restore_selected_btn)
        
        # 彻底删除选中提示词按钮
        delete_selected_btn = QPushButton()
        delete_selected_btn.setCursor(Qt.PointingHandCursor)
        delete_selected_btn.setToolTip("彻底删除选中提示词")
        # 添加SVG图标
        delete_icon = self.load_svg_icon("icons/delete-forever.svg", 20)
        delete_selected_btn.setIcon(delete_icon)
        delete_selected_btn.setIconSize(QSize(20, 20))
        delete_selected_btn.setFixedSize(32, 32)
        delete_selected_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #FEE2E2;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        delete_selected_btn.clicked.connect(self.permanently_delete_selected_prompts)
        header_layout.addWidget(delete_selected_btn)
        
        # 清空回收站按钮
        clear_btn = QPushButton()
        clear_btn.setCursor(Qt.PointingHandCursor)
        clear_btn.setToolTip("清空回收站")
        # 添加SVG图标
        clear_icon = self.load_svg_icon("icons/trash-empty.svg", 20)
        clear_btn.setIcon(clear_icon)
        clear_btn.setIconSize(QSize(20, 20))
        clear_btn.setFixedSize(32, 32)
        clear_btn.setStyleSheet("""
            QPushButton {
                background: transparent;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #FEE2E2;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        clear_btn.clicked.connect(self.empty_trash)
        header_layout.addWidget(clear_btn)
        
        layout.addLayout(header_layout)
        
        # 分割线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setFrameShadow(QFrame.Sunken)
        line.setStyleSheet("background-color: #E5E7EB;")
        layout.addWidget(line)
        
        # 创建回收站内容显示区域
        self.trash_scroll_area = QScrollArea()
        self.trash_scroll_area.setWidgetResizable(True)
        self.trash_scroll_area.setFrameShape(QFrame.NoFrame)
        self.trash_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        self.trash_scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """)
        
        self.trash_content_widget = QWidget()
        self.trash_content_layout = QVBoxLayout(self.trash_content_widget)
        self.trash_content_layout.setContentsMargins(0, 0, 0, 0)
        self.trash_content_layout.setSpacing(10)
        self.trash_content_layout.setAlignment(Qt.AlignTop)
        
        self.trash_scroll_area.setWidget(self.trash_content_widget)
        layout.addWidget(self.trash_scroll_area)
        
        # 空状态提示（默认隐藏）
        self.trash_empty_label = QLabel("回收站为空")
        self.trash_empty_label.setAlignment(Qt.AlignCenter)
        self.trash_empty_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 14px;
                padding: 40px;
            }
        """)
        self.trash_content_layout.addWidget(self.trash_empty_label)
        
        # 初始化回收站选中的卡片列表
        self.trash_selected_cards = []
        
        # 加载回收站内容
        self.load_trash_content()
        
        return page
        
    def load_trash_content(self):
        """加载回收站内容"""
        # 清空现有内容
        while self.trash_content_layout.count():
            item = self.trash_content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
        
        # 清空选中卡片列表
        self.trash_selected_cards = []
        
        # 获取已删除的提示词
        if hasattr(self.parent_window, 'model'):
            deleted_prompts = self.parent_window.model.get_deleted_prompts()
            
            if not deleted_prompts:
                # 如果回收站为空，显示空状态提示
                self.trash_empty_label = QLabel("回收站为空")
                self.trash_empty_label.setAlignment(Qt.AlignCenter)
                self.trash_empty_label.setStyleSheet("""
                    QLabel {
                        color: #9CA3AF;
                        font-size: 14px;
                        padding: 40px;
                    }
                """)
                self.trash_content_layout.addWidget(self.trash_empty_label)
            else:
                # 创建卡片容器（类似于主页的卡片视图）
                self.trash_cards_container = QWidget()
                self.trash_cards_layout = QVBoxLayout(self.trash_cards_container)
                self.trash_cards_layout.setContentsMargins(0, 0, 0, 0)
                self.trash_cards_layout.setSpacing(10)
                self.trash_cards_layout.setAlignment(Qt.AlignTop)
                
                # 为每个已删除的提示词创建卡片
                for prompt in deleted_prompts:
                    card = TrashPromptCard(prompt, self)
                    # 连接卡片的选中状态变化信号
                    card.clicked.connect(lambda checked=False, c=card: self.on_trash_card_clicked(c))
                    self.trash_cards_layout.addWidget(card)
                
                self.trash_content_layout.addWidget(self.trash_cards_container)
    
    def create_trash_item(self, prompt):
        """创建回收站项目"""
        # 创建TrashPromptCard
        card = TrashPromptCard(prompt, self)
        # 使用ContentArea的颜色服务
        card.color_service = self.color_service
        
        # 设置卡片样式
        card.setStyleSheet("""
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
            }
            QFrame:hover {
                border-color: #D1D5DB;
                background-color: #F3F4F6;
            }
        """)
        
        return card
        
    def on_trash_thumbnail_clicked(self, index, media_files):
        """处理回收站中缩略图点击事件"""
        if not media_files:
            return
            
        media_path = media_files[index]
        file_path = Path(media_path)
        
        print(f"回收站缩略图被点击: {file_path}")
        
        if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件，使用系统默认播放器打开
            print(f"尝试使用系统播放器打开视频: {file_path}")
            success = open_video_with_system_player(file_path)
            if not success:
                print(f"打开视频失败: {file_path}")
                QMessageBox.warning(self.window(), "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
            else:
                print(f"成功打开视频: {file_path}")
        else:
            # 图片文件，打开图片查看对话框
            print(f"打开图片查看对话框: {file_path}")
            dialog = ImageViewerDialog(media_files, index, self.window())
            dialog.exec()
    
    def restore_prompt(self, prompt_id):
        """从回收站恢复提示词"""
        reply = QMessageBox.question(
            self,
            "确认恢复",
            "确定要恢复这个提示词吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            if hasattr(self.parent_window, 'model'):
                try:
                    self.parent_window.model.restore_prompt(prompt_id)
                    
                    # 刷新回收站和主页
                    self.load_trash_content()
                    self.refresh_prompt_list()
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status("提示词已恢复")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "恢复失败",
                        f"恢复提示词时出错: {str(e)}"
                    )
    
    def permanently_delete_prompt(self, prompt_id):
        """永久删除提示词"""
        reply = QMessageBox.warning(
            self,
            "确认永久删除",
            "确定要永久删除这个提示词吗？此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            if hasattr(self.parent_window, 'model'):
                try:
                    self.parent_window.model.permanently_delete_prompt(prompt_id)
                    
                    # 刷新回收站
                    self.load_trash_content()
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status("提示词已永久删除")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "删除失败",
                        f"永久删除提示词时出错: {str(e)}"
                    )
    
    def empty_trash(self):
        """清空回收站"""
        if hasattr(self.parent_window, 'model'):
            deleted_prompts = self.parent_window.model.get_deleted_prompts()
            if not deleted_prompts:
                self.parent_window.status_bar.set_status("回收站已经是空的")
                return
                
            reply = QMessageBox.warning(
                self,
                "确认清空回收站",
                f"确定要永久删除回收站中的所有 {len(deleted_prompts)} 个提示词吗？此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    # 永久删除所有回收站中的提示词
                    for prompt in deleted_prompts:
                        self.parent_window.model.permanently_delete_prompt(prompt.id)
                    
                    # 刷新回收站
                    self.load_trash_content()
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status("回收站已清空")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "清空失败",
                        f"清空回收站时出错: {str(e)}"
                    )
        
    def create_settings_page(self):
        """创建增强的设置页面"""
        from enhanced_settings_help import SettingsPage

        settings_page = SettingsPage(self.parent_window)
        
        # 连接设置变更信号到主窗口
        if self.parent_window and hasattr(self.parent_window, 'on_settings_changed'):
            settings_page.settingsChanged.connect(self.parent_window.on_settings_changed)
        
        return settings_page
        
    def create_setting_item(self, title, description):
        """创建设置项"""
        widget = QFrame()
        widget.setStyleSheet("""
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 12px;
            }
            QFrame:hover {
                background-color: #F3F4F6;
            }
        """)
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        """)
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return widget
        
    def create_help_page(self):
        """创建增强的帮助页面"""
        from enhanced_settings_help import HelpPage
        
        help_page = HelpPage(self.parent_window)
        
        return help_page
        
    def switch_to_page(self, page_key):
        """切换到指定页面"""
        if page_key == "home":
            self.stacked_widget.setCurrentWidget(self.home_page)
            # 刷新提示词列表
            self.refresh_prompt_list()
        elif page_key == "trash":
            self.stacked_widget.setCurrentWidget(self.trash_page)
            # 加载回收站内容
            self.load_trash_content()
        elif page_key == "settings":
            self.stacked_widget.setCurrentWidget(self.settings_page)
        elif page_key == "help":
            self.stacked_widget.setCurrentWidget(self.help_page)
            
    def create_toolbar(self):
        """创建工具栏 - 无边框设计"""
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(40)
        toolbar_widget.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-radius: 6px;
                border: none;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(12, 0, 12, 0)
        toolbar_layout.setSpacing(8)
        
        # 左侧工具栏标识
        toolbar_label = QLabel("工具栏")
        toolbar_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        """)
        toolbar_layout.addWidget(toolbar_label)
        
        # 添加筛选按钮组
        filter_buttons_layout = QHBoxLayout()
        filter_buttons_layout.setSpacing(4)
        
        # 准备 SVG 图标内容
        filter_icons = self.get_filter_icons()
        
        # 创建三个筛选按钮
        self.favorite_filter_btn = FilterToggleButton(
            filter_icons['favorite'], "收藏筛选", "favorite", self
        )
        self.favorite_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("favorite"))
        filter_buttons_layout.addWidget(self.favorite_filter_btn)
        
        self.category_filter_btn = FilterToggleButton(
            filter_icons['category'], "分类筛选", "category", self
        )
        self.category_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("category"))
        filter_buttons_layout.addWidget(self.category_filter_btn)
        
        self.tag_filter_btn = FilterToggleButton(
            filter_icons['tag'], "标签筛选", "tag", self
        )
        self.tag_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("tag"))
        filter_buttons_layout.addWidget(self.tag_filter_btn)
        
        # 添加工具箱按钮
        self.toolbox_btn = ToolboxButton(
            filter_icons['toolbox'], "工具箱", "toolbox", self
        )
        filter_buttons_layout.addWidget(self.toolbox_btn)
        
        toolbar_layout.addLayout(filter_buttons_layout)
        toolbar_layout.addStretch()
        
        # 创建排序按钮
        self.sort_button = SortButton(self)
        toolbar_layout.addWidget(self.sort_button)
        
        # 右侧单个视图切换按钮
        self.view_toggle_btn = self.create_single_view_toggle_button()
        toolbar_layout.addWidget(self.view_toggle_btn)
        
        return toolbar_widget
    
    def create_single_view_toggle_button(self):
        """创建单个视图切换按钮"""
        # 卡片视图图标（默认显示）
        card_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 列表视图图标
        list_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 创建按钮，存储两个图标
        btn = QPushButton()
        btn.setFixedSize(32, 28)
        btn.card_svg = card_svg
        btn.list_svg = list_svg
        btn.current_view = "card"  # 默认卡片视图
        
        # 设置初始样式和图标
        self.update_toggle_button_style(btn)
        
        # 连接点击事件
        btn.clicked.connect(self.toggle_view)
        
        return btn
    
    def toggle_view(self):
        """切换视图（循环切换）"""
        # 切换视图类型
        if self.current_view == "card":
            self.current_view = "list"
        else:
            self.current_view = "card"
        
        # 保存视图设置
        settings = QSettings("PromptAssistant", "AppSettings")
        settings.setValue("view_type", self.current_view)
        
        # 更新按钮显示
        self.update_toggle_button_style(self.view_toggle_btn)
        
        # 切换显示的视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if self.current_view == 'card' else '列表'}视图")
    
    def create_card_view(self):
        """创建卡片视图容器 - 一行一个卡片，自适应宽度"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)     # 启用垂直滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """)
        
        # 创建卡片容器
        self.card_container = QWidget()
        self.card_layout = QVBoxLayout(self.card_container)
        self.card_layout.setContentsMargins(15, 15, 15, 15)  # 外部容器边距
        self.card_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.card_layout.setAlignment(Qt.AlignTop)
        
        # 创建垂直布局来放置卡片（一行一个）
        self.cards_list_widget = QWidget()
        self.cards_list_layout = QVBoxLayout(self.cards_list_widget)
        self.cards_list_layout.setContentsMargins(0, 0, 0, 0)
        self.cards_list_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.cards_list_layout.setAlignment(Qt.AlignTop)
        
        self.card_layout.addWidget(self.cards_list_widget)
        self.card_layout.addStretch()
        
        scroll_area.setWidget(self.card_container)
        return scroll_area

    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        self.update_view_buttons()
        
        # 切换显示的视图
        if view_type == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        # 保存视图设置到QSettings中
        settings = QSettings("PromptAssistant", "AppSettings")
        settings.setValue("view_type", view_type)
        
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if view_type == 'card' else '列表'}视图")
    
    def update_toggle_button_style(self, btn):
        """更新切换按钮样式和图标"""
        # 根据当前视图选择图标和提示文本
        if self.current_view == "card":
            svg_content = btn.card_svg
            tooltip = "当前：卡片视图 (点击切换到列表视图)"
            bg_color = "#3B82F6"  # 蓝色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#3B82F6"
        else:
            svg_content = btn.list_svg
            tooltip = "当前：列表视图 (点击切换到卡片视图)"
            bg_color = "#10B981"  # 绿色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#10B981"
        
        # 设置按钮样式
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 6px;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.current_view == "card" else "#059669"};
                border-color: {"#2563EB" if self.current_view == "card" else "#059669"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.current_view == "card" else "#047857"};
            }}
        """)
        
        # 设置工具提示
        btn.setToolTip(tooltip)
        
        # 更新图标
        self.set_button_svg_icon(btn, svg_content, icon_color)
    
    def set_button_svg_icon(self, button, svg_content, color):
        """设置按钮SVG图标"""
        
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(16, 16))

    def update_card_view(self, prompts):
        """更新卡片视图显示 - 一行一个卡片"""
        # 保存已选中卡片的ID
        selected_prompt_ids = [card.prompt.id for card in self.selected_cards] if self.selected_cards else []
        
        # 清除现有卡片
        if hasattr(self, 'cards_list_layout'):
            for i in reversed(range(self.cards_list_layout.count())):
                child = self.cards_list_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
        
        # 清空选中卡片列表
        self.selected_cards = []
        
        # 创建卡片，每行一个
        for prompt in prompts:
            # 创建卡片外围容器 - 用于实现边框效果
            container = QFrame()
            container.setFrameShape(QFrame.Box)
            container.setFrameShadow(QFrame.Plain)
            container.setLineWidth(1)
            # 使用中性色边框
            container.setStyleSheet("""
                QFrame {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    padding: 0px;
                    background-color: white;
                }
                QFrame:hover {
                    border: 1px solid #9ca3af;
                }
            """)
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
            container_layout.setSpacing(0)
            
            # 创建卡片内容
            card = PromptCardWidget(prompt, self)
            # 使用ContentArea的颜色服务
            card.color_service = self.color_service
            # 卡片自身不显示边框
            card.setStyleSheet("QFrame { border: none; background-color: white; }")
            card.setFrameShape(QFrame.NoFrame)
            
            # 恢复选中状态
            if prompt.id in selected_prompt_ids:
                card.set_selected(True)
                self.selected_cards.append(card)
                
            container_layout.addWidget(card)
            
            # 添加到卡片列表布局
            self.cards_list_layout.addWidget(container)
        
        # 更新状态栏显示选中数量
        self.update_selection_status()

    def update_view_buttons(self):
        """更新视图按钮状态"""
        self.card_view_btn.set_active(self.current_view == "card")
        self.list_view_btn.set_active(self.current_view == "list")

    def create_add_button(self):
        """创建方形SVG新建按钮"""
        # 定义新建按钮的SVG图标
        add_svg = '''<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 12H19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        btn = AddIconButton(add_svg, "新建提示词", self)
        btn.clicked.connect(self.add_prompt)
        return btn
        
    def add_prompt(self):
        """添加新提示词"""
        from create_prompt_dialog import CreatePromptDialog
        
        if self.parent_window:
            self.parent_window.status_bar.set_status("打开新建提示词对话框...")
            
            # 创建新建提示词对话框
            dialog = CreatePromptDialog(self.parent_window)
            
            # 连接对话框的accepted和rejected信号
            dialog.accepted.connect(lambda: self.on_dialog_accepted(dialog, "提示词创建成功"))
            dialog.rejected.connect(lambda: self.on_dialog_rejected(dialog, "取消创建提示词"))
            
            # 保存对话框引用并显示（非模态）
            self.active_dialogs.append(dialog)
            dialog.show()
    
    def on_dialog_accepted(self, dialog, message):
        """对话框接受时的处理"""
        if self.parent_window:
            self.parent_window.status_bar.set_status(message)
            # 刷新提示词列表
            self.refresh_prompt_list()
        
        # 从活动对话框列表中移除
        if dialog in self.active_dialogs:
            self.active_dialogs.remove(dialog)
    
    def on_dialog_rejected(self, dialog, message):
        """对话框拒绝时的处理"""
        if self.parent_window:
            self.parent_window.status_bar.set_status(message)
        
        # 从活动对话框列表中移除
        if dialog in self.active_dialogs:
            self.active_dialogs.remove(dialog)
    
    def get_filter_icons(self):
        """获取筛选按钮的 SVG 图标"""
        return {
            'favorite': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'tag': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0136 20.9135 12.7709 21.0141C12.5282 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4818 21.1148 11.2391 21.0141C10.9964 20.9135 10.7757 20.766 10.59 20.58L2 12V2H12L20.59 10.59C20.9625 10.9647 21.1716 11.4716 21.1716 12C21.1716 12.5284 20.9625 13.0353 20.59 13.41V13.41Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 7H7.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'toolbox': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.7 6.3a1 1 0 000 1.4l1.6 1.6a1 1 0 001.4 0l3.77-3.77a6 6 0 01-7.94 7.94l-6.91 6.91a2.12 2.12 0 01-3-3l6.91-6.91a6 6 0 017.94-7.94l-3.76 3.76z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
    
    def apply_filters_and_update_views(self):
        """应用筛选并更新视图 - 所有筛选逻辑的中心"""
        # 从完整列表创建副本
        filtered_prompts = self.all_prompts[:]
        
        # 首先过滤掉已删除的提示词（仅在主页显示未删除的）
        filtered_prompts = [prompt for prompt in filtered_prompts if not hasattr(prompt, 'is_deleted') or prompt.is_deleted == 0]
        
        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选（多选支持）
        if self.active_filters.get('tags') and len(self.active_filters['tags']) > 0:
            selected_tags = self.active_filters['tags']
            filtered_prompts = [prompt for prompt in filtered_prompts
                              if hasattr(prompt, 'tags') and prompt.tags and 
                              selected_tags.issubset(set(prompt.tags))]
        
        # 检查搜索关键词筛选
        search_keyword = self.active_filters.get('search', '').strip()
        if search_keyword:
            filtered_prompts = [
                prompt for prompt in filtered_prompts
                if (
                    search_keyword.lower() in prompt.title.lower() or
                    search_keyword.lower() in prompt.content.lower() or
                    any(search_keyword.lower() in tag.lower() for tag in prompt.tags if tag)
                )
            ]
        
        # 应用排序
        filtered_prompts = self.sort_prompts(filtered_prompts)
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if search_keyword:
                self.parent_window.status_bar.set_info(f'搜索"{search_keyword}": {filtered_count}/{total_count} 个提示词')
            elif self.active_filters.get('favorite', False):
                self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
            else:
                self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
    
    def apply_sort(self):
        """应用当前排序方式"""
        # 直接调用apply_filters_and_update_views，它会处理排序逻辑
        self.apply_filters_and_update_views()
    
    def sort_prompts(self, prompts_list):
        """根据当前排序方式对提示词列表进行排序"""
        # 加载排序设置
        settings = QSettings("PromptAssistant", "AppSettings")
        self.current_sort = settings.value("sort_method", "updated_desc")
        
        # 首先，根据是否置顶将列表分为两部分：置顶和非置顶
        pinned_prompts = []
        unpinned_prompts = []
        
        for prompt in prompts_list:
            # 检查是否有置顶属性，并根据置顶状态分类
            is_pinned = hasattr(prompt, 'is_pinned') and prompt.is_pinned == 1
            if is_pinned:
                pinned_prompts.append(prompt)
            else:
                unpinned_prompts.append(prompt)
        
        # 对置顶和非置顶部分分别进行排序
        def sort_by_method(prompts):
            if self.current_sort == "updated_desc":
                # 按更新时间降序（最新的在前）
                prompts.sort(key=lambda p: p.updated_at if hasattr(p, 'updated_at') and p.updated_at else "", reverse=True)
            elif self.current_sort == "updated_asc":
                # 按更新时间升序（最旧的在前）
                prompts.sort(key=lambda p: p.updated_at if hasattr(p, 'updated_at') and p.updated_at else "")
            elif self.current_sort == "created_desc":
                # 按创建时间降序（最新的在前）
                prompts.sort(key=lambda p: p.created_at if hasattr(p, 'created_at') and p.created_at else "", reverse=True)
            elif self.current_sort == "created_asc":
                # 按创建时间升序（最旧的在前）
                prompts.sort(key=lambda p: p.created_at if hasattr(p, 'created_at') and p.created_at else "")
            elif self.current_sort == "title_asc":
                # 按标题升序（A-Z）
                prompts.sort(key=lambda p: p.title.lower())
            elif self.current_sort == "title_desc":
                # 按标题降序（Z-A）
                prompts.sort(key=lambda p: p.title.lower(), reverse=True)
            elif self.current_sort == "category":
                # 按类别排序
                prompts.sort(key=lambda p: (p.category.lower() if p.category else "", p.title.lower()))
            return prompts
            
        # 分别对置顶和非置顶部分进行排序
        pinned_prompts = sort_by_method(pinned_prompts)
        unpinned_prompts = sort_by_method(unpinned_prompts)
        
        # 合并结果：置顶部分在前，非置顶部分在后
        return pinned_prompts + unpinned_prompts

    def on_category_button_clicked(self, category_name):
        """分类按钮点击处理器"""
        # 更新分类筛选状态
        self.active_filters['category'] = category_name
        
        # 更新按钮状态
        self.update_category_button_states()
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def update_category_button_states(self):
        """更新分类按钮的激活状态"""
        current_category = self.active_filters.get('category', '全部')
        
        for category_name, button in self.category_buttons.items():
            if category_name == current_category:
                # 激活状态样式
                if category_name == "全部":
                    button.setStyleSheet('''
                        QPushButton {
                            background-color: #3B82F6;
                            color: white;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #2563EB;
                            border-color: #2563EB;
                        }
                    ''')
                else:
                    button.setStyleSheet('''
                        QPushButton {
                            background-color: #EBF4FF;
                            color: #1E40AF;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #DBEAFE;
                            border-color: #2563EB;
                        }
                    ''')
            else:
                # 默认状态样式
                button.setStyleSheet('''
                    QPushButton {
                        background-color: white;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        border-radius: 6px;
                        padding: 6px 12px;
                        font-size: 12px;
                        font-weight: 400;
                        min-width: 0px;
                    }
                    QPushButton:hover {
                        background-color: #F3F4F6;
                        border-color: #9CA3AF;
                    }
                ''')

    def populate_tag_filters(self):
        """动态填充标签筛选按钮"""
        
        # 清除现有按钮和引用
        self.tag_buttons.clear()
        while self.tag_filter_layout.count():
            item = self.tag_filter_layout.takeAt(0)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
        
        # 从所有提示词中提取唯一标签
        all_tags = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'tags') and prompt.tags:
                for tag in prompt.tags:
                    if tag and tag.strip():
                        all_tags.add(tag.strip())
        
        # 转换为排序列表
        unique_tags = sorted(list(all_tags))
        
        # 添加"全部"按钮
        all_button = QPushButton("全部")
        all_button.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        all_button.adjustSize()
        all_button.clicked.connect(lambda: self.on_tag_button_clicked("全部"))
        self.tag_buttons["全部"] = all_button
        self.tag_filter_layout.addWidget(all_button)
        
        # 添加各标签按钮
        for tag in unique_tags:
            button = QPushButton(tag)
            button.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
            button.adjustSize()
            button.clicked.connect(lambda checked, t=tag: self.on_tag_button_clicked(t))
            self.tag_buttons[tag] = button
            self.tag_filter_layout.addWidget(button)
        
        # 更新按钮状态
        self.update_tag_button_states()

    def on_tag_button_clicked(self, tag_name):
        """标签按钮点击处理器（支持多选）"""
        if tag_name == '全部':
            # 点击"全部"按钮，清空所有选中的标签
            self.active_filters['tags'].clear()
        else:
            # 处理普通标签按钮的多选逻辑
            if tag_name in self.active_filters['tags']:
                # 如果标签已选中，则取消选择
                self.active_filters['tags'].remove(tag_name)
            else:
                # 如果标签未选中，则添加到选中集合
                self.active_filters['tags'].add(tag_name)
        
        # 更新按钮状态
        self.update_tag_button_states()
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def update_tag_button_states(self):
        """更新标签按钮的激活状态（支持多选）"""
        selected_tags = self.active_filters.get('tags', set())
        
        # 更新所有标签按钮的状态
        for tag_name, button in self.tag_buttons.items():
            if tag_name == '全部':
                # "全部"按钮：当没有其他标签选中时激活
                if len(selected_tags) == 0:
                    button.setStyleSheet(self.get_active_filter_button_style())
                else:
                    button.setStyleSheet(self.get_filter_button_style())
            else:
                # 普通标签按钮：根据是否在选中集合中设置状态
                if tag_name in selected_tags:
                    button.setStyleSheet(self.get_active_filter_button_style())
                else:
                    button.setStyleSheet(self.get_filter_button_style())

    def on_filter_button_clicked(self, filter_key):
        """筛选按钮点击处理方法"""
        print(f"筛选按钮被点击: {filter_key}")
        
        # 切换按钮激活状态和筛选器状态
        if filter_key == "favorite":
            current_state = self.favorite_filter_btn.is_active
            new_state = not current_state
            self.favorite_filter_btn.set_active(new_state)
            self.active_filters['favorite'] = new_state
            
            # 不再需要完全刷新，直接应用筛选即可，前面更新了收藏状态会自动同步
            # 应用筛选并更新视图
            self.apply_filters_and_update_views()
            
            # 更新状态栏的收藏提示
            if self.parent_window:
                if new_state:
                    self.parent_window.status_bar.set_status("已激活收藏筛选")
                else:
                    self.parent_window.status_bar.set_status("已取消收藏筛选")
            
        elif filter_key == "category":
            # 切换分类筛选栏的显示/隐藏
            current_state = self.category_filter_btn.is_active
            new_state = not current_state
            
            # 如果要显示分类筛选框，先隐藏标签筛选框
            if new_state:
                # 隐藏标签筛选框
                self.tag_filter_btn.set_active(False)
                self.tag_filter_area.hide()
                # 重置标签筛选状态
                if self.active_filters['tags']:
                    self.active_filters['tags'].clear()
                    self.update_tag_button_states()
                    self.apply_filters_and_update_views()
            
            self.category_filter_btn.set_active(new_state)
            self.category_filter_bar.setVisible(new_state)
            
            # 如果隐藏分类筛选框，重置分类筛选为"全部"
            if not new_state:
                self.active_filters['category'] = '全部'
                self.update_category_button_states()
                self.apply_filters_and_update_views()
            
            # 当激活分类筛选时，重置标签筛选
            if new_state:
                self.on_tag_button_clicked('全部')
            
        elif filter_key == "tag":
            # 切换标签筛选栏的显示/隐藏
            current_state = self.tag_filter_btn.is_active
            new_state = not current_state
            
            # 如果要显示标签筛选框，先隐藏分类筛选框
            if new_state:
                # 隐藏分类筛选框并重置其状态
                self.category_filter_btn.set_active(False)
                self.category_filter_bar.hide()
                # 重置分类筛选状态
                if self.active_filters['category'] != '全部':
                    self.active_filters['category'] = '全部'
                    self.update_category_button_states()
                    self.apply_filters_and_update_views()
            
            # 更新标签筛选按钮状态和显示
            self.tag_filter_btn.set_active(new_state)
            self.tag_filter_area.setVisible(new_state)
            
            # 如果隐藏标签筛选框，重置标签筛选
            if not new_state:
                if self.active_filters['tags']:
                    self.active_filters['tags'].clear()
                    self.update_tag_button_states()
                    self.apply_filters_and_update_views()
        # 更新状态栏显示
        if self.parent_window:
            filter_names = {
                "favorite": "收藏",
                "category": "分类", 
                "tag": "标签"
            }
            if filter_key == "favorite":
                status = "激活" if self.active_filters['favorite'] else "取消"
            elif filter_key == "category":
                status = "显示" if self.category_filter_btn.is_active else "隐藏"
            else:
                status = "激活" if getattr(getattr(self, f"{filter_key}_filter_btn"), "is_active") else "取消"
            self.parent_window.status_bar.set_status(f"{status}{filter_names[filter_key]}筛选")

    def get_all_categories(self):
        """获取所有唯一分类"""
        categories = set()
        for prompt in self.all_prompts:
            if prompt.category:
                categories.add(prompt.category)
        return sorted(list(categories))
    
    def populate_category_filters(self):
        """动态填充分类筛选按钮"""
        # 获取所有分类
        categories = self.get_all_categories()
        
        # 清除现有按钮
        while self.category_filter_layout.count():
            item = self.category_filter_layout.takeAt(0)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
        
        # 从所有提示词中提取唯一的分类名称
        categories = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'category') and prompt.category and prompt.category.strip():
                categories.add(prompt.category.strip())
        
        # 排序分类列表
        sorted_categories = sorted(list(categories))
        
        # 创建"全部"按钮
        all_btn = self.create_category_button("全部", is_all_button=True)
        all_btn.clicked.connect(lambda: self.on_category_filter_clicked("全部"))
        self.category_buttons["全部"] = all_btn
        self.category_filter_layout.addWidget(all_btn)
        
        # 为每个分类创建按钮
        for category in sorted_categories:
            btn = self.create_category_button(category)
            btn.clicked.connect(lambda checked, cat=category: self.on_category_filter_clicked(cat))
            self.category_buttons[category] = btn
            self.category_filter_layout.addWidget(btn)
        
    def create_category_button(self, category_name, is_all_button=False):
        """创建分类按钮"""
        btn = QPushButton(category_name)
        btn.setFixedHeight(32)
        btn.category_name = category_name
        btn.is_active = False
        
        # 设置按钮自适应宽度
        btn.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        btn.adjustSize()
        
        # 设置按钮样式
        if is_all_button:
            # "全部"按钮的特殊样式
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: 1px solid #3B82F6;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 500;
                    min-width: 0px;
                }
                QPushButton:hover {
                    background-color: #2563EB;
                    border-color: #2563EB;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            """)
            btn.is_active = True  # "全部"按钮默认激活
        else:
            # 普通分类按钮样式
            btn.setStyleSheet("""
                QPushButton {
                    background-color: white;
                    color: #374151;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 400;
                    min-width: 0px;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                    border-color: #9CA3AF;
                }
                QPushButton:pressed {
                    background-color: #E5E7EB;
                }
            """)
        
        return btn
    
    def on_category_filter_clicked(self, category_name):
        """分类筛选按钮点击处理 - 支持取消选择"""
        print(f"分类筛选被点击: {category_name}")
        
        # 检查当前选中的分类，支持取消选择
        current_category = self.active_filters.get('category', '全部')
        
        # 如果点击的是已经选中的分类（且不是"全部"），则取消选择，回到"全部"
        if category_name == current_category and category_name != "全部":
            category_name = "全部"
        
        # 更新筛选状态
        self.active_filters['category'] = category_name
        
        # 更新按钮状态 - 使用category_buttons字典直接访问
        for btn_category_name, btn in self.category_buttons.items():
            is_selected = btn_category_name == category_name
            btn.is_active = is_selected
            
            if is_selected:
                if category_name == "全部":
                    # "全部"按钮激活样式
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #3B82F6;
                            color: white;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #2563EB;
                            border-color: #2563EB;
                        }
                    """)
                else:
                    # 普通分类按钮激活样式
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #EBF4FF;
                            color: #1E40AF;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #DBEAFE;
                            border-color: #2563EB;
                        }
                    """)
            else:
                # 未激活状态样式
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: white;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        border-radius: 6px;
                        padding: 6px 12px;
                        font-size: 12px;
                        font-weight: 400;
                        min-width: 0px;
                    }
                    QPushButton:hover {
                        background-color: #F3F4F6;
                        border-color: #9CA3AF;
                    }
                """)
        
        # 应用分类筛选
        self.apply_category_filter(category_name)
        
        # 更新状态栏
        if self.parent_window:
            if category_name == "全部":
                self.parent_window.status_bar.set_status("显示所有分类")
            else:
                self.parent_window.status_bar.set_status(f"筛选分类: {category_name}")
    
    def apply_category_filter(self, category_name):
        """应用分类筛选"""
        if category_name == "全部":
            # 显示所有提示词（但仍要考虑其他筛选器）
            filtered_prompts = self.all_prompts[:]
        else:
            # 按分类筛选
            filtered_prompts = [prompt for prompt in self.all_prompts 
                              if hasattr(prompt, 'category') and prompt.category == category_name]
        
        # 如果收藏筛选也激活，需要进一步筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if category_name == "全部":
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
            else:
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"{category_name} (收藏): {filtered_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"{category_name}: {filtered_count} 个提示词")

    def refresh_prompt_list(self):
        """刷新提示词列表"""
        if self.parent_window:
            # 加载数据（从数据库中获取未删除的提示词）
            self.parent_window.load_data()
            
    def share_prompts(self):
        """打开分享提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = SharePromptDialog(self.parent_window, self.parent_window.model)
            dialog.exec()
    
    def share_prompts_batch(self):
        """打开批量分享提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = SharePromptDialog(self.parent_window, self.parent_window.model)
            dialog.exec()
    
    def import_prompts_batch(self):
        """打开批量获取提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ImportPromptDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("批量提示词导入成功")
    
    def import_prompt(self):
        """打开获取提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ImportPromptDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("提示词导入成功")
    
    def export_prompts(self):
        """打开导出提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ExportPromptDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("提示词导出成功")
    
    def import_prompts_from_file(self):
        """打开从文件导入提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ImportFileDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("文件导入成功")

    def on_card_selection_changed(self, card, is_selected):
        """处理卡片选中状态变化"""
        if is_selected and card not in self.selected_cards:
            self.selected_cards.append(card)
        elif not is_selected and card in self.selected_cards:
            self.selected_cards.remove(card)
            
        # 更新状态栏显示
        self.update_selection_status()
    
    def update_selection_status(self):
        """更新状态栏显示选中卡片数量"""
        if self.parent_window and hasattr(self.parent_window, 'status_bar'):
            if self.selected_cards:
                self.parent_window.status_bar.set_info(f"已选择 {len(self.selected_cards)} 个提示词")
            else:
                # 恢复显示总数或筛选信息
                total_count = len(self.all_prompts)
                
                # 计算当前筛选后显示的卡片数量
                filtered_count = 0
                if hasattr(self, 'cards_list_layout'):
                    filtered_count = self.cards_list_layout.count()
                
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
    
    def clear_card_selections(self):
        """清除所有卡片的选中状态"""
        for card in self.selected_cards:
            card.set_selected(False)
        self.selected_cards.clear()
        self.update_selection_status()
        
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 按下Escape键清除所有选择
        if event.key() == Qt.Key_Escape:
            # 在主页
            if hasattr(self, 'selected_cards') and self.selected_cards:
                self.clear_card_selections()
            # 更新状态栏
            if self.parent_window:
                self.parent_window.status_bar.set_status("已取消所有选择")
            # 在回收站
            elif hasattr(self, 'trash_selected_cards') and self.trash_selected_cards:
                self.clear_trash_selections()
                # 更新状态栏
                if self.parent_window:
                    self.parent_window.status_bar.set_status("已取消所有选择")
        # 按下Delete键删除选中的提示词
        elif event.key() == Qt.Key_Delete:
            # 在主页
            if hasattr(self, 'selected_cards') and self.selected_cards:
                self.delete_selected_prompts()
            # 在回收站
            elif hasattr(self, 'trash_selected_cards') and self.trash_selected_cards:
                self.permanently_delete_selected_prompts()
        # 按下Ctrl+A全选
        elif event.key() == Qt.Key_A and event.modifiers() & Qt.ControlModifier:
            # 检查当前页面
            current_page = self.stacked_widget.currentWidget()
            
            # 根据当前页面判断
            if current_page == self.home_page:
                if hasattr(self, 'selected_cards') and hasattr(self, 'cards_list_layout'):
                    # 先清除现有选择
                    self.clear_card_selections()
                    
                    # 全选主页卡片
                    for i in range(self.cards_list_layout.count()):
                        item = self.cards_list_layout.itemAt(i)
                        if item and item.widget():
                            container = item.widget()
                            # 主页卡片被包装在容器中，需要找到实际的卡片
                            if hasattr(container, 'layout'):
                                container_layout = container.layout()
                                if container_layout and container_layout.count() > 0:
                                    card_item = container_layout.itemAt(0)
                                    if card_item and card_item.widget():
                                        card = card_item.widget()
                                        if hasattr(card, 'set_selected') and hasattr(card, 'is_selected'):
                                            card.is_selected = True
                                            card.set_selected(True)
                                            # 通知父容器卡片选择状态变化
                                            self.on_card_selection_changed(card, True)
                                            # 强制刷新卡片UI
                                            card.update()
                    
                    # 强制刷新整个卡片列表区域
                    if hasattr(self, 'cards_list_layout'):
                        cards_widget = self.cards_list_layout.parent()
                        if cards_widget:
                            cards_widget.update()
                    
                    # 更新状态栏
                    if self.parent_window:
                        self.parent_window.status_bar.set_status("已全选所有提示词")
            elif current_page == self.trash_page:
                if hasattr(self, 'trash_selected_cards') and hasattr(self, 'trash_cards_layout'):
                    self.select_all_trash_cards()
        else:
            super().keyPressEvent(event)
    
    def delete_selected_prompts(self):
        """删除所有选中的提示词"""
        if not self.selected_cards:
            return
        
        # 确认是否要删除
        if self.parent_window:
            prompt_count = len(self.selected_cards)
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {prompt_count} 个提示词吗？删除的提示词将移至回收站。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    # 收集所有选中卡片的提示词ID
                    prompt_ids = [card.prompt.id for card in self.selected_cards]
                    
                    # 从数据库中删除所有选中的提示词
                    if hasattr(self.parent_window, 'model'):
                        for prompt_id in prompt_ids:
                            self.parent_window.model.delete_prompt(prompt_id)
                        
                        # 清空选中状态
                        self.selected_cards.clear()
                        
                        # 更新界面
                        self.refresh_prompt_list()
                        
                        # 显示状态提示
                        self.parent_window.status_bar.set_status(f"已将 {prompt_count} 个提示词移至回收站")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "删除失败",
                        f"删除提示词时出错: {str(e)}"
                    )

    def delete_prompt(self, prompt_id):
        """删除单个提示词"""
        # 确认是否要删除
        reply = QMessageBox.question(
            self, 
            "确认删除",
            "确定要删除选中的提示词吗？删除的提示词将移至回收站。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 从数据库中删除提示词
                if hasattr(self.parent_window, 'model'):
                    self.parent_window.model.delete_prompt(prompt_id)
                    
                    # 更新界面
                    self.refresh_prompt_list()
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status("提示词已移至回收站")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "删除失败",
                    f"删除提示词时出错: {str(e)}"
                )

    def clear_all_prompts(self):
        """清空所有提示词"""
        # 确认对话框
        reply = QMessageBox.question(
            self, 
            "确认清空", 
            "确定要清空所有提示词吗？\n\n提示词将被移动到回收站，您可以在回收站中恢复或永久删除它们。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # 默认No按钮
        )
        
        if reply == QMessageBox.Yes:
            if self.parent_window and hasattr(self.parent_window, 'model'):
                # 调用模型清空提示词
                success = self.parent_window.model.clear_all_prompts()
                
                if success:
                    # 刷新提示词列表
                    self.refresh_prompt_list()
                    self.parent_window.status_bar.set_status("所有提示词已清空")
                else:
                    QMessageBox.warning(self, "操作失败", "清空提示词失败，请重试")
        else:
            self.parent_window.status_bar.set_status("已取消清空操作")

    def on_search_text_changed(self, text):
        """
        处理搜索框文本变化
        
        参数:
            text (str): 搜索框中的文本
        """
        # 更新搜索关键词筛选条件
        self.active_filters['search'] = text
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def restore_selected_prompts(self):
        """恢复所有选中的提示词"""
        if not self.trash_selected_cards:
            self.parent_window.status_bar.set_status("请先选择要恢复的提示词")
            return
            
        prompt_count = len(self.trash_selected_cards)
        reply = QMessageBox.question(
            self,
            "确认恢复",
            f"确定要恢复选中的 {prompt_count} 个提示词吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 收集所有选中卡片的提示词ID
                prompt_ids = [card.prompt.id for card in self.trash_selected_cards]
                
                # 恢复所有选中的提示词
                if hasattr(self.parent_window, 'model'):
                    for prompt_id in prompt_ids:
                        self.parent_window.model.restore_prompt(prompt_id)
                    
                    # 清空选中状态
                    self.trash_selected_cards.clear()
                    
                    # 刷新回收站和主页
                    self.load_trash_content()
                    self.refresh_prompt_list()
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status(f"已恢复 {prompt_count} 个提示词")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "恢复失败",
                    f"恢复提示词时出错: {str(e)}"
                )
    
    def permanently_delete_selected_prompts(self):
        """永久删除所有选中的提示词"""
        if not self.trash_selected_cards:
            self.parent_window.status_bar.set_status("请先选择要删除的提示词")
            return
            
        prompt_count = len(self.trash_selected_cards)
        reply = QMessageBox.warning(
            self,
            "确认永久删除",
            f"确定要永久删除选中的 {prompt_count} 个提示词吗？此操作不可撤销！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 收集所有选中卡片的提示词ID
                prompt_ids = [card.prompt.id for card in self.trash_selected_cards]
                
                # 永久删除所有选中的提示词
                if hasattr(self.parent_window, 'model'):
                    for prompt_id in prompt_ids:
                        self.parent_window.model.permanently_delete_prompt(prompt_id)
                    
                    # 清空选中状态
                    self.trash_selected_cards.clear()
                    
                    # 刷新回收站
                    self.load_trash_content()
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status(f"已永久删除 {prompt_count} 个提示词")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "删除失败",
                    f"永久删除提示词时出错: {str(e)}"
                )

    def clear_trash_selections(self):
        """清除回收站中所有卡片的选中状态"""
        for card in self.trash_selected_cards:
            card.set_selected(False)
        self.trash_selected_cards.clear()
        # 更新状态栏
        if self.parent_window and hasattr(self.parent_window, 'status_bar'):
            self.parent_window.status_bar.set_info("")
    
    def select_all_trash_cards(self):
        """全选回收站中的所有卡片"""
        # 获取所有回收站卡片
        if hasattr(self, 'trash_cards_layout'):
            # 遍历所有卡片并选中
            for i in range(self.trash_cards_layout.count()):
                item = self.trash_cards_layout.itemAt(i)
                if item and item.widget():
                    card = item.widget()
                    if hasattr(card, 'set_selected') and hasattr(card, 'is_selected'):
                        card.is_selected = True
                        card.set_selected(True)
                        if card not in self.trash_selected_cards:
                            self.trash_selected_cards.append(card)
                        # 强制刷新卡片UI
                        card.update()
            
            # 强制刷新整个回收站卡片列表区域
            if hasattr(self, 'trash_cards_layout'):
                trash_widget = self.trash_cards_layout.parent()
                if trash_widget:
                    trash_widget.update()
            
            # 更新状态栏
            if self.parent_window and hasattr(self.parent_window, 'status_bar'):
                self.parent_window.status_bar.set_info(f"已选择 {len(self.trash_selected_cards)} 个提示词")
                self.parent_window.status_bar.set_status("已全选回收站中的所有提示词")

    def on_trash_card_clicked(self, card):
        """处理回收站卡片点击事件"""
        # 更新状态栏显示
        if hasattr(self, 'trash_selected_cards') and self.trash_selected_cards:
            self.parent_window.status_bar.set_info(f"已选择 {len(self.trash_selected_cards)} 个提示词")
        else:
            self.parent_window.status_bar.set_info("")

class SortButton(QPushButton):
    """排序按钮 - 带下拉菜单"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_content_area = parent
        self.setFixedSize(32, 28)
        
        # 定义排序图标
        self.sort_icons = {
            'updated_desc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 4H16M3 8H12M3 12H9M13 12L17 8M17 8L21 12M17 8V20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 更新时间降序（最新的在前）
            'updated_asc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 4H16M3 8H12M3 12H9M13 20L17 16M17 16L21 20M17 16V4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 更新时间升序（最旧的在前）
            'created_desc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 10H13M21 6H13M21 14H13M21 18H13M5 10V18M5 18L9 14M5 18L1 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 创建时间降序（最新的在前）
            'created_asc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 10H13M21 6H13M21 14H13M21 18H13M5 14V6M5 6L9 10M5 6L1 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 创建时间升序（最旧的在前）
            'title_asc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 5L14 19M14 5L18 9M14 5L10 9M5 15V9M5 9L3 11M5 9L7 11" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 标题升序（A-Z）
            'title_desc': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 19L14 5M14 19L18 15M14 19L10 15M5 9V15M5 15L3 13M5 15L7 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',  # 标题降序（Z-A）
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 20H8M8 20H12M8 20V4M16 12H12M16 12H20M16 12V4M16 12V20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''   # 按类别
        }
        
        # 排序方式和提示文字
        self.sort_tooltips = {
            'updated_desc': '按更新时间（最新的在前）',
            'updated_asc': '按更新时间（最旧的在前）',
            'created_desc': '按创建时间（最新的在前）',
            'created_asc': '按创建时间（最旧的在前）',
            'title_asc': '按标题排序（A-Z）',
            'title_desc': '按标题排序（Z-A）',
            'category': '按类别排序'
        }
        
        # 加载设置中的排序方式，默认为'updated_desc'
        settings = QSettings("PromptAssistant", "AppSettings")
        self.current_sort = settings.value("sort_method", "updated_desc")
        
        # 设置样式和图标
        self.update_style()
        self.update_icon()
        
        # 设置工具提示
        self.setToolTip(self.sort_tooltips[self.current_sort])
        
        # 创建菜单
        self.menu = None
        
        # 连接点击事件
        self.clicked.connect(self.show_menu)
        
        # 标记是否正在处理排序变更
        self.is_processing = False
        
    def update_style(self):
        """更新按钮样式"""
        # 设置按钮样式
        self.setStyleSheet("""
            QPushButton {
                background-color: #F9FAFB;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #9CA3AF;
            }
            QPushButton:pressed {
                background-color: #E5E7EB;
            }
        """)
        
    def update_icon(self):
        """更新按钮图标"""
        svg_content = self.sort_icons[self.current_sort]
        
        # 设置图标颜色
        svg_data = svg_content.replace("currentColor", "#4B5563")
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def setup_menu(self):
        """设置下拉菜单"""
        # 设置菜单样式
        self.menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
                margin: 2px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
                min-width: 180px;
            }
            QMenu::item:selected {
                background-color: #3B82F6;
                color: white;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 添加排序选项
        sort_options = [
            ('updated_desc', '按更新时间（最新的在前）'),
            ('updated_asc', '按更新时间（最旧的在前）'),
            ('created_desc', '按创建时间（最新的在前）'),
            ('created_asc', '按创建时间（最旧的在前）'),
            ('title_asc', '按标题排序（A-Z）'),
            ('title_desc', '按标题排序（Z-A）'),
            ('category', '按类别排序')
        ]
        
        for sort_key, sort_text in sort_options:
            action = self.menu.addAction(sort_text)
            action.setData(sort_key)
            
            # 设置图标
            icon_svg = self.sort_icons[sort_key]
            svg_data = icon_svg.replace("currentColor", "#4B5563")
            
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(16, 16)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            action.setIcon(QIcon(pixmap))
            
            # 标记当前选中的排序方式
            if sort_key == self.current_sort:
                # 设置字体为粗体
                font = action.font()
                font.setBold(True)
                action.setFont(font)
        
        # 连接菜单项点击事件
        self.menu.triggered.connect(self.on_sort_method_changed)
        
    def show_menu(self):
        """显示排序菜单"""
        try:
            # 每次显示菜单前都创建新菜单
            if self.menu:
                self.menu.deleteLater()
                
            self.menu = QMenu(self)
            self.menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 5px;
                    margin: 2px;
                }
                QMenu::item {
                    padding: 6px 25px 6px 10px;
                    border-radius: 4px;
                    margin: 2px 5px;
                    color: #1F2937;
                    font-size: 12px;
                    min-width: 180px;
                }
                QMenu::item:selected {
                    background-color: #3B82F6;
                    color: white;
                    font-weight: bold;
                }
                QMenu::separator {
                    height: 1px;
                    background-color: #E5E7EB;
                    margin: 5px 10px;
                }
            """)
            
            # 添加排序选项
            sort_options = [
                ('updated_desc', '按更新时间（最新的在前）'),
                ('updated_asc', '按更新时间（最旧的在前）'),
                ('created_desc', '按创建时间（最新的在前）'),
                ('created_asc', '按创建时间（最旧的在前）'),
                ('title_asc', '按标题排序（A-Z）'),
                ('title_desc', '按标题排序（Z-A）'),
                ('category', '按类别排序')
            ]
            
            for sort_key, sort_text in sort_options:
                action = self.menu.addAction(sort_text)
                action.setData(sort_key)
                
                # 设置图标
                icon_svg = self.sort_icons[sort_key]
                svg_data = icon_svg.replace("currentColor", "#4B5563")
                
                renderer = QSvgRenderer()
                renderer.load(svg_data.encode())
                
                pixmap = QPixmap(16, 16)
                pixmap.fill(Qt.transparent)
                
                painter = QPainter(pixmap)
                renderer.render(painter)
                painter.end()
                
                action.setIcon(QIcon(pixmap))
                
                # 标记当前选中的排序方式
                if sort_key == self.current_sort:
                    # 设置字体为粗体
                    font = action.font()
                    font.setBold(True)
                    action.setFont(font)
            
            # 连接菜单项点击事件
            self.menu.triggered.connect(self.on_sort_method_changed)
            
            # 显示菜单
            self.menu.exec(self.mapToGlobal(QPoint(0, self.height())))
        except Exception as e:
            print(f"显示排序菜单时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def on_sort_method_changed(self, action):
        """排序方式变更处理"""
        # 防止重复处理
        if self.is_processing:
            return
            
        try:
            self.is_processing = True
            
            sort_key = action.data()
            if sort_key != self.current_sort:
                self.current_sort = sort_key
                
                # 保存排序设置
                settings = QSettings("PromptAssistant", "AppSettings")
                settings.setValue("sort_method", self.current_sort)
                settings.sync()  # 确保设置被立即保存
                
                # 更新图标和工具提示
                self.update_icon()
                self.setToolTip(self.sort_tooltips[self.current_sort])
                
                # 应用排序
                if self.parent_content_area:
                    QApplication.processEvents()  # 处理可能的挂起事件
                    self.parent_content_area.apply_sort()
                    
                    # 更新状态栏
                    if (hasattr(self.parent_content_area, 'parent_window') and 
                        self.parent_content_area.parent_window):
                        self.parent_content_area.parent_window.status_bar.set_status(
                            f"已按{self.sort_tooltips[self.current_sort]}排序")
        except Exception as e:
            print(f"处理排序方法变更时出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            self.is_processing = False

class TrashPromptCard(PromptCardWidget):
    """回收站中的提示词卡片，继承自PromptCardWidget，但不显示基础功能按钮"""
    
    # 自定义信号
    clicked = Signal(bool)
    
    def __init__(self, prompt, parent=None):
        super().__init__(prompt, parent)
        
    def create_top_section(self):
        """创建顶部区域 - 分类和标题在一行"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 创建分类和标题的组合标签
        category = getattr(self.prompt, 'category', '通用')
        title = self.prompt.title
        
        combined_label = QLabel(f"{category} | {title}")
        combined_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        combined_label.setWordWrap(True)
        combined_label.setMaximumHeight(40)  # 最多显示2行标题
        
        layout.addWidget(combined_label, 1)  # 1表示伸展因子
        
        # 不添加操作按钮
        
        return widget
        
    def create_title_section(self):
        """重写标题区域方法，返回空组件，避免重复显示标题"""
        empty_widget = QWidget()
        empty_widget.setFixedHeight(0)  # 设置高度为0，不占用空间
        return empty_widget
    
    def create_action_buttons(self):
        """重写方法，不创建操作按钮"""
        return None
    
    def setup_context_menu(self):
        """设置右键菜单 - 只包含恢复和永久删除选项"""
        # 设置菜单样式
        self.context_menu = QMenu(self)
        self.context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
                margin: 2px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
                min-width: 120px;
            }
            QMenu::item:selected {
                background-color: #3B82F6;
                color: white;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 创建恢复操作
        restore_action = self.context_menu.addAction("恢复提示词")
        restore_action.triggered.connect(self.on_restore_action)
        
        # 创建永久删除操作
        delete_action = self.context_menu.addAction("永久删除")
        delete_action.triggered.connect(self.on_permanent_delete_action)
    
    def on_restore_action(self):
        """处理恢复操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area:
            content_area.restore_prompt(self.prompt.id)
    
    def on_permanent_delete_action(self):
        """处理永久删除操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area:
            content_area.permanently_delete_prompt(self.prompt.id)
            
    def mousePressEvent(self, event):
        """重写鼠标点击事件，处理正确的选中状态管理"""
        super(PromptCardWidget, self).mousePressEvent(event)  # 调用基类的方法，而不是直接父类
        
        # 发射点击信号
        self.clicked.emit(True)
        
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area and hasattr(content_area, 'trash_selected_cards'):
            # 如果按住Ctrl键，则切换选中状态（多选模式）
            if event.modifiers() & Qt.ControlModifier:
                self.is_selected = not self.is_selected
                self.set_selected(self.is_selected)
                
                # 更新选中列表
                if self.is_selected and self not in content_area.trash_selected_cards:
                    content_area.trash_selected_cards.append(self)
                elif not self.is_selected and self in content_area.trash_selected_cards:
                    content_area.trash_selected_cards.remove(self)
            # 普通点击：正确的单选逻辑
            else:
                # 检查是否在全选状态下
                is_all_selected = len(content_area.trash_selected_cards) > 1
                
                if self.is_selected and not is_all_selected:
                    # 如果当前卡片已选中且不是全选状态，则取消选中
                    self.is_selected = False
                    self.set_selected(False)
                    
                    # 从选中列表中移除
                    if self in content_area.trash_selected_cards:
                        content_area.trash_selected_cards.remove(self)
                else:
                    # 如果当前卡片未选中，或者是全选状态下的点击，则选中当前卡片并取消其他所有卡片
                    # 普通点击始终是单选逻辑：清除所有其他卡片，选中当前卡片
                    # 清除所有其他卡片的选中状态
                    for card in content_area.trash_selected_cards[:]:  # 使用切片创建副本
                        if card != self:
                            card.is_selected = False
                            card.update()
                    content_area.trash_selected_cards.clear()
                    content_area.trash_selected_cards.append(self)
                    
                    # 选中当前卡片
                    self.is_selected = True
                    self.set_selected(True)
            
            # 更新状态栏
            if hasattr(content_area.parent_window, 'status_bar'):
                if content_area.trash_selected_cards:
                    content_area.parent_window.status_bar.set_info(f"已选择 {len(content_area.trash_selected_cards)} 个提示词")
                else:
                    content_area.parent_window.status_bar.set_info("")

class PromptAssistantRedesigned(QMainWindow):
    """重构版主窗口"""

    def __init__(self):
        super().__init__()
        # 初始化设置状态
        self.debug_mode = False
        self.performance_mode = False
        self.settings = QSettings("PromptAssistant", "AppSettings")
        self.init_ui()
    
    def load_svg_icon(self, svg_file_path, size=24):
        """加载SVG图标并转换为QIcon"""
        try:
            from pathlib import Path
            icon_path = Path(svg_file_path)
            if icon_path.exists():
                renderer = QSvgRenderer(str(icon_path))
                pixmap = QPixmap(size, size)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                renderer.render(painter)
                painter.end()
                return QIcon(pixmap)
            else:
                print(f"SVG图标文件不存在: {svg_file_path}")
                return QIcon()
        except Exception as e:
            print(f"加载SVG图标失败: {e}")
            return QIcon()

    def init_ui(self):
        """初始化用户界面"""
        self.model = PromptModel("sqlite")
        self.setup_ui()
        self.load_data()
        # 加载初始设置（调试模式、性能模式等）
        self.load_initial_settings()
        
    def closeEvent(self, event):
        """关闭事件，同时关闭所有打开的对话框"""
        # 关闭所有活动对话框
        if hasattr(self, 'content_area') and hasattr(self.content_area, 'active_dialogs'):
            for dialog in self.content_area.active_dialogs[:]:  # 使用副本进行遍历
                dialog.close()
        
        # 调用父类方法
        super().closeEvent(event)
        
    def setup_ui(self):
        # 设置窗口属性
        self.setWindowTitle("Prompt收藏助手")
        self.setFixedSize(460, 800)
        self.setWindowFlags(Qt.FramelessWindowHint)  # 无标题栏
        
        # 设置窗口样式 - 移除圆角，改为直角边框
        self.setStyleSheet("""
            QMainWindow {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 0px;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 (水平布局)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.nav_bar = NavigationBar(self)
        main_layout.addWidget(self.nav_bar)
        
        # 右侧主区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 自定义标题栏
        self.title_bar = CustomTitleBar(self)
        right_layout.addWidget(self.title_bar)
        
        # 内容区域
        self.content_area = ContentArea(self)
        right_layout.addWidget(self.content_area)
        
        # 状态栏
        self.status_bar = StatusBar(self)
        right_layout.addWidget(self.status_bar)
        
        main_layout.addWidget(right_widget)
        
        # 居中显示窗口
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def switch_page(self, page_key):
        """切换页面"""
        self.content_area.switch_to_page(page_key)
        
        # 更新标题栏标题
        titles = {
            "home": "我的提示词",
            "trash": "回收站",
            "settings": "设置",
            "help": "帮助"
        }
        
        if page_key in titles:
            self.title_bar.set_title(titles[page_key])
            self.status_bar.set_status(f"已切换到{titles[page_key]}")
            
    def load_data(self):
        """加载数据"""
        try:
            # 只获取未删除的提示词
            prompts = self.model.get_all_prompts(include_deleted=False)
            # 将数据传递给 ContentArea 并应用筛选
            self.content_area.all_prompts = prompts
            # 填充分类筛选按钮
            self.content_area.populate_category_filters()
            # 填充标签筛选按钮
            self.content_area.populate_tag_filters()
            self.content_area.apply_filters_and_update_views()
            self.status_bar.set_status("数据加载完成")
        except Exception as e:
            print(f"加载数据时出错: {e}")
            self.status_bar.set_status(f"加载失败: {str(e)}")
            
    def update_prompt_list(self, prompts):
        """更新提示词列表显示"""
        if hasattr(self.content_area, 'prompt_list'):
            # 更新列表视图
            self.content_area.prompt_list.clear()
            for prompt in prompts:
                item_text = f"{prompt.title}\n{prompt.content[:50]}..." if len(prompt.content) > 50 else f"{prompt.title}\n{prompt.content}"
                self.content_area.prompt_list.addItem(item_text)
            
            # 更新卡片视图
            try:
                if hasattr(self.content_area, 'update_card_view') and hasattr(self.content_area, 'cards_list_layout'):
                    self.content_area.update_card_view(prompts)
            except Exception as e:
                import traceback
                print(f"更新卡片视图时出错: {e}")
                traceback.print_exc()
            
    def refresh_prompt_list(self):
        """刷新提示词列表"""
        self.load_data()
        self.status_bar.set_status("提示词列表已刷新")
    
    def on_settings_changed(self, setting_name, value):
        """处理设置变更"""
        try:
            if setting_name == "database_path":
                self.apply_database_path_change(value)
            elif setting_name == "debug_mode":
                self.apply_debug_mode_change(value)
            elif setting_name == "performance_mode":
                self.apply_performance_mode_change(value)
            
            # 更新状态栏
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status(f"设置已更新: {setting_name}")
                
        except Exception as e:
            print(f"应用设置变更时出错: {e}")
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status(f"设置更新失败: {str(e)}")
        
    def apply_database_path_change(self, db_path):
        """应用数据库路径变更"""
        # 这里可以实现数据库路径切换逻辑
        print(f"数据库路径已更改到: {db_path}")
    
    def apply_debug_mode_change(self, enabled):
        """应用调试模式变更"""
        self.debug_mode = enabled
        
        if enabled:
            print("🐛 调试模式已启用")
            # 启用详细日志输出
            import logging
            logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
            
            # 在状态栏显示调试指示器
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("🐛 调试模式已启用", 3000)
            
            # 启用错误详情显示
            import sys
            sys.tracebacklimit = 100  # 显示完整的错误堆栈
            
            print("调试功能已激活:")
            print("- 详细日志输出")
            print("- 完整错误堆栈跟踪") 
            print("- 状态栏调试指示")
            
        else:
            print("调试模式已禁用")
            # 恢复正常日志级别
            import logging
            logging.basicConfig(level=logging.WARNING)
            
            # 移除状态栏调试指示
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("调试模式已禁用", 2000)
            
            # 恢复默认错误显示
            import sys
            sys.tracebacklimit = 10
    
    def apply_performance_mode_change(self, enabled):
        """应用性能优化模式变更"""
        self.performance_mode = enabled
        
        if enabled:
            print("⚡ 性能优化模式已启用")
            
            # 禁用动画效果
            if hasattr(self, 'content_area'):
                # 禁用内容区域的动画
                for widget in self.content_area.findChildren(QWidget):
                    if hasattr(widget, 'setGraphicsEffect'):
                        widget.setGraphicsEffect(None)
            
            # 设置性能优化的样式（减少阴影和特效）
            performance_style = """
                QWidget {
                    /* 禁用所有动画效果 */
                }
                QPushButton {
                    transition: none;
                }
                QFrame {
                    /* 简化边框效果 */
                }
            """
            
            # 应用性能优化样式
            current_style = self.styleSheet()
            self.setStyleSheet(current_style + performance_style)
            
            # 在状态栏显示性能模式指示器
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("⚡ 性能优化模式已启用", 3000)
            
            print("性能优化功能已激活:")
            print("- 禁用UI动画效果")
            print("- 简化视觉特效")
            print("- 优化界面渲染")
            
        else:
            print("性能优化模式已禁用")
            
            # 重新加载原始样式（移除性能优化样式）
            original_style = """
            QMainWindow {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 0px;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
            """
            self.setStyleSheet(original_style)
            
            # 移除状态栏性能指示
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("性能优化模式已禁用", 2000)
    
    def load_initial_settings(self):
        """加载初始设置"""
        try:
            # 加载调试模式设置
            debug_mode = self.settings.value("debug_mode", False, type=bool)
            self.apply_debug_mode_change(debug_mode)
            
            # 加载性能模式设置
            performance_mode = self.settings.value("performance_mode", False, type=bool)
            self.apply_performance_mode_change(performance_mode)
            
            if debug_mode or performance_mode:
                modes = []
                if debug_mode:
                    modes.append("调试模式")
                if performance_mode:
                    modes.append("性能优化模式")
                print(f"已加载设置: {', '.join(modes)}")
                
        except Exception as e:
            print(f"加载初始设置时出错: {e}")

class SharePromptDialog(QDialog):
    """分享提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        self.selected_prompt = None
        
        # 初始化筛选相关属性
        self.all_prompts = []
        self.filtered_prompts = []
        self.current_category_filter = "全部分类"
        self.current_tag_filter = "全部标签"
        
        self.setWindowTitle("分享提示词")
        self.setFixedSize(450, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("选择要分享的提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 筛选区域
        filter_layout = QVBoxLayout()
        filter_label = QLabel("筛选条件:")
        filter_label.setStyleSheet("color: #374151; font-size: 14px; font-weight: 500;")
        filter_layout.addWidget(filter_label)
        
        # 分类筛选
        category_layout = QHBoxLayout()
        category_label = QLabel("分类:")
        category_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        category_label.setFixedWidth(40)
        
        self.category_combo = QComboBox()
        self.category_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: white;
                font-size: 12px;
                min-width: 120px;
                color: #374151;
            }
            QComboBox:hover {
                border-color: #9CA3AF;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6B7280;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                background-color: white;
                selection-background-color: #EBF4FF;
                selection-color: #1E40AF;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                color: #374151;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #F3F4F6;
                color: #1F2937;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
        """)
        self.category_combo.addItem("全部分类")
        self.category_combo.currentTextChanged.connect(self.on_category_filter_changed)
        
        category_layout.addWidget(category_label)
        category_layout.addWidget(self.category_combo)
        category_layout.addStretch()
        
        # 标签筛选
        tag_layout = QHBoxLayout()
        tag_label = QLabel("标签:")
        tag_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        tag_label.setFixedWidth(40)
        
        self.tag_combo = QComboBox()
        self.tag_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 4px 8px;
                background-color: white;
                font-size: 12px;
                min-width: 120px;
                color: #374151;
            }
            QComboBox:hover {
                border-color: #9CA3AF;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6B7280;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                background-color: white;
                selection-background-color: #EBF4FF;
                selection-color: #1E40AF;
                outline: none;
            }
            QComboBox QAbstractItemView::item {
                padding: 6px 8px;
                color: #374151;
                background-color: transparent;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #F3F4F6;
                color: #1F2937;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
        """)
        self.tag_combo.addItem("全部标签")
        self.tag_combo.currentTextChanged.connect(self.on_tag_filter_changed)
        
        tag_layout.addWidget(tag_label)
        tag_layout.addWidget(self.tag_combo)
        tag_layout.addStretch()
        
        filter_layout.addLayout(category_layout)
        filter_layout.addLayout(tag_layout)
        layout.addLayout(filter_layout)
        
        # 提示词列表
        prompt_layout = QVBoxLayout()
        prompt_label = QLabel("选择提示词:")
        prompt_label.setStyleSheet("color: #374151; font-size: 14px; font-weight: 500;")
        
        # 选择控制按钮
        selection_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.select_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        self.select_all_btn.clicked.connect(self.select_all_prompts)
        
        self.deselect_all_btn = QPushButton("取消全选")
        self.deselect_all_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        self.deselect_all_btn.clicked.connect(self.deselect_all_prompts)
        
        self.selection_count_label = QLabel("已选择: 0 个提示词")
        self.selection_count_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        
        selection_layout.addWidget(self.select_all_btn)
        selection_layout.addWidget(self.deselect_all_btn)
        selection_layout.addStretch()
        selection_layout.addWidget(self.selection_count_label)
        
        self.prompt_list = QListWidget()
        self.prompt_list.setSelectionMode(QListWidget.MultiSelection)
        self.prompt_list.setMinimumHeight(180)  # 调整列表高度
        self.prompt_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
                padding: 5px;
                font-size: 13px;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
                border-radius: 4px;
            }
            QListWidget::item:hover {
                background-color: #F9FAFB;
            }
        """)
        self.prompt_list.itemSelectionChanged.connect(self.on_prompt_selection_changed)
        
        prompt_layout.addWidget(prompt_label)
        prompt_layout.addLayout(selection_layout)
        prompt_layout.addWidget(self.prompt_list)
        layout.addLayout(prompt_layout)
        
        # 分享链接区域
        link_layout = QVBoxLayout()
        link_label = QLabel("分享链接:")
        link_label.setStyleSheet("color: #374151; font-size: 14px; font-weight: 500;")
        self.link_text = QTextEdit()
        self.link_text.setReadOnly(True)
        self.link_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: #F9FAFB;
                padding: 8px;
                font-size: 12px;
                color: #4B5563;
            }
        """)
        self.link_text.setFixedHeight(60)  # 减小链接区域高度
        self.link_text.setPlaceholderText("选择提示词后将显示分享链接")
        
        link_layout.addWidget(link_label)
        link_layout.addWidget(self.link_text)
        layout.addLayout(link_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.copy_btn = QPushButton("复制链接")
        self.copy_btn.setEnabled(False)  # 初始禁用
        self.copy_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
            QPushButton:disabled {
                background-color: #CBD5E1;
                color: #94A3B8;
            }
        """)
        self.copy_btn.clicked.connect(self.copy_link)
        
        self.batch_share_btn = QPushButton("批量分享")
        self.batch_share_btn.setEnabled(False)  # 初始禁用
        self.batch_share_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
            QPushButton:disabled {
                background-color: #CBD5E1;
                color: #94A3B8;
            }
        """)
        self.batch_share_btn.clicked.connect(self.copy_batch_link)
        
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: #4B5563;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #9CA3AF;
            }
            QPushButton:pressed {
                background-color: #E5E7EB;
            }
        """)
        close_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.batch_share_btn)
        button_layout.addWidget(self.copy_btn)
        button_layout.addWidget(close_btn)
        
        layout.addLayout(button_layout)
        
        # 加载提示词列表
        self.load_prompts()
        
    def on_category_filter_changed(self, category):
        """分类筛选变化处理"""
        self.current_category_filter = category
        self.apply_filters()
        
    def on_tag_filter_changed(self, tag):
        """标签筛选变化处理"""
        self.current_tag_filter = tag
        self.apply_filters()
        
    def apply_filters(self):
        """应用筛选条件"""
        self.filtered_prompts = []
        
        for prompt in self.all_prompts:
            # 分类筛选
            if self.current_category_filter != "全部分类":
                prompt_category = getattr(prompt, 'category', '通用')
                if prompt_category != self.current_category_filter:
                    continue
                    
            # 标签筛选
            if self.current_tag_filter != "全部标签":
                prompt_tags = getattr(prompt, 'tags', '')
                if isinstance(prompt_tags, str):
                    prompt_tags = [tag.strip() for tag in prompt_tags.split(',') if tag.strip()]
                else:
                    prompt_tags = list(prompt_tags) if prompt_tags else []
                    
                if self.current_tag_filter not in prompt_tags:
                    continue
                    
            self.filtered_prompts.append(prompt)
            
        self.update_prompt_list()
        
    def update_prompt_list(self):
        """更新提示词列表显示"""
        self.prompt_list.clear()
        
        for prompt in self.filtered_prompts:
            title = getattr(prompt, 'title', '无标题')
            item = QListWidgetItem(title)
            item.setData(Qt.UserRole, prompt)
            self.prompt_list.addItem(item)
            
        # 更新选择计数
        self.on_prompt_selection_changed()
        
    def populate_filters(self):
        """填充筛选选项"""
        if not self.all_prompts:
            return
            
        # 获取所有分类
        categories = set()
        for prompt in self.all_prompts:
            category = getattr(prompt, 'category', '通用')
            categories.add(category)
            
        # 更新分类下拉框
        self.category_combo.clear()
        self.category_combo.addItem("全部分类")
        for category in sorted(categories):
            self.category_combo.addItem(category)
            
        # 获取所有标签
        tags = set()
        for prompt in self.all_prompts:
            prompt_tags = getattr(prompt, 'tags', '')
            if isinstance(prompt_tags, str):
                prompt_tags = [tag.strip() for tag in prompt_tags.split(',') if tag.strip()]
            else:
                prompt_tags = list(prompt_tags) if prompt_tags else []
                
            for tag in prompt_tags:
                tags.add(tag)
                
        # 更新标签下拉框
        self.tag_combo.clear()
        self.tag_combo.addItem("全部标签")
        for tag in sorted(tags):
            self.tag_combo.addItem(tag)
        
    def select_all_prompts(self):
        """全选所有提示词"""
        for i in range(self.prompt_list.count()):
            self.prompt_list.item(i).setSelected(True)
    
    def deselect_all_prompts(self):
        """取消全选所有提示词"""
        for i in range(self.prompt_list.count()):
            self.prompt_list.item(i).setSelected(False)
    
    def on_prompt_selection_changed(self):
        """处理提示词选择变化"""
        selected_items = self.prompt_list.selectedItems()
        selected_count = len(selected_items)
        
        # 更新选择数量显示
        self.selection_count_label.setText(f"已选择: {selected_count} 个提示词")
        
        # 更新按钮状态
        has_selection = selected_count > 0
        self.copy_btn.setEnabled(has_selection)
        self.batch_share_btn.setEnabled(selected_count > 1)
        
        # 如果只选择了一个，显示单个分享链接
        if selected_count == 1:
            item = selected_items[0]
            prompt = item.data(Qt.UserRole)
            if prompt:
                self.selected_prompt = prompt
                share_code = self.generate_share_code(prompt)
                share_link = f"promptshare://{share_code}"
                self.link_text.setText(share_link)
        elif selected_count > 1:
            # 多个选择时，生成批量分享链接
            selected_prompts = []
            for item in selected_items:
                prompt = item.data(Qt.UserRole)
                if prompt:
                    selected_prompts.append(prompt)
            
            if selected_prompts:
                batch_share_code = self.generate_batch_share_code(selected_prompts)
                batch_share_link = f"promptshare://batch/{batch_share_code}"
                self.link_text.setText(batch_share_link)
        else:
            # 没有选择时，清空链接显示
            self.link_text.setText("")
        
    def load_prompts(self):
        """加载提示词列表"""
        if self.model:
            # 只获取未删除的提示词
            self.all_prompts = self.model.get_all_prompts(include_deleted=False)
            
            # 初始化筛选后的提示词列表
            self.filtered_prompts = self.all_prompts.copy()
            
            # 填充筛选选项
            self.populate_filters()
            
            # 更新列表显示
            self.update_prompt_list()
                
    def on_prompt_selected(self, item):
        """处理提示词选择事件（已废弃，保留兼容性）"""
        # 这个方法已经被on_prompt_selection_changed替代
        pass
    
    def generate_share_code(self, prompt):
        """生成分享代码"""
        import base64
        import json
        
        # 将提示词转换为JSON字符串
        prompt_dict = {
            'title': prompt.title,
            'content': prompt.content,
            'tags': prompt.tags if hasattr(prompt, 'tags') else [],
            'category': prompt.category if hasattr(prompt, 'category') else '',
            'media_files': prompt.media_files if hasattr(prompt, 'media_files') else [],
            'type': prompt.type if hasattr(prompt, 'type') else '文本'
        }
        
        # JSON序列化
        json_str = json.dumps(prompt_dict, ensure_ascii=False)
        
        # Base64编码
        share_code = base64.urlsafe_b64encode(json_str.encode('utf-8')).decode('utf-8')
        return share_code
    
    def generate_batch_share_code(self, prompts):
        """生成批量分享代码"""
        import base64
        import json
        
        # 将多个提示词转换为JSON数组
        prompts_data = []
        for prompt in prompts:
            prompt_dict = {
                'title': prompt.title,
                'content': prompt.content,
                'tags': prompt.tags if hasattr(prompt, 'tags') else [],
                'category': prompt.category if hasattr(prompt, 'category') else '',
                'media_files': prompt.media_files if hasattr(prompt, 'media_files') else [],
                'type': prompt.type if hasattr(prompt, 'type') else '文本'
            }
            prompts_data.append(prompt_dict)
        
        # JSON序列化
        json_str = json.dumps(prompts_data, ensure_ascii=False)
        
        # Base64编码
        share_code = base64.urlsafe_b64encode(json_str.encode('utf-8')).decode('utf-8')
        return share_code
    
    def copy_batch_link(self):
        """复制批量分享链接"""
        from PySide6.QtGui import QClipboard
        clipboard = QApplication.clipboard()
        clipboard.setText(self.link_text.toPlainText())
        
        # 显示复制成功提示
        selected_count = len(self.prompt_list.selectedItems())
        QMessageBox.information(self, "复制成功", f"批量分享链接已复制到剪贴板，包含 {selected_count} 个提示词")
    
    def copy_link(self):
        """复制分享链接"""
        from PySide6.QtGui import QClipboard
        clipboard = QApplication.clipboard()
        clipboard.setText(self.link_text.toPlainText())
        
        # 显示复制成功提示
        QMessageBox.information(self, "复制成功", "分享链接已复制到剪贴板")

class ImportPromptDialog(QDialog):
    """导入提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        
        self.setWindowTitle("获取提示词")
        self.setFixedSize(400, 250)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("导入分享的提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("请粘贴分享链接（支持多个链接，每行一个）:")
        desc_label.setStyleSheet("color: #374151; font-size: 14px;")
        layout.addWidget(desc_label)
        
        # 链接输入框
        self.link_input = QTextEdit()
        self.link_input.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
                font-size: 13px;
            }
        """)
        self.link_input.setFixedHeight(120)
        self.link_input.setPlaceholderText("粘贴以 promptshare:// 开头的分享链接\n支持多个链接，每行一个\n支持批量分享链接（promptshare://batch/）")
        layout.addWidget(self.link_input)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.import_btn = QPushButton("导入")
        self.import_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        self.import_btn.clicked.connect(self.import_prompt)
        
        self.batch_import_btn = QPushButton("批量导入")
        self.batch_import_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
        """)
        self.batch_import_btn.clicked.connect(self.import_batch_prompts)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: #4B5563;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #9CA3AF;
            }
            QPushButton:pressed {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.batch_import_btn)
        button_layout.addWidget(self.import_btn)
        button_layout.addWidget(cancel_btn)
        
        layout.addLayout(button_layout)
        
    def import_prompt(self):
        """导入提示词"""
        link = self.link_input.toPlainText().strip()
        
        # 检查链接格式
        if not link.startswith("promptshare://"):
            QMessageBox.warning(self, "格式错误", "无效的分享链接格式，请确保链接以 promptshare:// 开头")
            return
        
        try:
            # 提取分享代码
            share_code = link.replace("promptshare://", "")
            prompt_dict = self.decode_share_code(share_code)
            
            if not prompt_dict:
                QMessageBox.warning(self, "导入失败", "无法解析分享链接")
                return
                
            # 创建Prompt对象
            from model import Prompt
            new_prompt = Prompt(
                title=prompt_dict.get('title', ''),
                content=prompt_dict.get('content', ''),
                tags=prompt_dict.get('tags', []),
                category=prompt_dict.get('category', ''),
                media_files=prompt_dict.get('media_files', []),
                type=prompt_dict.get('type', '文本')
            )
            
            # 检查是否有媒体文件但文件不存在
            missing_media = []
            for media_file in new_prompt.media_files:
                if not os.path.exists(media_file):
                    missing_media.append(media_file)
            
            if missing_media:
                media_list = "\n".join(missing_media)
                QMessageBox.warning(self, "媒体文件缺失", 
                                   f"以下媒体文件在您的系统上不存在，将无法显示：\n{media_list}")
            
            # 添加到数据库
            if self.model:
                prompt_id = self.model.add_prompt(new_prompt)
                if prompt_id:
                    QMessageBox.information(self, "导入成功", f"已成功导入提示词: {new_prompt.title}")
                    self.accept()
                else:
                    QMessageBox.warning(self, "导入失败", "无法保存提示词到数据库")
            else:
                QMessageBox.warning(self, "导入失败", "数据模型不可用")
        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"导入过程中出错: {str(e)}")
    
    def import_batch_prompts(self):
        """批量导入提示词"""
        links_text = self.link_input.toPlainText().strip()
        if not links_text:
            QMessageBox.warning(self, "输入错误", "请先输入分享链接")
            return
        
        # 解析多个链接
        links = self.parse_multiple_links(links_text)
        if not links:
            QMessageBox.warning(self, "格式错误", "未找到有效的分享链接")
            return
        
        # 开始批量导入
        success_count = 0
        failed_count = 0
        failed_links = []
        
        for i, link in enumerate(links):
            try:
                # 检查链接格式
                if not link.startswith("promptshare://"):
                    failed_links.append(f"链接 {i+1}: 无效格式")
                    failed_count += 1
                    continue
                
                # 处理批量分享链接
                if link.startswith("promptshare://batch/"):
                    batch_result = self.import_batch_share_link(link)
                    if batch_result:
                        success_count += batch_result
                    else:
                        failed_links.append(f"链接 {i+1}: 批量链接解析失败")
                        failed_count += 1
                else:
                    # 处理单个分享链接
                    share_code = link.replace("promptshare://", "")
                    prompt_dict = self.decode_share_code(share_code)
                    
                    if not prompt_dict:
                        failed_links.append(f"链接 {i+1}: 解码失败")
                        failed_count += 1
                        continue
                    
                    # 创建Prompt对象
                    from model import Prompt
                    new_prompt = Prompt(
                        title=prompt_dict.get('title', ''),
                        content=prompt_dict.get('content', ''),
                        tags=prompt_dict.get('tags', []),
                        category=prompt_dict.get('category', ''),
                        media_files=prompt_dict.get('media_files', []),
                        type=prompt_dict.get('type', '文本')
                    )
                    
                    # 添加到数据库
                    if self.model:
                        prompt_id = self.model.add_prompt(new_prompt)
                        if prompt_id:
                            success_count += 1
                        else:
                            failed_links.append(f"链接 {i+1}: 保存失败")
                            failed_count += 1
                    else:
                        failed_links.append(f"链接 {i+1}: 数据模型不可用")
                        failed_count += 1
                        
            except Exception as e:
                failed_links.append(f"链接 {i+1}: {str(e)}")
                failed_count += 1
        
        # 显示导入结果
        result_message = f"批量导入完成！\n\n成功导入: {success_count} 个提示词\n失败: {failed_count} 个"
        if failed_links:
            result_message += f"\n\n失败详情:\n" + "\n".join(failed_links[:5])  # 只显示前5个失败项
            if len(failed_links) > 5:
                result_message += f"\n... 还有 {len(failed_links) - 5} 个失败项"
        
        if success_count > 0:
            QMessageBox.information(self, "批量导入成功", result_message)
            self.accept()
        else:
            QMessageBox.warning(self, "批量导入失败", result_message)
    
    def parse_multiple_links(self, links_text):
        """解析多个分享链接"""
        lines = links_text.strip().split('\n')
        links = []
        for line in lines:
            line = line.strip()
            if line and line.startswith("promptshare://"):
                links.append(line)
        return links
    
    def import_batch_share_link(self, batch_link):
        """导入批量分享链接"""
        try:
            # 提取批量分享代码
            batch_code = batch_link.replace("promptshare://batch/", "")
            prompts_data = self.decode_batch_share_code(batch_code)
            
            if not prompts_data:
                return 0
            
            success_count = 0
            for prompt_dict in prompts_data:
                try:
                    # 创建Prompt对象
                    from model import Prompt
                    new_prompt = Prompt(
                        title=prompt_dict.get('title', ''),
                        content=prompt_dict.get('content', ''),
                        tags=prompt_dict.get('tags', []),
                        category=prompt_dict.get('category', ''),
                        media_files=prompt_dict.get('media_files', []),
                        type=prompt_dict.get('type', '文本')
                    )
                    
                    # 添加到数据库
                    if self.model:
                        prompt_id = self.model.add_prompt(new_prompt)
                        if prompt_id:
                            success_count += 1
                except Exception as e:
                    print(f"导入批量提示词时出错: {e}")
                    continue
            
            return success_count
        except Exception as e:
            print(f"解析批量分享链接时出错: {e}")
            return 0
    
    def decode_batch_share_code(self, batch_code):
        """解码批量分享代码"""
        import base64
        import json
        
        try:
            # Base64解码
            json_str = base64.urlsafe_b64decode(batch_code).decode('utf-8')
            
            # JSON反序列化
            prompts_data = json.loads(json_str)
            return prompts_data
        except Exception as e:
            print(f"解码批量分享代码时出错: {e}")
            return None
    
    def decode_share_code(self, share_code):
        """解码分享代码"""
        import base64
        import json
        
        try:
            # Base64解码
            json_str = base64.urlsafe_b64decode(share_code).decode('utf-8')
            
            # JSON反序列化
            prompt_dict = json.loads(json_str)
            return prompt_dict
        except Exception as e:
            print(f"解码分享代码时出错: {e}")
            return None

class ZoomableImageView(QWidget):
    """可缩放的图片显示组件，支持滚轮缩放和拖动平移"""
    
    # 自定义信号
    zoomChanged = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.original_pixmap = None
        self.current_pixmap = None
        self.transform = QTransform()
        self.zoom_level = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 5.0
        self.zoom_step = 0.2
        
        # 拖拽相关
        self.dragging = False
        self.last_mouse_pos = QPoint()
        self.drag_offset = QPoint()
        
        # 设置鼠标追踪
        self.setMouseTracking(True)
        
        # 设置样式
        self.setStyleSheet("""
            ZoomableImageView {
                background-color: #2D3748;
                border: none;
            }
        """)
        
    def setPixmap(self, pixmap):
        """设置图片并重置视图"""
        self.original_pixmap = pixmap
        self.current_pixmap = pixmap
        if pixmap:
            self.resetView()
        else:
            self.zoom_level = 1.0
            self.transform = QTransform()
        self.update()
        
    def resetView(self):
        """重置视图到适应窗口状态"""
        if not self.original_pixmap:
            return
            
        self.zoom_level = 1.0
        self.transform = QTransform()
        self.fitToWindow()
        self.zoomChanged.emit()
        
    def fitToWindow(self):
        """适应窗口显示"""
        if not self.original_pixmap or self.width() <= 0 or self.height() <= 0:
            return
            
        # 计算适应窗口的缩放比例
        pixmap_rect = self.original_pixmap.rect()
        widget_rect = self.rect()
        
        scale_x = widget_rect.width() / pixmap_rect.width()
        scale_y = widget_rect.height() / pixmap_rect.height()
        scale = min(scale_x, scale_y)
        
        # 如果图片比窗口小，不放大
        if scale > 1.0:
            scale = 1.0
            
        self.zoom_level = scale
        self.transform = QTransform().scale(scale, scale)
        
        # 居中显示
        scaled_width = pixmap_rect.width() * scale
        scaled_height = pixmap_rect.height() * scale
        x_offset = (widget_rect.width() - scaled_width) / 2
        y_offset = (widget_rect.height() - scaled_height) / 2
        self.transform.translate(x_offset / scale, y_offset / scale)
        
        self.update()
        self.zoomChanged.emit()
        
    def actualSize(self):
        """显示实际大小"""
        if not self.original_pixmap:
            return
            
        self.zoom_level = 1.0
        self.transform = QTransform()
        
        # 居中显示
        widget_rect = self.rect()
        pixmap_rect = self.original_pixmap.rect()
        x_offset = (widget_rect.width() - pixmap_rect.width()) / 2
        y_offset = (widget_rect.height() - pixmap_rect.height()) / 2
        self.transform.translate(x_offset, y_offset)
        
        self.update()
        self.zoomChanged.emit()
        
    def zoomIn(self):
        """放大图片"""
        if self.zoom_level < self.max_zoom:
            self.zoom_level = min(self.zoom_level + self.zoom_step, self.max_zoom)
            self.transform.scale(1 + self.zoom_step / self.zoom_level, 1 + self.zoom_step / self.zoom_level)
            self.update()
            self.zoomChanged.emit()
            
    def zoomOut(self):
        """缩小图片"""
        if self.zoom_level > self.min_zoom:
            self.zoom_level = max(self.zoom_level - self.zoom_step, self.min_zoom)
            self.transform.scale(1 - self.zoom_step / self.zoom_level, 1 - self.zoom_step / self.zoom_level)
            self.update()
            self.zoomChanged.emit()
            
    def getZoomLevel(self):
        """获取当前缩放级别"""
        return self.zoom_level
        
    def paintEvent(self, event):
        """绘制事件"""
        if not self.original_pixmap:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.SmoothPixmapTransform)
        
        # 应用变换
        painter.setTransform(self.transform)
        
        # 绘制图片
        painter.drawPixmap(0, 0, self.original_pixmap)
        
    def wheelEvent(self, event):
        """滚轮事件处理"""
        if not self.original_pixmap:
            return
            
        # 获取鼠标位置
        mouse_pos = event.position()
        
        # 计算缩放因子
        zoom_factor = 1.0
        if event.angleDelta().y() > 0:
            # 放大
            if self.zoom_level < self.max_zoom:
                zoom_factor = 1.1
                self.zoom_level = min(self.zoom_level * zoom_factor, self.max_zoom)
            else:
                return
        else:
            # 缩小
            if self.zoom_level > self.min_zoom:
                zoom_factor = 0.9
                self.zoom_level = max(self.zoom_level * zoom_factor, self.min_zoom)
            else:
                return
            
        # 计算鼠标位置相对于图片的偏移
        transform_inv = self.transform.inverted()
        if transform_inv[1]:  # 检查是否可逆
            mouse_in_image = transform_inv[0].map(mouse_pos)
            
            # 应用缩放
            self.transform.scale(zoom_factor, zoom_factor)
            
            # 调整位置以保持鼠标位置不变
            new_transform_inv = self.transform.inverted()
            if new_transform_inv[1]:  # 检查是否可逆
                new_mouse_in_image = new_transform_inv[0].map(mouse_pos)
                offset = new_mouse_in_image - mouse_in_image
                self.transform.translate(offset.x(), offset.y())
        else:
            # 如果变换不可逆，直接应用缩放
            self.transform.scale(zoom_factor, zoom_factor)
        
        self.update()
        self.zoomChanged.emit()
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and self.zoom_level > 1.0:
            self.dragging = True
            self.last_mouse_pos = event.position()
            self.setCursor(Qt.ClosedHandCursor)
        elif event.button() == Qt.RightButton:
            # 右键双击重置视图
            self.resetView()
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and self.zoom_level > 1.0:
            current_pos = event.position()
            delta = current_pos - self.last_mouse_pos
            
            # 应用平移
            self.transform.translate(delta.x(), delta.y())
            
            # 边界检查
            self.constrainToBounds()
            
            self.last_mouse_pos = current_pos
            self.update()
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            self.setCursor(Qt.ArrowCursor)
            
    def constrainToBounds(self):
        """约束图片在视图边界内"""
        if not self.original_pixmap or self.zoom_level <= 1.0:
            return
            
        # 获取变换后的图片边界
        pixmap_rect = self.original_pixmap.rect()
        transformed_rect = self.transform.mapRect(pixmap_rect)
        widget_rect = self.rect()
        
        # 计算需要的调整
        dx = 0
        dy = 0
        
        if transformed_rect.left() > widget_rect.left():
            dx = widget_rect.left() - transformed_rect.left()
        elif transformed_rect.right() < widget_rect.right():
            dx = widget_rect.right() - transformed_rect.right()
            
        if transformed_rect.top() > widget_rect.top():
            dy = widget_rect.top() - transformed_rect.top()
        elif transformed_rect.bottom() < widget_rect.bottom():
            dy = widget_rect.bottom() - transformed_rect.bottom()
            
        # 应用调整
        if dx != 0 or dy != 0:
            self.transform.translate(dx, dy)
            
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if self.original_pixmap and self.zoom_level <= 1.0:
            self.fitToWindow()


class ImageViewerDialog(QDialog):
    """图片查看对话框，用于显示和浏览提示词的媒体文件"""
    
    def __init__(self, media_files, current_index=0, parent=None):
        super().__init__(parent)
        self.media_files = media_files
        self.current_index = current_index
        
        # 设置窗口属性
        self.setWindowTitle("媒体查看器")
        self.setMinimumSize(800, 600)
        
        # 初始化UI
        self.init_ui()
        
        # 加载当前图片
        self.load_current_image()
        
        # 居中显示
        self.center_dialog()
        
    def init_ui(self):
        """初始化UI"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 图片显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2D3748;
                border: none;
            }
        """)
        
        # 可缩放图片视图
        self.zoomable_image_view = ZoomableImageView()
        
        self.scroll_area.setWidget(self.zoomable_image_view)
        layout.addWidget(self.scroll_area)
        
        # 控制区域
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(10)
        
        # 上一张按钮
        self.prev_button = QPushButton("上一张")
        self.prev_button.setFixedHeight(36)
        self.prev_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.prev_button.clicked.connect(self.show_previous)
        control_layout.addWidget(self.prev_button)
        
        # 缩放控制按钮
        zoom_layout = QHBoxLayout()
        zoom_layout.setSpacing(5)
        
        # 缩小按钮
        self.zoom_out_button = QPushButton("缩小")
        self.zoom_out_button.setFixedSize(60, 36)
        self.zoom_out_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.zoom_out_button.clicked.connect(self.zoom_out)
        zoom_layout.addWidget(self.zoom_out_button)
        
        # 缩放比例显示
        self.zoom_label = QLabel("100%")
        self.zoom_label.setAlignment(Qt.AlignCenter)
        self.zoom_label.setFixedSize(60, 36)
        self.zoom_label.setStyleSheet("""
            QLabel {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        zoom_layout.addWidget(self.zoom_label)
        
        # 放大按钮
        self.zoom_in_button = QPushButton("放大")
        self.zoom_in_button.setFixedSize(60, 36)
        self.zoom_in_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.zoom_in_button.clicked.connect(self.zoom_in)
        zoom_layout.addWidget(self.zoom_in_button)
        
        # 适应窗口按钮
        self.fit_button = QPushButton("适应")
        self.fit_button.setFixedSize(60, 36)
        self.fit_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.fit_button.clicked.connect(self.fit_to_window)
        zoom_layout.addWidget(self.fit_button)
        
        # 实际大小按钮
        self.actual_button = QPushButton("实际")
        self.actual_button.setFixedSize(60, 36)
        self.actual_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.actual_button.clicked.connect(self.actual_size)
        zoom_layout.addWidget(self.actual_button)
        
        control_layout.addLayout(zoom_layout)
        
        # 图片索引指示器
        self.indicator_label = QLabel()
        self.indicator_label.setAlignment(Qt.AlignCenter)
        self.indicator_label.setStyleSheet("""
            QLabel {
                color: #4B5563;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        control_layout.addWidget(self.indicator_label, 1)  # 1表示拉伸因子
        
        # 下一张按钮
        self.next_button = QPushButton("下一张")
        self.next_button.setFixedHeight(36)
        self.next_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.next_button.clicked.connect(self.show_next)
        control_layout.addWidget(self.next_button)
        
        layout.addLayout(control_layout)
        
        # 安装事件过滤器，用于处理键盘事件
        self.installEventFilter(self)
        
        # 连接缩放视图的信号
        self.zoomable_image_view.zoomChanged.connect(self.update_zoom_display)
        
    def load_current_image(self):
        """加载当前索引的图片或视频缩略图"""
        if not self.media_files or self.current_index >= len(self.media_files):
            self.zoomable_image_view.setPixmap(None)
            self.update_controls()
            return
            
        media_path = self.media_files[self.current_index]
        file_path = Path(media_path)
        
        if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片文件
            pixmap = QPixmap(media_path)
            if not pixmap.isNull():
                # 设置图片到可缩放视图
                self.zoomable_image_view.setPixmap(pixmap)
            else:
                # 创建错误提示图片
                error_pixmap = QPixmap(400, 300)
                error_pixmap.fill(QColor(45, 55, 72))
                painter = QPainter(error_pixmap)
                painter.setPen(QPen(QColor(255, 255, 255)))
                painter.drawText(error_pixmap.rect(), Qt.AlignCenter, "无法加载图片")
                painter.end()
                self.zoomable_image_view.setPixmap(error_pixmap)
        elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件 - 尝试提取第一帧作为缩略图
            print(f"ImageViewerDialog - 正在处理视频文件: {file_path}, OpenCV可用: {OPENCV_AVAILABLE}")
            if OPENCV_AVAILABLE:
                # 尝试获取视频缩略图
                print(f"ImageViewerDialog - 开始提取视频缩略图: {file_path}")
                thumb_pixmap = extract_video_thumbnail(file_path, (self.scroll_area.width() - 20, self.scroll_area.height() - 20))
                print(f"ImageViewerDialog - 提取结果: {'成功' if thumb_pixmap and not thumb_pixmap.isNull() else '失败'}")
                
                if thumb_pixmap and not thumb_pixmap.isNull():
                    # 设置视频缩略图到可缩放视图
                    self.zoomable_image_view.setPixmap(thumb_pixmap)
                    return
            
            # 如果无法提取缩略图，显示默认占位符
            video_pixmap = QPixmap(400, 300)
            video_pixmap.fill(QColor(45, 55, 72))
            painter = QPainter(video_pixmap)
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(video_pixmap.rect(), Qt.AlignCenter, "视频文件\n(点击使用系统播放器播放)")
            painter.end()
            self.zoomable_image_view.setPixmap(video_pixmap)
        else:
            # 其他文件
            other_pixmap = QPixmap(400, 300)
            other_pixmap.fill(QColor(45, 55, 72))
            painter = QPainter(other_pixmap)
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(other_pixmap.rect(), Qt.AlignCenter, "不支持的文件格式")
            painter.end()
            self.zoomable_image_view.setPixmap(other_pixmap)
            
        # 更新控件状态
        self.update_controls()
        
    def update_controls(self):
        """更新控件状态"""
        has_multiple_files = len(self.media_files) > 1
        self.prev_button.setEnabled(has_multiple_files)
        self.next_button.setEnabled(has_multiple_files)
        
        if self.media_files:
            self.indicator_label.setText(f"{self.current_index + 1} / {len(self.media_files)}")
        else:
            self.indicator_label.setText("0 / 0")
            
    def show_next(self):
        """显示下一张图片"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index + 1) % len(self.media_files)
        self.load_current_image()
        
    def show_previous(self):
        """显示上一张图片"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index - 1) % len(self.media_files)
        self.load_current_image()
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理键盘事件和点击事件"""
        if event.type() == QEvent.KeyPress:
            key = event.key()
            if key == Qt.Key_Left:
                self.show_previous()
                return True
            elif key == Qt.Key_Right:
                self.show_next()
                return True
            elif key == Qt.Key_Escape:
                self.close()
                return True
        
        # 如果是鼠标点击事件，检查当前媒体是否为视频
        elif event.type() == QEvent.MouseButtonPress and obj == self.zoomable_image_view:
            if self.media_files and self.current_index < len(self.media_files):
                media_path = self.media_files[self.current_index]
                file_path = Path(media_path)
                if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                    # 如果是视频文件，使用系统默认播放器打开
                    success = open_video_with_system_player(file_path)
                    if not success:
                        QMessageBox.warning(self, "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
                    return True
                
        return super().eventFilter(obj, event)
        
    def zoom_in(self):
        """放大图片"""
        self.zoomable_image_view.zoomIn()
        
    def zoom_out(self):
        """缩小图片"""
        self.zoomable_image_view.zoomOut()
        
    def fit_to_window(self):
        """适应窗口显示"""
        self.zoomable_image_view.fitToWindow()
        
    def actual_size(self):
        """显示实际大小"""
        self.zoomable_image_view.actualSize()
        
    def update_zoom_display(self):
        """更新缩放比例显示"""
        zoom_level = self.zoomable_image_view.getZoomLevel()
        self.zoom_label.setText(f"{int(zoom_level * 100)}%")
        
    def center_dialog(self):
        """居中显示对话框"""
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move((screen.width() - size.width()) // 2,
                 (screen.height() - size.height()) // 2)

def open_video_with_system_player(video_path):
    """使用系统默认播放器打开视频文件"""
    import os
    import platform
    import subprocess
    
    video_path = str(video_path)
    
    try:
        if platform.system() == 'Windows':
            os.startfile(video_path)
        elif platform.system() == 'Darwin':  # macOS
            subprocess.call(['open', video_path])
        else:  # Linux
            subprocess.call(['xdg-open', video_path])
        return True
    except Exception as e:
        print(f"无法打开视频文件: {e}")
        return False

class ExportPromptDialog(QDialog):
    """导出提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        
        self.setWindowTitle("导出提示词")
        self.setFixedSize(400, 200)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("导出提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 格式选择
        format_layout = QHBoxLayout()
        format_label = QLabel("导出格式:")
        format_label.setStyleSheet("color: #374151; font-size: 14px;")
        format_layout.addWidget(format_label)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["JSON", "CSV", "Markdown"])
        self.format_combo.setStyleSheet("""
            QComboBox {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                padding: 8px;
                background-color: white;
                font-size: 13px;
            }
        """)
        format_layout.addWidget(self.format_combo)
        format_layout.addStretch()
        layout.addLayout(format_layout)
        
        # 说明文字
        desc_label = QLabel("选择导出格式后点击导出按钮选择保存位置")
        desc_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        layout.addWidget(desc_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.export_btn = QPushButton("导出")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        self.export_btn.clicked.connect(self.export_prompts)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def export_prompts(self):
        """导出提示词"""
        if not self.model:
            QMessageBox.warning(self, "错误", "无法获取提示词数据")
            return
        
        # 获取所有提示词
        prompts = self.model.get_all_prompts()
        if not prompts:
            QMessageBox.information(self, "提示", "没有可导出的提示词")
            return
        
        # 获取选择的格式
        format_type = self.format_combo.currentText()
        
        # 设置文件过滤器
        if format_type == "JSON":
            file_filter = "JSON文件 (*.json)"
            default_ext = ".json"
        elif format_type == "CSV":
            file_filter = "CSV文件 (*.csv)"
            default_ext = ".csv"
        elif format_type == "Markdown":
            file_filter = "Markdown文件 (*.md)"
            default_ext = ".md"
        
        # 打开文件保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存提示词文件", 
            f"prompts{default_ext}", 
            file_filter
        )
        
        if not file_path:
            return
        
        try:
            if format_type == "JSON":
                self.export_to_json(prompts, file_path)
            elif format_type == "CSV":
                self.export_to_csv(prompts, file_path)
            elif format_type == "Markdown":
                self.export_to_markdown(prompts, file_path)
            
            QMessageBox.information(self, "成功", f"提示词已成功导出到: {file_path}")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def export_to_json(self, prompts, file_path):
        """导出为JSON格式"""
        import json
        
        # 转换为字典列表
        data = []
        for prompt in prompts:
            prompt_dict = prompt.to_dict()
            # 移除内部ID，避免导入时冲突
            if 'id' in prompt_dict:
                del prompt_dict['id']
            data.append(prompt_dict)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def export_to_csv(self, prompts, file_path):
        """导出为CSV格式"""
        import csv
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(['标题', '内容', '分类', '标签', '类型', '收藏', '置顶', '创建时间'])
            
            # 写入数据
            for prompt in prompts:
                tags_str = ', '.join(prompt.tags) if prompt.tags else ''
                writer.writerow([
                    prompt.title,
                    prompt.content,
                    prompt.category,
                    tags_str,
                    prompt.type,
                    '是' if prompt.is_favorite else '否',
                    '是' if prompt.is_pinned else '否',
                    prompt.created_at
                ])
    
    def export_to_markdown(self, prompts, file_path):
        """导出为Markdown格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("# 提示词集合\n\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总计: {len(prompts)} 个提示词\n\n")
            
            for i, prompt in enumerate(prompts, 1):
                f.write(f"## {i}. {prompt.title}\n\n")
                f.write(f"**分类:** {prompt.category}\n\n")
                
                if prompt.tags:
                    tags_str = ', '.join([f'`{tag}`' for tag in prompt.tags])
                    f.write(f"**标签:** {tags_str}\n\n")
                
                f.write(f"**类型:** {prompt.type}\n\n")
                
                if prompt.is_favorite:
                    f.write("**收藏:** 是\n\n")
                if prompt.is_pinned:
                    f.write("**置顶:** 是\n\n")
                
                f.write("**内容:**\n\n")
                f.write(f"{prompt.content}\n\n")
                
                if prompt.created_at:
                    f.write(f"**创建时间:** {prompt.created_at}\n\n")
                
                f.write("---\n\n")

class ImportFileDialog(QDialog):
    """从文件导入提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        self.analyzed_prompts = []  # 存储分析结果
        self.selected_file_path = None
        
        self.setWindowTitle("从文件导入提示词")
        self.setFixedSize(500, 600)
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
            }
        """)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("从文件导入提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("选择要导入的提示词文件（支持JSON、CSV、Markdown格式）")
        desc_label.setStyleSheet("color: #374151; font-size: 14px;")
        layout.addWidget(desc_label)
        
        # 文件选择按钮
        self.import_btn = QPushButton("选择文件")
        self.import_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:pressed {
                background-color: #047857;
            }
        """)
        self.import_btn.clicked.connect(self.select_file)
        layout.addWidget(self.import_btn)
        
        # 分析结果显示区域
        self.analysis_group = QFrame()
        self.analysis_group.setStyleSheet("""
            QFrame {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: #F9FAFB;
                padding: 10px;
            }
        """)
        self.analysis_group.setVisible(False)
        layout.addWidget(self.analysis_group)
        
        analysis_layout = QVBoxLayout(self.analysis_group)
        analysis_layout.setSpacing(15)
        
        # 文件信息
        self.file_info_label = QLabel()
        self.file_info_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        analysis_layout.addWidget(self.file_info_label)
        
        # 提示词数量
        self.count_label = QLabel()
        self.count_label.setStyleSheet("color: #374151; font-size: 13px;")
        analysis_layout.addWidget(self.count_label)
        
        # 提示词预览列表
        self.preview_label = QLabel("提示词预览:")
        self.preview_label.setStyleSheet("color: #374151; font-size: 13px; font-weight: 500;")
        analysis_layout.addWidget(self.preview_label)
        
        self.preview_list = QTextEdit()
        self.preview_list.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
                font-size: 12px;
                max-height: 200px;
            }
        """)
        self.preview_list.setReadOnly(True)
        analysis_layout.addWidget(self.preview_list)
        
        # 确认导入按钮
        self.confirm_btn = QPushButton("确认导入")
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        self.confirm_btn.clicked.connect(self.confirm_import)
        self.confirm_btn.setVisible(False)
        analysis_layout.addWidget(self.confirm_btn)
        
        # 底部按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def select_file(self):
        """选择文件并分析"""
        if not self.model:
            QMessageBox.warning(self, "错误", "无法获取提示词数据")
            return
        
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择提示词文件",
            "",
            "所有支持的文件 (*.json *.csv *.md);;JSON文件 (*.json);;CSV文件 (*.csv);;Markdown文件 (*.md)"
        )
        
        if not file_path:
            return
        
        self.selected_file_path = file_path
        
        try:
            # 分析文件
            self.analyze_file(file_path)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"文件分析失败: {str(e)}")
    
    def analyze_file(self, file_path):
        """分析文件内容"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.json':
            self.analyze_json_file(file_path)
        elif file_ext == '.csv':
            self.analyze_csv_file(file_path)
        elif file_ext == '.md':
            self.analyze_markdown_file(file_path)
        else:
            QMessageBox.warning(self, "错误", "不支持的文件格式")
            return
    
    def analyze_json_file(self, file_path):
        """分析JSON文件"""
        import json
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            data = [data]
        
        self.analyzed_prompts = []
        for item in data:
            try:
                prompt = Prompt.from_dict(item)
                self.analyzed_prompts.append(prompt)
            except Exception as e:
                print(f"解析提示词失败: {e}")
        
        self.show_analysis_result("JSON", file_path)
    
    def analyze_csv_file(self, file_path):
        """分析CSV文件"""
        import csv
        
        self.analyzed_prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # 解析标签
                    tags = []
                    if row.get('标签'):
                        tags = [tag.strip() for tag in row['标签'].split(',') if tag.strip()]
                    
                    # 创建Prompt对象
                    prompt = Prompt(
                        title=row.get('标题', ''),
                        content=row.get('内容', ''),
                        category=row.get('分类', ''),
                        tags=tags,
                        type=row.get('类型', '文本'),
                        is_favorite=1 if row.get('收藏') == '是' else 0,
                        is_pinned=1 if row.get('置顶') == '是' else 0
                    )
                    self.analyzed_prompts.append(prompt)
                except Exception as e:
                    print(f"解析提示词失败: {e}")
        
        self.show_analysis_result("CSV", file_path)
    
    def analyze_markdown_file(self, file_path):
        """分析Markdown文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的Markdown解析
        import re
        
        # 分割为各个提示词块
        prompt_blocks = re.split(r'## \d+\.', content)
        
        self.analyzed_prompts = []
        for block in prompt_blocks[1:]:  # 跳过第一个空块
            try:
                lines = block.strip().split('\n')
                if not lines:
                    continue
                
                # 提取标题（第一行）
                title = lines[0].strip()
                
                # 提取其他信息
                category = ''
                tags = []
                prompt_content = ''
                prompt_type = '文本'
                
                in_content = False
                for line in lines[1:]:
                    line = line.strip()
                    if not line or line == '---':
                        continue
                    
                    if line.startswith('**分类:**'):
                        category = line.replace('**分类:**', '').strip()
                    elif line.startswith('**标签:**'):
                        tags_text = line.replace('**标签:**', '').strip()
                        tags = [tag.strip('`') for tag in re.findall(r'`([^`]+)`', tags_text)]
                    elif line.startswith('**类型:**'):
                        prompt_type = line.replace('**类型:**', '').strip()
                    elif line.startswith('**内容:**'):
                        in_content = True
                        continue
                    elif in_content and line.startswith('**'):
                        in_content = False
                    elif in_content:
                        prompt_content += line + '\n'
                
                # 创建Prompt对象
                prompt = Prompt(
                    title=title,
                    content=prompt_content.strip(),
                    category=category,
                    tags=tags,
                    type=prompt_type
                )
                self.analyzed_prompts.append(prompt)
                
            except Exception as e:
                print(f"解析提示词失败: {e}")
        
        self.show_analysis_result("Markdown", file_path)
    
    def show_analysis_result(self, file_type, file_path):
        """显示分析结果"""
        file_name = Path(file_path).name
        total_count = len(self.analyzed_prompts)
        
        # 更新文件信息
        self.file_info_label.setText(f"文件: {file_name} ({file_type}格式)")
        
        # 更新数量信息
        self.count_label.setText(f"发现 {total_count} 个提示词")
        
        # 更新预览列表
        preview_text = ""
        for i, prompt in enumerate(self.analyzed_prompts[:5], 1):  # 只显示前5个
            preview_text += f"{i}. {prompt.title}\n"
            if prompt.category:
                preview_text += f"   分类: {prompt.category}\n"
            if prompt.tags:
                tags_str = ', '.join(prompt.tags)
                preview_text += f"   标签: {tags_str}\n"
            preview_text += "\n"
        
        if total_count > 5:
            preview_text += f"... 还有 {total_count - 5} 个提示词\n"
        
        self.preview_list.setPlainText(preview_text.strip())
        
        # 显示分析结果区域和确认按钮
        self.analysis_group.setVisible(True)
        self.confirm_btn.setVisible(True)
    
    def confirm_import(self):
        """确认导入"""
        if not self.analyzed_prompts:
            QMessageBox.warning(self, "错误", "没有可导入的提示词")
            return
        
        try:
            # 执行实际导入
            imported_count = 0
            for prompt in self.analyzed_prompts:
                try:
                    self.model.add_prompt(prompt)
                    imported_count += 1
                except Exception as e:
                    print(f"导入提示词失败: {e}")
            
            QMessageBox.information(self, "成功", f"成功导入 {imported_count} 个提示词")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入失败: {str(e)}")
    
    def import_from_file(self):
        """从文件导入提示词（保留原方法以兼容）"""
        self.select_file()
    
    def import_from_json(self, file_path):
        """从JSON文件导入（直接导入，不分析）"""
        import json
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            data = [data]
        
        imported_count = 0
        for item in data:
            try:
                # 创建Prompt对象
                prompt = Prompt.from_dict(item)
                # 添加到数据库
                self.model.add_prompt(prompt)
                imported_count += 1
            except Exception as e:
                print(f"导入提示词失败: {e}")
        
        print(f"成功导入 {imported_count} 个提示词")
    
    def import_from_csv(self, file_path):
        """从CSV文件导入（直接导入，不分析）"""
        import csv
        
        imported_count = 0
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # 解析标签
                    tags = []
                    if row.get('标签'):
                        tags = [tag.strip() for tag in row['标签'].split(',') if tag.strip()]
                    
                    # 创建Prompt对象
                    prompt = Prompt(
                        title=row.get('标题', ''),
                        content=row.get('内容', ''),
                        category=row.get('分类', ''),
                        tags=tags,
                        type=row.get('类型', '文本'),
                        is_favorite=1 if row.get('收藏') == '是' else 0,
                        is_pinned=1 if row.get('置顶') == '是' else 0
                    )
                    
                    # 添加到数据库
                    self.model.add_prompt(prompt)
                    imported_count += 1
                except Exception as e:
                    print(f"导入提示词失败: {e}")
        
        print(f"成功导入 {imported_count} 个提示词")
    
    def import_from_markdown(self, file_path):
        """从Markdown文件导入（直接导入，不分析）"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的Markdown解析
        import re
        
        # 分割为各个提示词块
        prompt_blocks = re.split(r'## \d+\.', content)
        
        imported_count = 0
        for block in prompt_blocks[1:]:  # 跳过第一个空块
            try:
                lines = block.strip().split('\n')
                if not lines:
                    continue
                
                # 提取标题（第一行）
                title = lines[0].strip()
                
                # 提取其他信息
                category = ''
                tags = []
                prompt_content = ''
                prompt_type = '文本'
                
                in_content = False
                for line in lines[1:]:
                    line = line.strip()
                    if not line or line == '---':
                        continue
                    
                    if line.startswith('**分类:**'):
                        category = line.replace('**分类:**', '').strip()
                    elif line.startswith('**标签:**'):
                        tags_text = line.replace('**标签:**', '').strip()
                        tags = [tag.strip('`') for tag in re.findall(r'`([^`]+)`', tags_text)]
                    elif line.startswith('**类型:**'):
                        prompt_type = line.replace('**类型:**', '').strip()
                    elif line.startswith('**内容:**'):
                        in_content = True
                        continue
                    elif in_content and line.startswith('**'):
                        in_content = False
                    elif in_content:
                        prompt_content += line + '\n'
                
                # 创建Prompt对象
                prompt = Prompt(
                    title=title,
                    content=prompt_content.strip(),
                    category=category,
                    tags=tags,
                    type=prompt_type
                )
                
                # 添加到数据库
                self.model.add_prompt(prompt)
                imported_count += 1
                
            except Exception as e:
                print(f"导入提示词失败: {e}")
        
        print(f"成功导入 {imported_count} 个提示词")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = PromptAssistantRedesigned()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main() 