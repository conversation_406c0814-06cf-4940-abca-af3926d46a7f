#!/usr/bin/env python3
"""
帮助页面组件
从ContentArea提取的帮助页面相关逻辑
"""
from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QScrollArea, QWidget, QLabel, QPushButton, QTextEdit, QFrame
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from pages.base_page import BasePage
from components.style_manager import StyleManager


class HelpPage(BasePage):
    """帮助页面 - 继承BasePage，提供应用程序帮助信息"""
    
    # 定义信号
    help_action_requested = Signal(str)  # 帮助操作信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
    def load_data(self):
        """加载帮助数据 - 帮助页面暂无需加载数据"""
        pass
        
    def setup_ui(self):
        """设置UI布局 - 从ContentArea.create_help_page提取"""
        main_layout = self.get_main_layout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("帮助与支持")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setStyleSheet("color: #1F2937; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 应用StyleManager滚动条样式
        scrollbar_style = StyleManager.get_scrollbar_style()
        scroll_area.setStyleSheet(scrollbar_style)
        
        # 内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(20)
        
        # 添加帮助内容
        self.add_quick_start_section(content_layout)
        self.add_features_section(content_layout)
        self.add_tips_section(content_layout)
        self.add_troubleshooting_section(content_layout)
        self.add_about_section(content_layout)
        
        # 添加弹簧
        content_layout.addStretch()
        
        scroll_area.setWidget(content_widget)
        main_layout.addWidget(scroll_area)
        
    def add_quick_start_section(self, layout):
        """添加快速开始部分"""
        section = self.create_help_section("🚀 快速开始")
        
        content = """
        <h3>欢迎使用 Prompt 助手！</h3>
        <p>这是一个强大的提示词管理工具，帮助您：</p>
        <ul>
            <li><b>创建</b> - 点击右下角 "+" 按钮创建新的提示词</li>
            <li><b>分类</b> - 使用类别功能整理您的提示词</li>
            <li><b>标签</b> - 为提示词添加标签便于查找</li>
            <li><b>搜索</b> - 使用搜索框快速定位需要的内容</li>
            <li><b>分享</b> - 将优质提示词分享给其他人</li>
        </ul>
        """
        
        self.add_content_to_section(section, content)
        layout.addWidget(section)
        
    def add_features_section(self, layout):
        """添加功能介绍部分"""
        section = self.create_help_section("✨ 主要功能")
        
        content = """
        <h3>核心功能介绍</h3>
        
        <h4>📝 提示词管理</h4>
        <ul>
            <li>创建、编辑、删除提示词</li>
            <li>支持富文本编辑</li>
            <li>媒体文件（图片、视频）支持</li>
            <li>批量操作功能</li>
        </ul>
        
        <h4>🏷️ 分类与标签</h4>
        <ul>
            <li>自定义类别管理</li>
            <li>灵活的标签系统</li>
            <li>智能筛选功能</li>
            <li>收藏夹功能</li>
        </ul>
        
        <h4>📤 导入导出</h4>
        <ul>
            <li>支持 JSON、CSV、Markdown 格式</li>
            <li>批量导入导出</li>
            <li>数据备份与恢复</li>
        </ul>
        """
        
        self.add_content_to_section(section, content)
        layout.addWidget(section)
        
    def add_tips_section(self, layout):
        """添加使用技巧部分"""
        section = self.create_help_section("💡 使用技巧")
        
        content = """
        <h3>高效使用技巧</h3>
        
        <h4>🔍 搜索技巧</h4>
        <ul>
            <li>使用关键词快速查找提示词</li>
            <li>组合使用类别和标签筛选</li>
            <li>利用排序功能按时间或标题排列</li>
        </ul>
        
        <h4>🎯 组织技巧</h4>
        <ul>
            <li>为相关提示词创建专门的类别</li>
            <li>使用一致的标签命名规范</li>
            <li>定期整理和清理不需要的内容</li>
        </ul>
        
        <h4>⚡ 快捷操作</h4>
        <ul>
            <li>双击卡片快速编辑</li>
            <li>右键菜单访问更多选项</li>
            <li>使用 Ctrl+A 全选功能</li>
        </ul>
        """
        
        self.add_content_to_section(section, content)
        layout.addWidget(section)
        
    def add_troubleshooting_section(self, layout):
        """添加故障排除部分"""
        section = self.create_help_section("🔧 常见问题")
        
        content = """
        <h3>故障排除指南</h3>
        
        <h4>❓ 常见问题</h4>
        <ul>
            <li><b>无法创建新提示词？</b><br>
                检查磁盘空间是否充足，确保有写入权限</li>
            <li><b>搜索结果不准确？</b><br>
                尝试使用更具体的关键词，或清空搜索后重试</li>
            <li><b>导入文件失败？</b><br>
                确认文件格式正确，文件没有被其他程序占用</li>
            <li><b>程序运行缓慢？</b><br>
                尝试重启应用，或清理临时缓存文件</li>
        </ul>
        
        <h4>💾 数据备份建议</h4>
        <ul>
            <li>定期导出重要的提示词数据</li>
            <li>使用云存储备份数据库文件</li>
            <li>在重要操作前创建备份</li>
        </ul>
        """
        
        self.add_content_to_section(section, content)
        layout.addWidget(section)
        
    def add_about_section(self, layout):
        """添加关于部分"""
        section = self.create_help_section("ℹ️ 关于应用")
        
        content = """
        <h3>Prompt 助手</h3>
        <p><b>版本：</b> 1.0.0</p>
        <p><b>开发者：</b> Prompt Team</p>
        <p><b>最后更新：</b> 2024年1月</p>
        
        <h4>📧 联系我们</h4>
        <p>如果您遇到问题或有建议，欢迎联系我们：</p>
        <ul>
            <li>邮箱：<EMAIL></li>
            <li>GitHub：github.com/promptassistant</li>
            <li>官网：www.promptassistant.com</li>
        </ul>
        
        <h4>📄 许可证</h4>
        <p>本软件基于 MIT 许可证开源发布。</p>
        """
        
        self.add_content_to_section(section, content)
        layout.addWidget(section)
        
    def create_help_section(self, title):
        """创建帮助部分容器"""
        section = QFrame()
        section.setFrameStyle(QFrame.Box | QFrame.Raised)
        section.setLineWidth(1)
        
        # 应用StyleManager样式
        card_style = StyleManager.get_card_style()
        section.setStyleSheet(f"""
            QFrame {{
                {card_style}
                margin: 5px;
                padding: 15px;
            }}
        """)
        
        # 主布局
        main_layout = QVBoxLayout(section)
        main_layout.setSpacing(10)
        
        # 标题
        title_label = QLabel(title)
        title_label.setFont(QFont("微软雅黑", 14, QFont.Bold))
        title_label.setStyleSheet("color: #374151; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        return section
        
    def add_content_to_section(self, section, html_content):
        """向帮助部分添加内容"""
        content_text = QTextEdit()
        content_text.setHtml(html_content)
        content_text.setReadOnly(True)
        content_text.setMaximumHeight(300)
        
        # 应用StyleManager样式
        content_text.setStyleSheet("""
            QTextEdit {
                background-color: transparent;
                border: none;
                font-family: '微软雅黑';
                font-size: 11px;
                line-height: 1.4;
            }
        """)
        
        # 获取section的布局并添加内容
        layout = section.layout()
        if layout:
            layout.addWidget(content_text) 