#!/usr/bin/env python3
"""
按钮工厂模块
提供统一的按钮创建功能，消除重复代码
"""
from enum import Enum
from typing import Dict, Optional, Callable
from PySide6.QtWidgets import QPushButton
from PySide6.QtCore import QSize, Qt
from PySide6.QtGui import QIcon, QPixmap, QPainter
from PySide6.QtSvg import QSvgRenderer


class ButtonType(Enum):
    """按钮类型枚举"""
    SMALL_ICON = "small_icon"
    FAVORITE = "favorite" 
    PIN = "pin"
    ACTION = "action"
    TOGGLE = "toggle"
    VIEW_TOGGLE = "view_toggle"


class ButtonFactory:
    """按钮工厂类，提供统一的按钮创建方法"""
    
    @staticmethod
    def create_small_icon_button(svg_content: str, tooltip: str) -> QPushButton:
        """创建小图标按钮 - 复制自main.py中的原始实现"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
        # 设置图标
        ButtonFactory._set_button_icon(btn, svg_content, "#6B7280")
        
        # 悬停效果
        def on_enter():
            ButtonFactory._set_button_icon(btn, svg_content, "#3B82F6")
        def on_leave():
            ButtonFactory._set_button_icon(btn, svg_content, "#6B7280")
            
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        
        return btn
    
    @staticmethod
    def create_favorite_button(normal_svg: str, active_svg: str, tooltip: str, is_favorited: bool = False) -> QPushButton:
        """创建收藏按钮 - 复制自main.py中的原始实现"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
        # 设置初始状态
        btn.is_favorited = is_favorited
        btn.normal_svg = normal_svg
        btn.active_svg = active_svg
        
        # 更新图标显示
        def update_button_state():
            if btn.is_favorited:
                ButtonFactory._set_button_icon(btn, btn.active_svg, "#EF4444")  # 红色表示已收藏
                btn.setToolTip("取消收藏")
            else:
                ButtonFactory._set_button_icon(btn, btn.normal_svg, "#6B7280")  # 灰色表示未收藏
                btn.setToolTip("收藏")
        
        # 设置悬停效果
        def on_enter():
            if btn.is_favorited:
                ButtonFactory._set_button_icon(btn, btn.active_svg, "#DC2626")  # 深红色悬停
            else:
                ButtonFactory._set_button_icon(btn, btn.normal_svg, "#EF4444")  # 红色悬停
        
        def on_leave():
            update_button_state()
        
        def on_clicked():
            btn.is_favorited = not btn.is_favorited
            update_button_state()
        
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        btn.clicked.connect(on_clicked)
        
        # 初始化显示
        update_button_state()
        
        return btn
    
    @staticmethod
    def create_pin_button(normal_svg: str, active_svg: str, tooltip: str, is_pinned: bool = False) -> QPushButton:
        """创建置顶按钮 - 复制自main.py中的原始实现"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
        # 设置初始状态
        btn.is_pinned = is_pinned
        btn.normal_svg = normal_svg
        btn.active_svg = active_svg
        
        # 更新图标显示
        def update_button_state():
            if btn.is_pinned:
                ButtonFactory._set_button_icon(btn, btn.active_svg, "#F59E0B")  # 橙色表示已置顶
                btn.setToolTip("取消置顶")
            else:
                ButtonFactory._set_button_icon(btn, btn.normal_svg, "#6B7280")  # 灰色表示未置顶
                btn.setToolTip("置顶")
        
        # 设置悬停效果
        def on_enter():
            if btn.is_pinned:
                ButtonFactory._set_button_icon(btn, btn.active_svg, "#D97706")  # 深橙色悬停
            else:
                ButtonFactory._set_button_icon(btn, btn.normal_svg, "#F59E0B")  # 橙色悬停
        
        def on_leave():
            update_button_state()
        
        def on_clicked():
            btn.is_pinned = not btn.is_pinned
            update_button_state()
        
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        btn.clicked.connect(on_clicked)
        
        # 初始化显示
        update_button_state()
        
        return btn
    
    @staticmethod
    def _set_button_icon(button: QPushButton, svg_content: str, color: str) -> None:
        """设置按钮图标 - 复制自main.py中的原始实现"""
        # 替换SVG中的颜色
        if 'stroke="currentColor"' in svg_content:
            svg_content = svg_content.replace('stroke="currentColor"', f'stroke="{color}"')
        if 'fill="currentColor"' in svg_content:
            svg_content = svg_content.replace('fill="currentColor"', f'fill="{color}"')
        
        # 创建SVG渲染器
        renderer = QSvgRenderer()
        renderer.load(bytes(svg_content, 'utf-8'))
        
        # 创建图标
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置按钮图标
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(12, 12)) 