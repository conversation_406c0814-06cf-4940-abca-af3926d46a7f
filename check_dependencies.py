#!/usr/bin/env python3
"""
依赖检查脚本
快速验证项目所需的关键依赖是否正常工作
"""
import sys

def main():
    """主检查函数"""
    print("🔍 检查项目依赖...")
    
    failed = []
    
    # 检查关键依赖
    dependencies = [
        ("PySide6", "PySide6"),
        ("opencv-python", "cv2"),
        ("AI服务", None),
        ("设置页面", None)
    ]
    
    for name, import_name in dependencies:
        try:
            if name == "AI服务":
                from services import AI_SERVICE_AVAILABLE
                if AI_SERVICE_AVAILABLE:
                    print(f"  ✅ {name} - 正常")
                else:
                    print(f"  ⚠️  {name} - 不可用")
                    failed.append(name)
            elif name == "设置页面":
                from enhanced_settings_help import SettingsPage
                print(f"  ✅ {name} - 正常")
            else:
                __import__(import_name)
                print(f"  ✅ {name} - 正常")
        except Exception as e:
            print(f"  ❌ {name} - 失败: {str(e)}")
            failed.append(name)
    
    if failed:
        print(f"\n❌ 失败的依赖: {', '.join(failed)}")
        print("建议运行: python install_ai_deps.py")
        return False
    else:
        print("\n🎉 所有依赖检查通过！")
        return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 