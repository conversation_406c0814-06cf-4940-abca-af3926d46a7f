#!/usr/bin/env python3
"""
图片查看对话框
从main.py提取的ImageViewerDialog和ZoomableImageView组件
"""
import os
import platform
import subprocess
from pathlib import Path
from PySide6.QtWidgets import (QDialog, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QScrollArea, QApplication, QMessageBox)
from PySide6.QtCore import Qt, QPoint, QEvent, Signal
from PySide6.QtGui import QPixmap, QPainter, QPen, QColor, QTransform

# 导入视频缩略图功能
try:
    from main import extract_video_thumbnail, OPENCV_AVAILABLE
except ImportError:
    # 如果导入失败，使用默认值
    OPENCV_AVAILABLE = False
    def extract_video_thumbnail(video_path, max_size):
        return None


class ZoomableImageView(QWidget):
    """可缩放的图片显示组件，支持滚轮缩放和拖动平移"""
    
    # 自定义信号
    zoomChanged = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.original_pixmap = None
        self.current_pixmap = None
        self.transform = QTransform()
        self.zoom_level = 1.0
        self.min_zoom = 0.1
        self.max_zoom = 5.0
        self.zoom_step = 0.2
        
        # 拖拽相关
        self.dragging = False
        self.last_mouse_pos = QPoint()
        self.drag_offset = QPoint()
        
        # 设置鼠标追踪
        self.setMouseTracking(True)
        
        # 设置样式
        self.setStyleSheet("""
            ZoomableImageView {
                background-color: #2D3748;
                border: none;
            }
        """)
        
    def setPixmap(self, pixmap):
        """设置图片并重置视图"""
        self.original_pixmap = pixmap
        self.current_pixmap = pixmap
        if pixmap:
            self.resetView()
        else:
            self.zoom_level = 1.0
            self.transform = QTransform()
        self.update()
        
    def resetView(self):
        """重置视图到适应窗口状态"""
        if not self.original_pixmap:
            return
            
        self.zoom_level = 1.0
        self.transform = QTransform()
        self.fitToWindow()
        self.zoomChanged.emit()
        
    def fitToWindow(self):
        """适应窗口显示"""
        if not self.original_pixmap or self.width() <= 0 or self.height() <= 0:
            return
            
        # 计算适应窗口的缩放比例
        pixmap_rect = self.original_pixmap.rect()
        widget_rect = self.rect()
        
        scale_x = widget_rect.width() / pixmap_rect.width()
        scale_y = widget_rect.height() / pixmap_rect.height()
        scale = min(scale_x, scale_y)
        
        # 如果图片比窗口小，不放大
        if scale > 1.0:
            scale = 1.0
            
        self.zoom_level = scale
        self.transform = QTransform().scale(scale, scale)
        
        # 居中显示
        scaled_width = pixmap_rect.width() * scale
        scaled_height = pixmap_rect.height() * scale
        x_offset = (widget_rect.width() - scaled_width) / 2
        y_offset = (widget_rect.height() - scaled_height) / 2
        self.transform.translate(x_offset / scale, y_offset / scale)
        
        self.update()
        self.zoomChanged.emit()
        
    def actualSize(self):
        """显示实际大小"""
        if not self.original_pixmap:
            return
            
        self.zoom_level = 1.0
        self.transform = QTransform()
        
        # 居中显示
        widget_rect = self.rect()
        pixmap_rect = self.original_pixmap.rect()
        x_offset = (widget_rect.width() - pixmap_rect.width()) / 2
        y_offset = (widget_rect.height() - pixmap_rect.height()) / 2
        self.transform.translate(x_offset, y_offset)
        
        self.update()
        self.zoomChanged.emit()
        
    def zoomIn(self):
        """放大图片"""
        if self.zoom_level < self.max_zoom:
            self.zoom_level = min(self.zoom_level + self.zoom_step, self.max_zoom)
            self.transform.scale(1 + self.zoom_step / self.zoom_level, 1 + self.zoom_step / self.zoom_level)
            self.update()
            self.zoomChanged.emit()
            
    def zoomOut(self):
        """缩小图片"""
        if self.zoom_level > self.min_zoom:
            self.zoom_level = max(self.zoom_level - self.zoom_step, self.min_zoom)
            self.transform.scale(1 - self.zoom_step / self.zoom_level, 1 - self.zoom_step / self.zoom_level)
            self.update()
            self.zoomChanged.emit()
            
    def getZoomLevel(self):
        """获取当前缩放级别"""
        return self.zoom_level
        
    def paintEvent(self, event):
        """绘制事件"""
        if not self.original_pixmap:
            return
            
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setRenderHint(QPainter.SmoothPixmapTransform)
        
        # 应用变换
        painter.setTransform(self.transform)
        
        # 绘制图片
        painter.drawPixmap(0, 0, self.original_pixmap)
        
    def wheelEvent(self, event):
        """滚轮事件处理"""
        if not self.original_pixmap:
            return
            
        # 获取鼠标位置
        mouse_pos = event.position()
        
        # 计算缩放因子
        zoom_factor = 1.0
        if event.angleDelta().y() > 0:
            # 放大
            if self.zoom_level < self.max_zoom:
                zoom_factor = 1.1
                self.zoom_level = min(self.zoom_level * zoom_factor, self.max_zoom)
            else:
                return
        else:
            # 缩小
            if self.zoom_level > self.min_zoom:
                zoom_factor = 0.9
                self.zoom_level = max(self.zoom_level * zoom_factor, self.min_zoom)
            else:
                return
            
        # 计算鼠标位置相对于图片的偏移
        transform_inv = self.transform.inverted()
        if transform_inv[1]:  # 检查是否可逆
            mouse_in_image = transform_inv[0].map(mouse_pos)
            
            # 应用缩放
            self.transform.scale(zoom_factor, zoom_factor)
            
            # 调整位置以保持鼠标位置不变
            new_transform_inv = self.transform.inverted()
            if new_transform_inv[1]:  # 检查是否可逆
                new_mouse_in_image = new_transform_inv[0].map(mouse_pos)
                offset = new_mouse_in_image - mouse_in_image
                self.transform.translate(offset.x(), offset.y())
        else:
            # 如果变换不可逆，直接应用缩放
            self.transform.scale(zoom_factor, zoom_factor)
        
        self.update()
        self.zoomChanged.emit()
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and self.zoom_level > 1.0:
            self.dragging = True
            self.last_mouse_pos = event.position()
            self.setCursor(Qt.ClosedHandCursor)
        elif event.button() == Qt.RightButton:
            # 右键双击重置视图
            self.resetView()
            
    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.dragging and self.zoom_level > 1.0:
            current_pos = event.position()
            delta = current_pos - self.last_mouse_pos
            
            # 应用平移
            self.transform.translate(delta.x(), delta.y())
            
            # 边界检查
            self.constrainToBounds()
            
            self.last_mouse_pos = current_pos
            self.update()
            
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.dragging = False
            self.setCursor(Qt.ArrowCursor)
            
    def constrainToBounds(self):
        """约束图片在视图边界内"""
        if not self.original_pixmap or self.zoom_level <= 1.0:
            return
            
        # 获取变换后的图片边界
        pixmap_rect = self.original_pixmap.rect()
        transformed_rect = self.transform.mapRect(pixmap_rect)
        widget_rect = self.rect()
        
        # 计算需要的调整
        dx = 0
        dy = 0
        
        if transformed_rect.left() > widget_rect.left():
            dx = widget_rect.left() - transformed_rect.left()
        elif transformed_rect.right() < widget_rect.right():
            dx = widget_rect.right() - transformed_rect.right()
            
        if transformed_rect.top() > widget_rect.top():
            dy = widget_rect.top() - transformed_rect.top()
        elif transformed_rect.bottom() < widget_rect.bottom():
            dy = widget_rect.bottom() - transformed_rect.bottom()
            
        # 应用调整
        if dx != 0 or dy != 0:
            self.transform.translate(dx, dy)
            
    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if self.original_pixmap and self.zoom_level <= 1.0:
            self.fitToWindow()


class ImageViewerDialog(QDialog):
    """图片查看对话框，用于显示和浏览提示词的媒体文件"""
    
    def __init__(self, media_files, current_index=0, parent=None):
        super().__init__(parent)
        self.media_files = media_files
        self.current_index = current_index
        
        # 设置窗口属性
        self.setWindowTitle("媒体查看器")
        self.setMinimumSize(800, 600)
        
        # 初始化UI
        self.init_ui()
        
        # 加载当前图片
        self.load_current_image()
        
        # 居中显示
        self.center_dialog()
        
    def init_ui(self):
        """初始化UI"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 图片显示区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setAlignment(Qt.AlignCenter)
        self.scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #2D3748;
                border: none;
            }
        """)
        
        # 可缩放图片视图
        self.zoomable_image_view = ZoomableImageView()
        
        self.scroll_area.setWidget(self.zoomable_image_view)
        layout.addWidget(self.scroll_area)
        
        # 控制区域
        control_layout = QHBoxLayout()
        control_layout.setContentsMargins(0, 0, 0, 0)
        control_layout.setSpacing(10)
        
        # 上一张按钮
        self.prev_button = QPushButton("上一张")
        self.prev_button.setFixedHeight(36)
        self.prev_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.prev_button.clicked.connect(self.show_previous)
        control_layout.addWidget(self.prev_button)
        
        # 缩放控制按钮
        zoom_layout = QHBoxLayout()
        zoom_layout.setSpacing(5)
        
        # 缩小按钮
        self.zoom_out_button = QPushButton("缩小")
        self.zoom_out_button.setFixedSize(60, 36)
        self.zoom_out_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.zoom_out_button.clicked.connect(self.zoom_out)
        zoom_layout.addWidget(self.zoom_out_button)
        
        # 缩放比例显示
        self.zoom_label = QLabel("100%")
        self.zoom_label.setAlignment(Qt.AlignCenter)
        self.zoom_label.setFixedSize(60, 36)
        self.zoom_label.setStyleSheet("""
            QLabel {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        zoom_layout.addWidget(self.zoom_label)
        
        # 放大按钮
        self.zoom_in_button = QPushButton("放大")
        self.zoom_in_button.setFixedSize(60, 36)
        self.zoom_in_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.zoom_in_button.clicked.connect(self.zoom_in)
        zoom_layout.addWidget(self.zoom_in_button)
        
        # 适应窗口按钮
        self.fit_button = QPushButton("适应")
        self.fit_button.setFixedSize(60, 36)
        self.fit_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.fit_button.clicked.connect(self.fit_to_window)
        zoom_layout.addWidget(self.fit_button)
        
        # 实际大小按钮
        self.actual_button = QPushButton("实际")
        self.actual_button.setFixedSize(60, 36)
        self.actual_button.setStyleSheet("""
            QPushButton {
                background-color: #6B7280;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #4B5563;
            }
        """)
        self.actual_button.clicked.connect(self.actual_size)
        zoom_layout.addWidget(self.actual_button)
        
        control_layout.addLayout(zoom_layout)
        
        # 图片索引指示器
        self.indicator_label = QLabel()
        self.indicator_label.setAlignment(Qt.AlignCenter)
        self.indicator_label.setStyleSheet("""
            QLabel {
                color: #4B5563;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        control_layout.addWidget(self.indicator_label, 1)  # 1表示拉伸因子
        
        # 下一张按钮
        self.next_button = QPushButton("下一张")
        self.next_button.setFixedHeight(36)
        self.next_button.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:disabled {
                background-color: #9CA3AF;
            }
        """)
        self.next_button.clicked.connect(self.show_next)
        control_layout.addWidget(self.next_button)
        
        layout.addLayout(control_layout)
        
        # 安装事件过滤器，用于处理键盘事件
        self.installEventFilter(self)
        
        # 连接缩放视图的信号
        self.zoomable_image_view.zoomChanged.connect(self.update_zoom_display)
        
    def load_current_image(self):
        """加载当前索引的图片或视频缩略图"""
        if not self.media_files or self.current_index >= len(self.media_files):
            self.zoomable_image_view.setPixmap(None)
            self.update_controls()
            return
            
        media_path = self.media_files[self.current_index]
        file_path = Path(media_path)
        
        if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片文件
            pixmap = QPixmap(media_path)
            if not pixmap.isNull():
                # 设置图片到可缩放视图
                self.zoomable_image_view.setPixmap(pixmap)
            else:
                # 创建错误提示图片
                error_pixmap = QPixmap(400, 300)
                error_pixmap.fill(QColor(45, 55, 72))
                painter = QPainter(error_pixmap)
                painter.setPen(QPen(QColor(255, 255, 255)))
                painter.drawText(error_pixmap.rect(), Qt.AlignCenter, "无法加载图片")
                painter.end()
                self.zoomable_image_view.setPixmap(error_pixmap)
        elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件 - 尝试提取第一帧作为缩略图
            print(f"ImageViewerDialog - 正在处理视频文件: {file_path}, OpenCV可用: {OPENCV_AVAILABLE}")
            if OPENCV_AVAILABLE:
                # 尝试获取视频缩略图
                print(f"ImageViewerDialog - 开始提取视频缩略图: {file_path}")
                thumb_pixmap = extract_video_thumbnail(file_path, (self.scroll_area.width() - 20, self.scroll_area.height() - 20))
                print(f"ImageViewerDialog - 提取结果: {'成功' if thumb_pixmap and not thumb_pixmap.isNull() else '失败'}")
                
                if thumb_pixmap and not thumb_pixmap.isNull():
                    # 设置视频缩略图到可缩放视图
                    self.zoomable_image_view.setPixmap(thumb_pixmap)
                    return
            
            # 如果无法提取缩略图，显示默认占位符
            video_pixmap = QPixmap(400, 300)
            video_pixmap.fill(QColor(45, 55, 72))
            painter = QPainter(video_pixmap)
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(video_pixmap.rect(), Qt.AlignCenter, "视频文件\n(点击使用系统播放器播放)")
            painter.end()
            self.zoomable_image_view.setPixmap(video_pixmap)
        else:
            # 其他文件
            other_pixmap = QPixmap(400, 300)
            other_pixmap.fill(QColor(45, 55, 72))
            painter = QPainter(other_pixmap)
            painter.setPen(QPen(QColor(255, 255, 255)))
            painter.drawText(other_pixmap.rect(), Qt.AlignCenter, "不支持的文件格式")
            painter.end()
            self.zoomable_image_view.setPixmap(other_pixmap)
            
        # 更新控件状态
        self.update_controls()
        
    def update_controls(self):
        """更新控件状态"""
        has_multiple_files = len(self.media_files) > 1
        self.prev_button.setEnabled(has_multiple_files)
        self.next_button.setEnabled(has_multiple_files)
        
        if self.media_files:
            self.indicator_label.setText(f"{self.current_index + 1} / {len(self.media_files)}")
        else:
            self.indicator_label.setText("0 / 0")
            
    def show_next(self):
        """显示下一张图片"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index + 1) % len(self.media_files)
        self.load_current_image()
        
    def show_previous(self):
        """显示上一张图片"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index - 1) % len(self.media_files)
        self.load_current_image()
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理键盘事件和点击事件"""
        if event.type() == QEvent.KeyPress:
            key = event.key()
            if key == Qt.Key_Left:
                self.show_previous()
                return True
            elif key == Qt.Key_Right:
                self.show_next()
                return True
            elif key == Qt.Key_Escape:
                self.close()
                return True
        
        # 如果是鼠标点击事件，检查当前媒体是否为视频
        elif event.type() == QEvent.MouseButtonPress and obj == self.zoomable_image_view:
            if self.media_files and self.current_index < len(self.media_files):
                media_path = self.media_files[self.current_index]
                file_path = Path(media_path)
                if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                    # 如果是视频文件，使用系统默认播放器打开
                    success = open_video_with_system_player(file_path)
                    if not success:
                        QMessageBox.warning(self, "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
                    return True
                
        return super().eventFilter(obj, event)
        
    def zoom_in(self):
        """放大图片"""
        self.zoomable_image_view.zoomIn()
        
    def zoom_out(self):
        """缩小图片"""
        self.zoomable_image_view.zoomOut()
        
    def fit_to_window(self):
        """适应窗口显示"""
        self.zoomable_image_view.fitToWindow()
        
    def actual_size(self):
        """显示实际大小"""
        self.zoomable_image_view.actualSize()
        
    def update_zoom_display(self):
        """更新缩放比例显示"""
        zoom_level = self.zoomable_image_view.getZoomLevel()
        self.zoom_label.setText(f"{int(zoom_level * 100)}%")
        
    def center_dialog(self):
        """居中显示对话框"""
        screen = QApplication.primaryScreen().geometry()
        size = self.geometry()
        self.move((screen.width() - size.width()) // 2,
                 (screen.height() - size.height()) // 2)


def open_video_with_system_player(video_path):
    """使用系统默认播放器打开视频文件"""
    video_path = str(video_path)
    
    try:
        if platform.system() == 'Windows':
            os.startfile(video_path)
        elif platform.system() == 'Darwin':  # macOS
            subprocess.call(['open', video_path])
        else:  # Linux
            subprocess.call(['xdg-open', video_path])
        return True
    except Exception as e:
        print(f"无法打开视频文件: {e}")
        return False 