#!/usr/bin/env python3
"""
导出提示词对话框
从main.py提取的ExportPromptDialog组件
"""
import json
import csv
from datetime import datetime
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QComboBox, QPushButton, QMessageBox, QFileDialog)
from PySide6.QtCore import Qt

from views.base_components import BaseDialog


class ExportPromptDialog(BaseDialog):
    """导出提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        
        self.setWindowTitle("导出提示词")
        self.setFixedSize(400, 200)
        
        # 应用统一的对话框样式
        from components.style_manager import StyleManager
        self.setStyleSheet(StyleManager.get_dialog_style('export'))
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("导出提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 格式选择
        format_layout = QHBoxLayout()
        format_label = QLabel("导出格式:")
        format_label.setStyleSheet("color: #374151; font-size: 14px;")
        format_layout.addWidget(format_label)
        
        self.format_combo = QComboBox()
        self.format_combo.addItems(["JSON", "CSV", "Markdown"])
        from components.style_manager import StyleManager
        self.format_combo.setStyleSheet(StyleManager.get_combobox_style())
        format_layout.addWidget(self.format_combo)
        format_layout.addStretch()
        layout.addLayout(format_layout)
        
        # 说明文字
        desc_label = QLabel("选择导出格式后点击导出按钮选择保存位置")
        desc_label.setStyleSheet("color: #6B7280; font-size: 12px;")
        layout.addWidget(desc_label)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        self.export_btn = self.create_primary_button("导出")
        self.export_btn.clicked.connect(self.export_prompts)
        
        cancel_btn = self.create_outline_button("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(self.export_btn)
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def export_prompts(self):
        """导出提示词"""
        if not self.model:
            QMessageBox.warning(self, "错误", "无法获取提示词数据")
            return
        
        # 获取所有提示词
        prompts = self.model.get_all_prompts()
        if not prompts:
            QMessageBox.information(self, "提示", "没有可导出的提示词")
            return
        
        # 获取选择的格式
        format_type = self.format_combo.currentText()
        
        # 设置文件过滤器
        if format_type == "JSON":
            file_filter = "JSON文件 (*.json)"
            default_ext = ".json"
        elif format_type == "CSV":
            file_filter = "CSV文件 (*.csv)"
            default_ext = ".csv"
        elif format_type == "Markdown":
            file_filter = "Markdown文件 (*.md)"
            default_ext = ".md"
        
        # 打开文件保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, 
            "保存提示词文件", 
            f"prompts{default_ext}", 
            file_filter
        )
        
        if not file_path:
            return
        
        try:
            if format_type == "JSON":
                self.export_to_json(prompts, file_path)
            elif format_type == "CSV":
                self.export_to_csv(prompts, file_path)
            elif format_type == "Markdown":
                self.export_to_markdown(prompts, file_path)
            
            QMessageBox.information(self, "成功", f"提示词已成功导出到: {file_path}")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
    
    def export_to_json(self, prompts, file_path):
        """导出为JSON格式"""
        # 转换为字典列表
        data = []
        for prompt in prompts:
            prompt_dict = prompt.to_dict()
            # 移除内部ID，避免导入时冲突
            if 'id' in prompt_dict:
                del prompt_dict['id']
            data.append(prompt_dict)
        
        # 写入文件
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def export_to_csv(self, prompts, file_path):
        """导出为CSV格式"""
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(['标题', '内容', '分类', '标签', '类型', '收藏', '置顶', '创建时间'])
            
            # 写入数据
            for prompt in prompts:
                tags_str = ', '.join(prompt.tags) if prompt.tags else ''
                writer.writerow([
                    prompt.title,
                    prompt.content,
                    prompt.category,
                    tags_str,
                    prompt.type,
                    '是' if prompt.is_favorite else '否',
                    '是' if prompt.is_pinned else '否',
                    prompt.created_at
                ])
    
    def export_to_markdown(self, prompts, file_path):
        """导出为Markdown格式"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("# 提示词集合\n\n")
            f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总计: {len(prompts)} 个提示词\n\n")
            
            for i, prompt in enumerate(prompts, 1):
                f.write(f"## {i}. {prompt.title}\n\n")
                f.write(f"**分类:** {prompt.category}\n\n")
                
                if prompt.tags:
                    tags_str = ', '.join([f'`{tag}`' for tag in prompt.tags])
                    f.write(f"**标签:** {tags_str}\n\n")
                
                f.write(f"**类型:** {prompt.type}\n\n")
                
                if prompt.is_favorite:
                    f.write("**收藏:** 是\n\n")
                if prompt.is_pinned:
                    f.write("**置顶:** 是\n\n")
                
                f.write("**内容:**\n\n")
                f.write(f"{prompt.content}\n\n")
                
                if prompt.created_at:
                    f.write(f"**创建时间:** {prompt.created_at}\n\n")
                
                f.write("---\n\n") 