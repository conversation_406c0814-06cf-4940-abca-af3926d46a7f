#!/usr/bin/env python3
"""
提示词服务模块
提供提示词相关业务逻辑，将数据访问与UI分离
"""
from typing import List, Optional, Dict, Set, Any
import json
import time
from datetime import datetime

from model import Prompt, PromptModel, PromptHistory
from utils.error_handler import log_error, DatabaseError, DataError
from utils.event_bus import EventType, publish_event
from interfaces.service_interfaces import IPromptService

class PromptService(IPromptService):
    """提示词服务类，处理提示词相关业务逻辑"""
    
    def __init__(self, model: PromptModel):
        """
        初始化提示词服务
        :param model: 提示词数据模型
        """
        self.model = model
        self._cache = {
            'all_prompts': [],
            'last_updated': 0
        }
        self._cache_ttl = 5  # 缓存有效期（秒）
        
    def get_all_prompts(self, use_cache: bool = True) -> List[Prompt]:
        """
        获取所有提示词
        :param use_cache: 是否使用缓存
        :return: 提示词列表
        """
        current_time = time.time()
        
        # 如果启用缓存且缓存未过期，则使用缓存
        if use_cache and self._cache['all_prompts'] and \
           current_time - self._cache['last_updated'] < self._cache_ttl:
            return self._cache['all_prompts']
        
        try:
            prompts = self.model.get_all_prompts()
            
            # 更新缓存
            self._cache['all_prompts'] = prompts
            self._cache['last_updated'] = current_time
            
            return prompts
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取提示词列表失败", str(e))
    
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        """
        获取特定提示词
        :param prompt_id: 提示词ID
        :return: 提示词对象，如果不存在则返回None
        """
        try:
            return self.model.get_prompt(prompt_id)
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"获取提示词ID={prompt_id}失败", str(e))
    
    def create_prompt(self, prompt: Prompt) -> int:
        """
        创建新的提示词
        :param prompt: 提示词对象
        :return: 新创建的提示词ID
        """
        try:
            # 数据验证
            if not prompt.title.strip():
                raise DataError("提示词标题不能为空")
            
            # 设置创建和更新时间
            now = datetime.now().isoformat()
            prompt.created_at = now
            prompt.updated_at = now
            
            # 保存到数据库
            prompt_id = self.model.add_prompt(prompt)
            
            # 清除缓存
            self._invalidate_cache()
            
            # 发布事件
            publish_event(EventType.PROMPT_CREATED, prompt_id)
            
            return prompt_id
        except DataError as e:
            # 重新抛出数据验证错误
            raise
        except Exception as e:
            log_error(e)
            raise DatabaseError("创建提示词失败", str(e))
    
    def update_prompt(self, prompt: Prompt) -> bool:
        """
        更新提示词
        :param prompt: 提示词对象
        :return: 是否更新成功
        """
        try:
            # 数据验证
            if not prompt.id:
                raise DataError("提示词ID不能为空")
            
            if not prompt.title.strip():
                raise DataError("提示词标题不能为空")
            
            # 更新时间
            prompt.updated_at = datetime.now().isoformat()
            
            # 保存到数据库
            self.model.update_prompt(prompt)
            
            # 清除缓存
            self._invalidate_cache()
            
            # 发布事件
            publish_event(EventType.PROMPT_UPDATED, prompt.id)
            
            return True
        except DataError as e:
            # 重新抛出数据验证错误
            raise
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"更新提示词ID={prompt.id}失败", str(e))
    
    def delete_prompt(self, prompt_id: int) -> bool:
        """
        删除提示词
        :param prompt_id: 提示词ID
        :return: 是否删除成功
        """
        try:
            # 检查提示词是否存在
            prompt = self.get_prompt(prompt_id)
            if not prompt:
                raise DataError(f"提示词ID={prompt_id}不存在")
            
            # 从数据库删除
            self.model.delete_prompt(prompt_id)
            
            # 清除缓存
            self._invalidate_cache()
            
            # 发布事件
            publish_event(EventType.PROMPT_DELETED, prompt_id)
            
            return True
        except DataError as e:
            # 重新抛出数据验证错误
            raise
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"删除提示词ID={prompt_id}失败", str(e))
    
    def toggle_favorite(self, prompt_id: int) -> bool:
        """
        切换提示词的收藏状态
        :param prompt_id: 提示词ID
        :return: 新的收藏状态
        """
        try:
            result = self.model.toggle_favorite(prompt_id)
            
            # 清除缓存
            self._invalidate_cache()
            
            # 获取更新后的提示词
            prompt = self.get_prompt(prompt_id)
            
            # 发布事件
            if prompt and prompt.is_favorite:
                publish_event(EventType.PROMPT_FAVORITED, prompt_id)
            else:
                publish_event(EventType.PROMPT_UNFAVORITED, prompt_id)
            
            return result
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"切换提示词ID={prompt_id}的收藏状态失败", str(e))
    
    def toggle_pin(self, prompt_id: int) -> bool:
        """
        切换提示词的置顶状态
        :param prompt_id: 提示词ID
        :return: 新的置顶状态
        """
        try:
            result = self.model.toggle_pin(prompt_id)
            
            # 清除缓存
            self._invalidate_cache()
            
            # 获取更新后的提示词
            prompt = self.get_prompt(prompt_id)
            
            # 发布事件
            if prompt and prompt.is_pinned:
                publish_event(EventType.PROMPT_PINNED, prompt_id)
            else:
                publish_event(EventType.PROMPT_UNPINNED, prompt_id)
            
            return result
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"切换提示词ID={prompt_id}的置顶状态失败", str(e))
    
    def get_favorite_prompts(self) -> List[Prompt]:
        """
        获取收藏的提示词列表
        :return: 收藏的提示词列表
        """
        try:
            return self.model.get_favorite_prompts()
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取收藏提示词列表失败", str(e))
    
    def search_prompts(self, keyword: str) -> List[Prompt]:
        """
        根据关键词搜索提示词
        :param keyword: 搜索关键词
        :return: 匹配的提示词列表
        """
        try:
            return self.model.search_prompts(keyword)
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"搜索提示词关键词='{keyword}'失败", str(e))
    
    def get_prompt_history(self, prompt_id: int) -> List[PromptHistory]:
        """
        获取提示词的历史版本列表
        :param prompt_id: 提示词ID
        :return: 历史版本列表
        """
        try:
            return self.model.get_prompt_history(prompt_id)
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"获取提示词ID={prompt_id}的历史版本失败", str(e))
    
    def restore_prompt_from_history(self, history_id: int) -> bool:
        """
        从历史版本恢复提示词
        :param history_id: 历史版本ID
        :return: 是否恢复成功
        """
        try:
            result = self.model.restore_prompt_from_history(history_id)
            
            # 清除缓存
            self._invalidate_cache()
            
            # 查找历史版本对应的提示词ID
            history = self.model.get_history_version(history_id)
            if history and result:
                # 发布事件
                publish_event(EventType.PROMPT_UPDATED, history.prompt_id)
            
            return result
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"从历史版本ID={history_id}恢复提示词失败", str(e))
    
    def clear_all_prompts(self) -> bool:
        """
        清空所有提示词
        :return: 是否清空成功
        """
        try:
            result = self.model.clear_all_prompts()
            
            # 清除缓存
            self._invalidate_cache()
            
            # 发布事件
            publish_event(EventType.PROMPTS_LOADED, [])
            
            return result
        except Exception as e:
            log_error(e)
            raise DatabaseError("清空所有提示词失败", str(e))
    
    def filter_prompts(self, filters: Dict[str, Any]) -> List[Prompt]:
        """
        根据筛选条件过滤提示词
        :param filters: 筛选条件，可包含category, tags, is_favorite, search_text等
        :return: 过滤后的提示词列表
        """
        try:
            # 获取所有提示词
            all_prompts = self.get_all_prompts()
            result = all_prompts.copy()
            
            # 应用筛选条件
            if 'category' in filters and filters['category'] != '全部':
                result = [p for p in result if p.category == filters['category']]
            
            if 'tags' in filters and filters['tags']:
                # 标签筛选支持多选，使用交集逻辑
                selected_tags = set(filters['tags'])
                if selected_tags:
                    result = [p for p in result if set(p.tags).intersection(selected_tags)]
            
            if 'is_favorite' in filters and filters['is_favorite']:
                result = [p for p in result if p.is_favorite]
            
            if 'search_text' in filters and filters['search_text']:
                search_text = filters['search_text'].lower()
                result = [p for p in result if 
                          search_text in p.title.lower() or 
                          search_text in p.content.lower() or
                          any(search_text in tag.lower() for tag in p.tags)]
            
            return result
        except Exception as e:
            log_error(e)
            raise DataError("过滤提示词失败", str(e))
    
    def sort_prompts(self, prompts: List[Prompt], sort_method: str = "updated_desc") -> List[Prompt]:
        """
        对提示词列表排序
        :param prompts: 提示词列表
        :param sort_method: 排序方法
                           updated_desc: 按更新时间降序（默认）
                           created_desc: 按创建时间降序
                           title_asc: 按标题升序
                           category_asc: 按分类升序
        :return: 排序后的提示词列表
        """
        try:
            result = prompts.copy()
            
            # 首先，置顶的提示词始终排在最前面
            pinned = [p for p in result if p.is_pinned]
            not_pinned = [p for p in result if not p.is_pinned]
            
            # 然后根据指定的方法排序非置顶提示词
            if sort_method == "updated_desc":
                not_pinned.sort(key=lambda p: p.updated_at or "", reverse=True)
            elif sort_method == "created_desc":
                not_pinned.sort(key=lambda p: p.created_at or "", reverse=True)
            elif sort_method == "title_asc":
                not_pinned.sort(key=lambda p: p.title.lower())
            elif sort_method == "category_asc":
                not_pinned.sort(key=lambda p: (p.category.lower(), p.title.lower()))
            
            # 合并结果
            return pinned + not_pinned
        except Exception as e:
            log_error(e)
            raise DataError("排序提示词失败", str(e))
    
    def _invalidate_cache(self):
        """清除缓存"""
        self._cache['all_prompts'] = []
        self._cache['last_updated'] = 0 