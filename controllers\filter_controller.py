#!/usr/bin/env python3
"""
过滤控制器
专门处理标签和分类筛选相关逻辑
"""
from typing import List, Dict, Set, Any, Optional, Tuple
from PySide6.QtCore import QObject, Signal, Slot

from services.tag_service import TagService
from services.category_service import CategoryService
from utils.error_handler import log_error
from utils.event_bus import EventType, publish_event, Event


class FilterController(QObject):
    """筛选控制器，处理筛选相关逻辑"""
    
    # 定义信号
    tag_list_updated = Signal(list)  # 标签列表更新
    category_list_updated = Signal(list)  # 分类列表更新
    tag_filter_changed = Signal(set)  # 标签筛选变化
    category_filter_changed = Signal(str)  # 分类筛选变化
    favorite_filter_changed = Signal(bool)  # 收藏筛选变化
    
    def __init__(self, tag_service: TagService, category_service: CategoryService):
        super().__init__()
        
        # 服务
        self.tag_service = tag_service
        self.category_service = category_service
        
        # 当前筛选状态
        self.current_filters = {
            'category': '全部',
            'tags': set(),
            'is_favorite': False,
            'search_text': ""
        }
        
        # 设置事件处理
        self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        from utils.event_bus import event_bus
        
        # 筛选器相关事件
        event_bus.subscribe(EventType.FILTER_CHANGED, self._on_filter_changed)
        event_bus.subscribe(EventType.CATEGORY_FILTER_CHANGED, self._on_category_filter_changed)
        event_bus.subscribe(EventType.TAG_FILTER_CHANGED, self._on_tag_filter_changed)
        event_bus.subscribe(EventType.FAVORITE_FILTER_CHANGED, self._on_favorite_filter_changed)
        event_bus.subscribe(EventType.SEARCH_FILTER_CHANGED, self._on_search_filter_changed)
    
    #----------------------------------------------------------------------
    # 事件处理方法
    #----------------------------------------------------------------------
    
    def _on_filter_changed(self, event: Event):
        """处理筛选条件变化事件"""
        filters = event.data
        self.current_filters.update(filters)
        
        # 发布特定筛选事件
        if 'category' in filters:
            publish_event(EventType.CATEGORY_FILTER_CHANGED, filters['category'])
        
        if 'tags' in filters:
            publish_event(EventType.TAG_FILTER_CHANGED, filters['tags'])
        
        if 'is_favorite' in filters:
            publish_event(EventType.FAVORITE_FILTER_CHANGED, filters['is_favorite'])
        
        if 'search_text' in filters:
            publish_event(EventType.SEARCH_FILTER_CHANGED, filters['search_text'])
    
    def _on_category_filter_changed(self, event: Event):
        """处理分类筛选变化事件"""
        category = event.data
        self.current_filters['category'] = category
        self.category_filter_changed.emit(category)
    
    def _on_tag_filter_changed(self, event: Event):
        """处理标签筛选变化事件"""
        tags_data = event.data
        
        if isinstance(tags_data, str):
            # 单个标签切换
            tag = tags_data
            if tag in self.current_filters['tags']:
                self.current_filters['tags'].remove(tag)
            else:
                self.current_filters['tags'].add(tag)
        elif isinstance(tags_data, (list, set)):
            # 设置整个标签集合
            self.current_filters['tags'] = set(tags_data)
        
        self.tag_filter_changed.emit(self.current_filters['tags'])
    
    def _on_favorite_filter_changed(self, event: Event):
        """处理收藏筛选变化事件"""
        is_favorite = event.data
        self.current_filters['is_favorite'] = is_favorite
        self.favorite_filter_changed.emit(is_favorite)
    
    def _on_search_filter_changed(self, event: Event):
        """处理搜索筛选变化事件"""
        search_text = event.data
        self.current_filters['search_text'] = search_text
    
    #----------------------------------------------------------------------
    # 公共方法
    #----------------------------------------------------------------------
    
    def load_filter_data(self):
        """加载筛选数据（标签和分类）"""
        try:
            # 加载标签
            tags = self.tag_service.get_all_tags()
            self.tag_list_updated.emit(tags)
            
            # 加载分类
            categories = self.category_service.get_all_categories()
            self.category_list_updated.emit(categories)
        except Exception as e:
            log_error(e)
    
    def apply_category_filter(self, category: str):
        """
        应用分类筛选
        :param category: 分类名称，如果为空或"全部"则清除筛选
        """
        # 如果点击的是当前选中的分类，则取消选择（返回到"全部"）
        if category == self.current_filters['category'] and category != "全部":
            category = "全部"
        
        publish_event(EventType.CATEGORY_FILTER_CHANGED, category)
    
    def apply_tag_filter(self, tag: str):
        """
        应用标签筛选（切换单个标签）
        :param tag: 标签名称
        """
        publish_event(EventType.TAG_FILTER_CHANGED, tag)
    
    def set_tag_filters(self, tags: List[str]):
        """
        设置标签筛选（一次性设置多个标签）
        :param tags: 标签列表
        """
        publish_event(EventType.TAG_FILTER_CHANGED, set(tags))
    
    def toggle_favorite_filter(self):
        """切换收藏筛选状态"""
        is_favorite = not self.current_filters['is_favorite']
        publish_event(EventType.FAVORITE_FILTER_CHANGED, is_favorite)
    
    def apply_search_filter(self, search_text: str):
        """
        应用搜索筛选
        :param search_text: 搜索关键词
        """
        publish_event(EventType.SEARCH_FILTER_CHANGED, search_text)
    
    def clear_filters(self):
        """清除所有筛选条件"""
        publish_event(EventType.FILTER_CHANGED, {
            'category': '全部',
            'tags': set(),
            'is_favorite': False,
            'search_text': ""
        })
    
    def get_current_filters(self) -> Dict[str, Any]:
        """
        获取当前筛选条件
        :return: 筛选条件字典
        """
        return self.current_filters.copy()
    
    #----------------------------------------------------------------------
    # 工具方法
    #----------------------------------------------------------------------
    
    def get_tag_counts(self) -> Dict[str, int]:
        """
        获取标签及其使用次数
        :return: 标签及使用次数字典，格式为 {tag: count}
        """
        return self.tag_service.get_tag_counts()
    
    def get_category_counts(self) -> Dict[str, int]:
        """
        获取分类及其数量
        :return: 分类及数量字典，格式为 {category: count}
        """
        return self.category_service.get_category_counts()
    
    def get_related_tags(self) -> Dict[str, int]:
        """
        获取与当前选中标签相关的标签及其关联度
        :return: 相关标签及关联度字典，格式为 {tag: count}
        """
        selected_tags = list(self.current_filters['tags'])
        return self.tag_service.get_related_tags(selected_tags) 