#!/usr/bin/env python3
"""
自定义标题栏组件
从main.py提取的CustomTitleBar类
"""
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel, QPushButton
from PySide6.QtCore import Qt, QPoint


class CustomTitleBar(QWidget):
    """自定义标题栏组件 (400x50) - 提取自main.py"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(400, 50)
        self.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        self.init_ui()
        
        # 用于拖拽窗口
        self.drag_position = QPoint()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 0, 10, 0)
        layout.setSpacing(10)
        
        # 标题标签
        self.title_label = QLabel("Prompt收藏助手")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(self.title_label)
        
        layout.addStretch()
        
        # 窗口控制按钮
        self.create_control_buttons(layout)
        
    def create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: #6B7280;
                font-size: 16px;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setStyleSheet(button_style)
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        close_style = button_style.replace("#E5E7EB", "#FEE2E2").replace("#D1D5DB", "#FECACA")
        close_style = close_style.replace("color: #6B7280;", "color: #DC2626;")
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet(close_style)
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def minimize_window(self):
        if self.parent_window:
            self.parent_window.showMinimized()
            
    def close_window(self):
        if self.parent_window:
            self.parent_window.close()
            
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title) 