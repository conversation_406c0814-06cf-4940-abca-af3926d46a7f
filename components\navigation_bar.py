#!/usr/bin/env python3
"""
导航栏组件
从main.py提取的NavigationBar类
"""
from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton, QGraphicsDropShadowEffect
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor
from PySide6.QtSvg import QSvgRenderer

# 导入必需的按钮组件（SvgIconButton在main.py中定义，暂时需要保持引用）
# 为了保持功能一致，暂时使用现有的组件结构


class SvgIconButton(QPushButton):
    """SVG图标按钮 - 支持状态变化的图标颜色"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.tooltip_text = tooltip
        self.key = key
        self.is_active_state = False
        self.is_hover_state = False
        
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        
        # 设置样式（无背景色）
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
        """)
        
        # 初始化阴影效果
        self.setup_shadow()
        
        # 设置图标
        self.update_icon()
        
    def setup_shadow(self):
        """初始化阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.setGraphicsEffect(self.shadow_effect)
        self.update_shadow()
        
    def update_shadow(self):
        """更新阴影效果"""
        if self.is_active_state:
            # 激活状态：微信绿色阴影，较强
            self.shadow_effect.setColor(QColor(7, 193, 96, 100))  # 微信绿色，透明度100
            self.shadow_effect.setBlurRadius(12)
            self.shadow_effect.setOffset(0, 4)
        elif self.is_hover_state:
            # 悬停状态：灰色阴影，中等
            self.shadow_effect.setColor(QColor(156, 163, 175, 80))  # 灰色，透明度80
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setOffset(0, 2)
        else:
            # 默认状态：黑色阴影，较弱
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 黑色，透明度60
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setOffset(0, 1)
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active_state = active
        self.update_icon()
        self.update_shadow()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active_state:  # 只有非激活状态才响应悬停
            self.is_hover_state = True
            self.update_icon()
            self.update_shadow()
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        if not self.is_active_state:  # 只有非激活状态才响应悬停
            self.is_hover_state = False
            self.update_icon()
            self.update_shadow()
        super().leaveEvent(event)
            
    def update_icon(self):
        """更新图标颜色"""
        # 根据状态设置图标颜色
        if self.is_active_state:
            color = "#07C160"  # 微信绿色 - 激活状态
        elif self.is_hover_state:
            color = "#9CA3AF"  # 灰色 - 悬停状态
        else:
            color = "#000000"  # 黑色 - 默认状态
        
        # 替换SVG中的currentColor
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(24, 24))


class NavigationBar(QWidget):
    """左侧导航栏组件 (60x800) - 提取自main.py"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(60, 800)
        self.current_page = "home"
        self.nav_buttons = {}
        self.init_ui()
        
    def get_svg_icons(self):
        """获取SVG图标定义"""
        return {
            "home": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "trash": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "settings": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "help": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
    def init_ui(self):
        self.setStyleSheet("""
            QWidget {
                background-color: #1F2937;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(0)
        
        # Logo区域
        logo_label = QLabel("PA")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet("""
            QLabel {
                color: #3B82F6;
                font-size: 18px;
                font-weight: bold;
                background-color: #374151;
                border-radius: 8px;
                padding: 8px;
                margin: 0 10px 30px 10px;
            }
        """)
        layout.addWidget(logo_label)
        
        # 主要导航按钮区域（居中显示）
        main_nav_layout = QVBoxLayout()
        main_nav_layout.setSpacing(15)
        main_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 主要导航项（首页和回收站）
        main_nav_items = [
            ("home", "首页"),
            ("trash", "回收站")
        ]
        
        svg_icons = self.get_svg_icons()
        
        for key, tooltip in main_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            main_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(main_nav_layout)
        layout.addStretch()
        
        # 底部功能按钮区域
        bottom_nav_layout = QVBoxLayout()
        bottom_nav_layout.setSpacing(15)
        bottom_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 底部导航项（设置和帮助）
        bottom_nav_items = [
            ("settings", "设置"),
            ("help", "帮助")
        ]
        
        for key, tooltip in bottom_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            bottom_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(bottom_nav_layout)
        
        # 设置默认选中状态
        self.set_active_button("home")
        
    def create_svg_button(self, svg_content, tooltip, key):
        """创建SVG图标按钮"""
        btn = SvgIconButton(svg_content, tooltip, key, self)
        btn.clicked.connect(lambda: self.on_nav_clicked(key))
        return btn
            
    def on_nav_clicked(self, key):
        """导航按钮点击事件"""
        self.set_active_button(key)
        if self.parent_window:
            self.parent_window.switch_page(key)
            
    def set_active_button(self, key):
        """设置活动按钮"""
        self.current_page = key
        for btn_key, btn in self.nav_buttons.items():
            is_active = btn_key == key
            btn.set_active(is_active) 