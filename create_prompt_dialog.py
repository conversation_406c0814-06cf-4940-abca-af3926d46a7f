#!/usr/bin/env python3
"""
新建提示词对话框
无标题栏设计，功能完整的提示词创建界面
"""
import sys
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, 
                               QLineEdit, QTextEdit, QComboBox, QRadioButton, 
                               QPushButton, QLabel, QFrame, QScrollArea, 
                               QButtonGroup, QWidget, QGridLayout, QFileDialog,
                               QMessageBox, QCompleter, QListWidget, QListWidgetItem,
                               QCheckBox, QGroupBox, QSplitter, QApplication)
from PySide6.QtCore import Qt, QSize, QPoint, QStringListModel, Signal, QEvent
from PySide6.QtGui import QIcon, QPixmap, QPainter, QFont, QColor, QImage
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtWidgets import QGraphicsDropShadowEffect
from pathlib import Path

# 尝试直接导入OpenCV，避免循环导入
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("在create_prompt_dialog中: OpenCV已成功加载")
except ImportError:
    OPENCV_AVAILABLE = False
    print("在create_prompt_dialog中: 未检测到OpenCV，视频缩略图功能不可用")

class FollowingDialog(QDialog):
    """
    跟随主窗口移动的对话框基类
    
    特性：
    1. 非模态显示，不阻塞主窗口操作
    2. 跟随主窗口移动
    3. 默认显示在主窗口右侧
    """
    
    def __init__(self, parent=None):
        super().__init__(parent, Qt.WindowType.Window)
        # 设置为非模态对话框
        self.setModal(False)
        
        # 记录与父窗口的相对位置
        self.relative_x = 0
        self.relative_y = 0
        
        # 安装事件过滤器到父窗口
        if self.parent():
            self.parent().installEventFilter(self)
            
            # 计算初始相对位置
            self.update_relative_position()
    
    def update_relative_position(self):
        """更新与父窗口的相对位置"""
        if self.parent():
            parent_pos = self.parent().pos()
            dialog_pos = self.pos()
            self.relative_x = dialog_pos.x() - parent_pos.x()
            self.relative_y = dialog_pos.y() - parent_pos.y()
    
    def eventFilter(self, obj, event):
        """事件过滤器，监听父窗口的移动事件"""
        if obj == self.parent() and event.type() == QEvent.Move:
            # 父窗口移动时，对话框跟随移动
            new_x = self.parent().x() + self.relative_x
            new_y = self.parent().y() + self.relative_y
            self.move(new_x, new_y)
            return False  # 继续处理事件
        return super().eventFilter(obj, event)
    
    def showEvent(self, event):
        """显示事件，确保对话框显示在正确位置"""
        super().showEvent(event)
        # 显示在主窗口右侧
        position_dialog_right(self, self.parent())
        # 更新相对位置
        self.update_relative_position()
    
    def closeEvent(self, event):
        """关闭事件，移除事件过滤器"""
        if self.parent():
            self.parent().removeEventFilter(self)
        super().closeEvent(event)

def position_dialog_right(dialog, parent):
    """
    将对话框定位在父窗口右侧
    
    参数:
        dialog: 要定位的对话框
        parent: 父窗口
    """
    if parent:
        parent_rect = parent.geometry()
        
        # 计算对话框在主窗口右侧的位置
        x = parent_rect.x() + parent_rect.width()  # 主窗口右边缘
        y = parent_rect.y()  # 与主窗口顶部对齐
        
        # 调整位置，确保对话框不会超出屏幕
        screen = QApplication.primaryScreen().geometry()
        if x + dialog.width() > screen.width():
            # 如果超出屏幕右侧，则显示在主窗口左侧
            x = parent_rect.x() - dialog.width()
            # 如果左侧也放不下，则居中显示
            if x < 0:
                x = parent_rect.x() + (parent_rect.width() - dialog.width()) // 2
                y = parent_rect.y() + (parent_rect.height() - dialog.height()) // 2
        
        dialog.move(x, y)

# 视频缩略图提取函数 - 避免循环导入
def extract_video_thumbnail(video_path, max_size=(150, 150)):
    """
    从视频文件提取第一帧作为缩略图
    
    参数:
        video_path: 视频文件路径
        max_size: 缩略图最大尺寸 (宽, 高)
        
    返回:
        QPixmap对象或None（如果失败）
    """
    if not OPENCV_AVAILABLE:
        print(f"在create_prompt_dialog中: 无法提取视频缩略图: OpenCV不可用")
        return None
    
    try:
        print(f"在create_prompt_dialog中: 尝试提取视频缩略图: {video_path}")
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        
        # 检查视频是否成功打开
        if not cap.isOpened():
            print(f"在create_prompt_dialog中: 无法打开视频文件: {video_path}")
            return None
        
        # 读取第一帧
        ret, frame = cap.read()
        cap.release()
        
        if not ret or frame is None:
            print(f"在create_prompt_dialog中: 无法读取视频第一帧: {video_path}")
            return None
        
        # 将BGR转换为RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 调整尺寸
        h, w, _ = frame_rgb.shape
        aspect_ratio = w / h
        
        if aspect_ratio > 1:  # 宽 > 高
            new_w = min(max_size[0], w)
            new_h = int(new_w / aspect_ratio)
        else:  # 高 >= 宽
            new_h = min(max_size[1], h)
            new_w = int(new_h * aspect_ratio)
        
        if new_w > 0 and new_h > 0:
            resized_frame = cv2.resize(frame_rgb, (new_w, new_h))
            
            # 创建QImage
            h, w, c = resized_frame.shape
            qimg = QImage(resized_frame.data, w, h, w * c, QImage.Format_RGB888)
            
            # 创建QPixmap
            pixmap = QPixmap.fromImage(qimg)
            print(f"在create_prompt_dialog中: 成功提取视频缩略图: {video_path}, 尺寸: {pixmap.width()}x{pixmap.height()}")
            return pixmap
        else:
            print(f"在create_prompt_dialog中: 调整尺寸失败: {new_w}x{new_h}")
            return None
    except Exception as e:
        print(f"在create_prompt_dialog中: 提取视频缩略图出错: {e}")
        return None

class MediaPreviewWidget(QWidget):
    """媒体文件预览组件"""
    
    def __init__(self, model, prompt=None, parent=None, prompt_id=None):
        super().__init__(parent)
        self.file_path = model
        self.setFixedSize(120, 90)
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(4, 4, 4, 4)
        layout.setSpacing(2)
        
        # 预览区域
        self.preview_label = QLabel()
        self.preview_label.setFixedSize(112, 70)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: #F9FAFB;
            }
        """)
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setScaledContents(True)
        
        # 加载预览
        self.load_preview()
        layout.addWidget(self.preview_label)
        
        # 文件名
        file_name = Path(self.file_path).name
        if len(file_name) > 15:
            file_name = file_name[:12] + "..."
        
        name_label = QLabel(file_name)
        name_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        """)
        name_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(name_label)
        
        # 删除按钮
        delete_btn = QPushButton("×")
        delete_btn.setFixedSize(16, 16)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_btn.clicked.connect(self.delete_file)
        
        # 将删除按钮定位到右上角
        delete_btn.setParent(self)
        delete_btn.move(100, 4)
        
    def load_preview(self):
        """加载文件预览"""
        file_path = Path(self.file_path)
        
        if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片预览
            pixmap = QPixmap(self.file_path)
            if not pixmap.isNull():
                # 按比例缩放图片以适应预览区域
                scaled_pixmap = pixmap.scaled(112, 70, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                self.preview_label.setPixmap(scaled_pixmap)
            else:
                self.preview_label.setText("图片")
        elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频预览 - 尝试提取第一帧作为缩略图
            if OPENCV_AVAILABLE:
                # 尝试提取视频缩略图
                thumb_pixmap = extract_video_thumbnail(file_path, (112, 70))
                
                if thumb_pixmap and not thumb_pixmap.isNull():
                    # 创建一个容器大小的图片
                    result_pixmap = QPixmap(112, 70)
                    result_pixmap.fill(Qt.transparent)
                    
                    # 绘制缩略图和播放标志
                    painter = QPainter(result_pixmap)
                    painter.setRenderHint(QPainter.Antialiasing)
                    
                    # 获取缩略图大小
                    pixmap_width = thumb_pixmap.width()
                    pixmap_height = thumb_pixmap.height()
                    
                    # 计算居中位置
                    x = (112 - pixmap_width) // 2
                    y = (70 - pixmap_height) // 2
                    
                    # 绘制视频缩略图
                    painter.drawPixmap(x, y, thumb_pixmap)
                    
                    # 绘制半透明遮罩
                    painter.setBrush(QColor(0, 0, 0, 100))  # 半透明黑色
                    painter.setPen(Qt.NoPen)
                    painter.drawRect(0, 0, 112, 70)
                    
                    # 绘制播放按钮
                    play_size = 30
                    center_x = 112 // 2
                    center_y = 70 // 2
                    
                    # 绘制播放三角形
                    painter.setBrush(QColor(255, 255, 255))
                    painter.setPen(Qt.NoPen)
                    points = [
                        QPoint(center_x - play_size // 3, center_y - play_size // 2),
                        QPoint(center_x + play_size // 2, center_y),
                        QPoint(center_x - play_size // 3, center_y + play_size // 2)
                    ]
                    painter.drawPolygon(points)
                    
                    painter.end()
                    self.preview_label.setPixmap(result_pixmap)
                    return
            
            # 如果无法提取缩略图，显示默认的播放图标
            self.preview_label.setText("▶\n视频")
            self.preview_label.setStyleSheet("""
                QLabel {
                    color: #6B7280;
                    font-size: 16px;
                    font-weight: bold;
                    text-align: center;
                    background-color: #F3F4F6;
                    border: 1px solid #E5E7EB;
                    border-radius: 4px;
                }
            """)
        else:
            self.preview_label.setText("文件")
            
    def delete_file(self):
        """删除文件"""
        if hasattr(self.parent(), 'remove_media_file'):
            self.parent().remove_media_file(self.file_path)

class MediaUploadArea(QWidget):
    """媒体上传区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.media_files = []
        self.init_ui()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 标题
        title_label = QLabel("媒体文件")
        title_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 4px;
            }
        """)
        layout.addWidget(title_label)
        
        # 上传按钮
        upload_btn = QPushButton("+ 添加媒体文件")
        upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #6B7280;
                border: 2px dashed #D1D5DB;
                border-radius: 6px;
                padding: 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
                border-color: #9CA3AF;
            }
        """)
        upload_btn.clicked.connect(self.select_media_files)
        layout.addWidget(upload_btn)
        
        # 预览区域
        self.preview_scroll = QScrollArea()
        self.preview_scroll.setWidgetResizable(True)
        self.preview_scroll.setMaximumHeight(200)
        self.preview_scroll.setStyleSheet("""
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
            }
        """)
        
        self.preview_widget = QWidget()
        self.preview_layout = QGridLayout(self.preview_widget)
        self.preview_layout.setSpacing(8)
        
        self.preview_scroll.setWidget(self.preview_widget)
        layout.addWidget(self.preview_scroll)
        
        # 初始隐藏预览区域
        self.preview_scroll.hide()
        
    def select_media_files(self):
        """选择媒体文件"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "选择媒体文件",
            "",
            "媒体文件 (*.jpg *.jpeg *.png *.gif *.bmp *.mp4 *.avi *.mov *.wmv *.flv *.webm);;所有文件 (*)"
        )
        
        for file_path in file_paths:
            if file_path not in self.media_files:
                self.add_media_file(file_path)
                
    def add_media_file(self, file_path):
        """添加媒体文件"""
        self.media_files.append(file_path)
        self.update_preview()
        
    def remove_media_file(self, file_path):
        """移除媒体文件"""
        if file_path in self.media_files:
            self.media_files.remove(file_path)
            self.update_preview()
            
    def update_preview(self):
        """更新预览显示"""
        # 清除现有预览
        for i in reversed(range(self.preview_layout.count())):
            child = self.preview_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
                
        # 添加新预览
        if self.media_files:
            self.preview_scroll.show()
            cols = 4  # 每行4个预览
            for i, file_path in enumerate(self.media_files):
                row = i // cols
                col = i % cols
                preview = MediaPreviewWidget(file_path, self)
                self.preview_layout.addWidget(preview, row, col)
        else:
            self.preview_scroll.hide()

class TagInputWidget(QWidget):
    """标签输入组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.model = None
        self.selected_tags = []
        self.available_tags = ["AI绘画", "文案写作", "代码生成", "数据分析", "创意设计", 
                              "学习辅助", "工作效率", "娱乐休闲", "生活助手", "专业技能"]
        self.completer = None
        self.completer_model = None
        self.init_ui()
        
    def set_model(self, model):
        """设置数据模型并加载标签"""
        self.model = model
        self.load_tags_from_database()
        
    def init_ui(self):
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 标签输入区域：输入框 + 添加按钮
        input_layout = QHBoxLayout()
        input_layout.setSpacing(6)
        
        # 输入框
        self.tag_input = QLineEdit()
        self.tag_input.setPlaceholderText("输入标签名称...")
        self.tag_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        """)
        
        # 设置自动补全
        self.setup_completer()
        self.tag_input.textChanged.connect(self.on_text_changed)
        self.tag_input.returnPressed.connect(self.add_tag)
        input_layout.addWidget(self.tag_input)
        
        # 添加按钮
        add_btn = QPushButton("添加")
        add_btn.setFixedHeight(36)  # 与输入框高度匹配
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 4px 12px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
            QPushButton:pressed {
                background-color: #1D4ED8;
            }
        """)
        add_btn.clicked.connect(self.add_tag)
        input_layout.addWidget(add_btn)
        
        layout.addLayout(input_layout)
        
        # 添加说明文本
        hint_label = QLabel("提示：输入标签后按Enter或点击添加按钮。可添加多个标签。")
        hint_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 11px;
                font-style: italic;
            }
        """)
        layout.addWidget(hint_label)
        
        # 已选标签显示区域
        self.tags_container = QScrollArea()
        self.tags_container.setWidgetResizable(True)
        self.tags_container.setFrameShape(QFrame.NoFrame)
        self.tags_container.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
                min-height: 40px;
            }
        """)
        
        self.tags_widget = QWidget()
        self.tags_widget.setStyleSheet("background-color: transparent;")
        self.tags_layout = QHBoxLayout(self.tags_widget)
        self.tags_layout.setContentsMargins(0, 0, 0, 0)
        self.tags_layout.setSpacing(6)
        self.tags_layout.setAlignment(Qt.AlignLeft | Qt.AlignTop)
        self.tags_layout.addStretch()
        
        self.tags_container.setWidget(self.tags_widget)
        layout.addWidget(self.tags_container)
    
    def setup_completer(self):
        """设置自动补全器"""
        self.completer_model = QStringListModel()
        self.completer_model.setStringList(self.available_tags)
        
        self.completer = QCompleter()
        self.completer.setModel(self.completer_model)
        self.completer.setFilterMode(Qt.MatchContains)  # 包含匹配模式，而不是前缀匹配
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)  # 不区分大小写
        self.completer.setCompletionMode(QCompleter.PopupCompletion)  # 弹出式补全
        self.tag_input.setCompleter(self.completer)
    
    def load_tags_from_database(self):
        """从数据库加载所有已有标签"""
        if not self.model:
            return
            
        try:
            # 使用模型获取所有唯一标签
            all_tags = self.model.get_all_tags()
            
            # 更新可用标签列表
            if all_tags:
                self.available_tags = sorted(list(set(self.available_tags) | set(all_tags)))
                self.update_completer()
        except Exception as e:
            print(f"从数据库加载标签时出错: {e}")
    
    def update_completer(self):
        """更新自动补全模型的数据"""
        self.completer_model.setStringList(self.available_tags)
    
    def on_text_changed(self, text):
        """当输入文本变化时，根据输入内容过滤标签"""
        if not text:
            # 如果输入为空，恢复完整标签列表
            self.completer_model.setStringList(self.available_tags)
            return
        
        # 根据输入内容过滤标签
        filtered_tags = [tag for tag in self.available_tags if text.lower() in tag.lower()]
        self.completer_model.setStringList(filtered_tags)
        
        # 显示下拉菜单（如果有匹配项）
        if filtered_tags and len(text) > 0:
            self.completer.complete()
        
    def add_tag(self):
        """添加标签"""
        tag_text = self.tag_input.text().strip()
        if tag_text and tag_text not in self.selected_tags:
            # 限制最多10个标签
            if len(self.selected_tags) >= 10:
                QMessageBox.warning(self, "提示", "最多可添加10个标签")
                return
                
            self.selected_tags.append(tag_text)
            self.create_tag_widget(tag_text)
            self.tag_input.clear()
            
            # 添加到可用标签列表以便自动补全
            if tag_text not in self.available_tags:
                self.available_tags.append(tag_text)
                self.update_completer()
                
    def create_tag_widget(self, tag_text):
        """创建标签显示组件"""
        tag_widget = QFrame()
        tag_widget.setStyleSheet("""
            QFrame {
                background-color: #EBF4FF;
                border: 1px solid #BFDBFE;
                border-radius: 12px;
                padding: 4px 8px;
            }
        """)
        
        layout = QHBoxLayout(tag_widget)
        layout.setContentsMargins(6, 2, 6, 2)
        layout.setSpacing(4)
        
        # 标签文本
        label = QLabel(tag_text)
        label.setStyleSheet("""
            QLabel {
                color: #1E40AF;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(label)
        
        # 删除按钮
        delete_btn = QPushButton("×")
        delete_btn.setFixedSize(14, 14)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #1E40AF;
                color: white;
                border: none;
                border-radius: 7px;
                font-size: 8px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1D4ED8;
            }
        """)
        delete_btn.clicked.connect(lambda: self.remove_tag(tag_text, tag_widget))
        layout.addWidget(delete_btn)
        
        # 插入到布局中（在stretch之前）
        self.tags_layout.insertWidget(self.tags_layout.count() - 1, tag_widget)
        
    def remove_tag(self, tag_text, tag_widget):
        """移除标签"""
        if tag_text in self.selected_tags:
            self.selected_tags.remove(tag_text)
        tag_widget.setParent(None)
        
    def get_selected_tags(self):
        """获取已选标签"""
        return self.selected_tags
        
    def set_selected_tags(self, tags):
        """设置已选标签（用于编辑现有提示词）"""
        # 先清除现有标签
        for i in reversed(range(self.tags_layout.count() - 1)):
            widget = self.tags_layout.itemAt(i).widget()
            if widget:
                widget.setParent(None)
        
        self.selected_tags = []
        
        # 添加新标签
        for tag in tags:
            if tag and tag not in self.selected_tags:
                self.selected_tags.append(tag)
                self.create_tag_widget(tag)

class AuxiliaryDialog(QDialog):
    """辅助选择对话框"""
    
    def __init__(self, parent=None, current_fields=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(450, 400)
        self.selected_options = []
        self.current_fields = current_fields or {}  # 当前已添加的字段
        self.init_ui()
        self.restore_selection_state()
        
    def init_ui(self):
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题栏
        title_layout = QHBoxLayout()
        title_label = QLabel("辅助选择")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        close_btn = QPushButton("×")
        close_btn.setFixedSize(24, 24)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #6B7280;
                border: none;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
            }
        """)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)
        
        layout.addLayout(title_layout)
        
        # 选择区域
        selection_group = QGroupBox("选择要添加的字段")
        selection_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        
        selection_layout = QVBoxLayout(selection_group)
        selection_layout.setSpacing(12)
        selection_layout.setContentsMargins(16, 20, 16, 16)
        
        # 负向提示词选择
        self.negative_checkbox = QCheckBox("负向提示词")
        self.negative_checkbox.setStyleSheet(self.get_checkbox_style())
        selection_layout.addWidget(self.negative_checkbox)
        
        # 参数设置选择
        self.parameters_checkbox = QCheckBox("参数设置")
        self.parameters_checkbox.setStyleSheet(self.get_checkbox_style())
        selection_layout.addWidget(self.parameters_checkbox)
        
        layout.addWidget(selection_group)
        
        # 自定义名称区域
        custom_group = QGroupBox("自定义字段")
        custom_group.setStyleSheet(selection_group.styleSheet())
        
        custom_layout = QVBoxLayout(custom_group)
        custom_layout.setSpacing(12)
        custom_layout.setContentsMargins(16, 20, 16, 16)
        
        # 自定义名称输入
        name_layout = QHBoxLayout()
        self.custom_name_input = QLineEdit()
        self.custom_name_input.setPlaceholderText("输入自定义字段名称...")
        self.custom_name_input.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
            }
        """)
        name_layout.addWidget(self.custom_name_input)
        
        add_custom_btn = QPushButton("添加字段")
        add_custom_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        add_custom_btn.clicked.connect(self.add_custom_field)
        name_layout.addWidget(add_custom_btn)
        
        custom_layout.addLayout(name_layout)
        
        # 已添加的自定义字段列表
        self.custom_fields_list = QListWidget()
        self.custom_fields_list.setMaximumHeight(100)
        self.custom_fields_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: #F9FAFB;
                font-size: 12px;
            }
            QListWidget::item {
                padding: 6px 8px;
                border-bottom: 1px solid #E5E7EB;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
        """)
        custom_layout.addWidget(self.custom_fields_list)
        
        layout.addWidget(custom_group)
        layout.addStretch()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.close)
        button_layout.addWidget(cancel_btn)
        
        confirm_btn = QPushButton("确认添加")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        confirm_btn.clicked.connect(self.confirm_selection)
        button_layout.addWidget(confirm_btn)
        
        layout.addLayout(button_layout)
        
    def get_checkbox_style(self):
        """获取复选框样式"""
        return """
            QCheckBox {
                color: #374151;
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox::indicator:unchecked {
                border: 2px solid #D1D5DB;
                border-radius: 3px;
                background-color: white;
            }
            QCheckBox::indicator:checked {
                border: 2px solid #3B82F6;
                border-radius: 3px;
                background-color: #3B82F6;
            }
        """
        
    def add_custom_field(self):
        """添加自定义字段"""
        field_name = self.custom_name_input.text().strip()
        if field_name and field_name not in [self.custom_fields_list.item(i).text() for i in range(self.custom_fields_list.count())]:
            self.custom_fields_list.addItem(field_name)
            self.custom_name_input.clear()
            
    def confirm_selection(self):
        """确认选择"""
        self.selected_options = []
        
        # 检查预定义选项
        if self.negative_checkbox.isChecked():
            self.selected_options.append({"type": "negative_prompt", "name": "负向提示词"})
            
        if self.parameters_checkbox.isChecked():
            self.selected_options.append({"type": "parameters", "name": "参数设置"})
            
        # 添加自定义字段
        for i in range(self.custom_fields_list.count()):
            field_name = self.custom_fields_list.item(i).text()
            self.selected_options.append({"type": "custom", "name": field_name})
            
        self.accept()
        
    def restore_selection_state(self):
        """恢复选择状态"""
        # 根据当前已添加的字段恢复复选框状态
        if "负向提示词" in self.current_fields:
            self.negative_checkbox.setChecked(True)
            
        if "参数设置" in self.current_fields:
            self.parameters_checkbox.setChecked(True)
            
        # 恢复自定义字段列表
        for field_name, field_info in self.current_fields.items():
            if field_info.get('type') == 'custom':
                self.custom_fields_list.addItem(field_name)
        
    def get_selected_options(self):
        """获取选中的选项"""
        return self.selected_options

class CreateCategoryDialog(FollowingDialog):
    """新建分类对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(400, 200)
        self.category_name = ""
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题
        title_label = QLabel("新建分类")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
                margin-bottom: 10px;
            }
        """)
        layout.addWidget(title_label)
        
        # 输入区域
        input_layout = QFormLayout()
        input_layout.setSpacing(12)
        
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("请输入分类名称...")
        self.name_input.setStyleSheet("""
            QLineEdit {
                padding: 10px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        """)
        self.name_input.returnPressed.connect(self.accept_category)
        
        input_layout.addRow("分类名称:", self.name_input)
        layout.addLayout(input_layout)
        
        layout.addStretch()
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        confirm_btn = QPushButton("确认")
        confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 13px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        confirm_btn.clicked.connect(self.accept_category)
        button_layout.addWidget(confirm_btn)
        
        layout.addLayout(button_layout)
        
    def accept_category(self):
        """确认创建分类"""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入分类名称！")
            return
            
        if len(name) > 20:
            QMessageBox.warning(self, "警告", "分类名称不能超过20个字符！")
            return
            
        self.category_name = name
        self.accept()
        
    def get_category_name(self):
        """获取分类名称"""
        return self.category_name
        
class CategoryManagerDialog(FollowingDialog):
    """分类管理对话框"""
    
    def __init__(self, parent=None, categories=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(500, 400)
        self.categories = categories or []
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        # 标题栏
        title_layout = QHBoxLayout()
        title_label = QLabel("分类管理")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        close_btn = QPushButton("×")
        close_btn.setFixedSize(24, 24)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #6B7280;
                border: none;
                border-radius: 12px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
            }
        """)
        close_btn.clicked.connect(self.reject)
        title_layout.addWidget(close_btn)
        
        layout.addLayout(title_layout)
        
        # 主要内容区域
        content_layout = QHBoxLayout()
        
        # 左侧分类列表
        left_layout = QVBoxLayout()
        
        list_label = QLabel("分类列表:")
        list_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 8px;
            }
        """)
        left_layout.addWidget(list_label)
        
        self.category_list = QListWidget()
        self.category_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: white;
                font-size: 13px;
                padding: 4px;
            }
            QListWidget::item {
                padding: 8px 12px;
                border: none;
                border-radius: 4px;
                margin: 1px;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
        """)
        
        # 添加分类到列表
        for category in self.categories:
            self.category_list.addItem(category)
            
        left_layout.addWidget(self.category_list)
        content_layout.addLayout(left_layout)
        
        # 右侧操作按钮
        right_layout = QVBoxLayout()
        right_layout.setAlignment(Qt.AlignTop)
        
        # 添加分类按钮
        add_btn = QPushButton("添加分类")
        add_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-size: 13px;
                margin-bottom: 8px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        add_btn.clicked.connect(self.add_category)
        right_layout.addWidget(add_btn)
        
        # 编辑分类按钮
        edit_btn = QPushButton("编辑分类")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #F59E0B;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-size: 13px;
                margin-bottom: 8px;
            }
            QPushButton:hover {
                background-color: #D97706;
            }
        """)
        edit_btn.clicked.connect(self.edit_category)
        right_layout.addWidget(edit_btn)
        
        # 删除分类按钮
        delete_btn = QPushButton("删除分类")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 10px 16px;
                font-size: 13px;
                margin-bottom: 8px;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_btn.clicked.connect(self.delete_category)
        right_layout.addWidget(delete_btn)
        
        right_layout.addStretch()
        content_layout.addLayout(right_layout)
        
        layout.addLayout(content_layout)
        
        # 底部按钮
        bottom_layout = QHBoxLayout()
        bottom_layout.addStretch()
        
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        bottom_layout.addWidget(cancel_btn)
        
        save_btn = QPushButton("保存")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #3B82F6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 20px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #2563EB;
            }
        """)
        save_btn.clicked.connect(self.save_categories)
        bottom_layout.addWidget(save_btn)
        
        layout.addLayout(bottom_layout)
        
    def add_category(self):
        """添加分类"""
        dialog = CreateCategoryDialog(self)
        if dialog.exec() == 1:  # QDialog.Accepted
            new_category = dialog.get_category_name()
            if new_category and new_category not in self.get_current_categories():
                self.category_list.addItem(new_category)
            elif new_category in self.get_current_categories():
                QMessageBox.warning(self, "警告", "该分类已存在！")
                
    def edit_category(self):
        """编辑分类"""
        current_item = self.category_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要编辑的分类！")
            return
            
        old_name = current_item.text()
        dialog = CreateCategoryDialog(self)
        dialog.name_input.setText(old_name)
        dialog.setWindowTitle("编辑分类")
        
        if dialog.exec() == 1:  # QDialog.Accepted
            new_name = dialog.get_category_name()
            if new_name and new_name != old_name:
                if new_name not in self.get_current_categories():
                    current_item.setText(new_name)
                else:
                    QMessageBox.warning(self, "警告", "该分类名称已存在！")
                    
    def delete_category(self):
        """删除分类"""
        current_item = self.category_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要删除的分类！")
            return
            
        category_name = current_item.text()
        if category_name == "通用":
            QMessageBox.warning(self, "警告", "默认分类'通用'不能删除！")
            return
            
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除分类 '{category_name}' 吗？",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            row = self.category_list.row(current_item)
            self.category_list.takeItem(row)
            
    def get_current_categories(self):
        """获取当前分类列表"""
        categories = []
        for i in range(self.category_list.count()):
            categories.append(self.category_list.item(i).text())
        return categories
        
    def save_categories(self):
        """保存分类"""
        self.categories = self.get_current_categories()
        self.accept()
        
    def get_categories(self):
        """获取分类列表"""
        return self.categories
        
class CreatePromptDialog(FollowingDialog):
    """新建提示词对话框"""
    
    def __init__(self, parent=None, edit_prompt=None):
        super().__init__(parent)
        self.model = None
        if hasattr(parent, 'model'):
            self.model = parent.model
        else:
            # 如果没有父窗口的model，尝试创建一个默认的
            try:
                from model import PromptModel
                self.model = PromptModel()
            except ImportError:
                print("警告: 无法创建PromptModel实例")
            
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(600, 700)
        self.auxiliary_data = {}
        self.dynamic_fields = {}  # 存储动态添加的字段
        self.edit_prompt = edit_prompt  # 保存要编辑的提示词对象
        self.init_ui()
        
        # 如果是编辑模式，加载提示词数据
        if self.edit_prompt:
            self.load_prompt_data()
            
        # 不需要调用center_dialog，因为FollowingDialog的showEvent会处理位置

    def init_ui(self):
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 自定义标题栏
        self.create_title_bar(main_layout)
        
        # 内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(24, 20, 24, 20)
        content_layout.setSpacing(20)
        
        # 创建表单
        self.create_form(content_layout)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidget(content_widget)
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: white;
            }
        """)
        
        main_layout.addWidget(scroll_area)
        
        # 底部按钮区域
        self.create_button_area(main_layout)
        
    def create_title_bar(self, layout):
        """创建自定义标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(20, 0, 15, 0)
        
        # 标题
        title_text = "编辑提示词" if self.edit_prompt else "新建提示词"
        title_label = QLabel(title_text)
        title_label.setObjectName("title_label")  # 添加对象名，便于后续查找
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #6B7280;
                border: none;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FEE2E2;
                color: #DC2626;
            }
        """)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)
        
        layout.addWidget(title_bar)
        
        # 用于拖拽
        self.drag_position = QPoint()
        title_bar.mousePressEvent = self.title_bar_mouse_press
        title_bar.mouseMoveEvent = self.title_bar_mouse_move
        
    def title_bar_mouse_press(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            
    def title_bar_mouse_move(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
            
    def create_form(self, layout):
        """创建表单区域"""
        # 基础信息区域
        basic_group = QGroupBox("基础信息")
        basic_group.setStyleSheet("""
            QGroupBox {
                font-size: 14px;
                font-weight: 600;
                color: #374151;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
        """)
        
        basic_layout = QFormLayout(basic_group)
        basic_layout.setSpacing(15)
        basic_layout.setContentsMargins(16, 20, 16, 16)
        
        # 标题（必填）
        self.title_input = QLineEdit()
        self.title_input.setPlaceholderText("请输入提示词标题（必填）")
        self.title_input.setStyleSheet(self.get_input_style())
        basic_layout.addRow("标题 *:", self.title_input)
        
        # 类型选择
        type_widget = QWidget()
        type_layout = QHBoxLayout(type_widget)
        type_layout.setContentsMargins(0, 0, 0, 0)
        type_layout.setSpacing(20)
        
        self.type_group = QButtonGroup()
        self.text_radio = QRadioButton("文本")
        self.image_radio = QRadioButton("图片")
        self.video_radio = QRadioButton("视频")
        
        self.text_radio.setChecked(True)  # 默认选择文本
        
        for radio in [self.text_radio, self.image_radio, self.video_radio]:
            radio.setStyleSheet("""
                QRadioButton {
                    color: #374151;
                    font-size: 13px;
                    spacing: 8px;
                }
                QRadioButton::indicator {
                    width: 16px;
                    height: 16px;
                }
                QRadioButton::indicator:unchecked {
                    border: 2px solid #D1D5DB;
                    border-radius: 8px;
                    background-color: white;
                }
                QRadioButton::indicator:checked {
                    border: 2px solid #3B82F6;
                    border-radius: 8px;
                    background-color: #3B82F6;
                }
            """)
            self.type_group.addButton(radio)
            type_layout.addWidget(radio)
            
        type_layout.addStretch()
        basic_layout.addRow("类型:", type_widget)
        
        # 分类选择
        category_widget = QWidget()
        category_layout = QHBoxLayout(category_widget)
        category_layout.setContentsMargins(0, 0, 0, 0)
        category_layout.setSpacing(8)
        
        self.category_combo = QComboBox()
        self.init_categories()
        self.category_combo.setStyleSheet(self.get_combo_style())
        category_layout.addWidget(self.category_combo)
        
        manage_cat_btn = QPushButton("管理")
        manage_cat_btn.setFixedSize(60, 32)
        manage_cat_btn.setStyleSheet(self.get_secondary_button_style())
        manage_cat_btn.clicked.connect(self.show_category_manager)
        category_layout.addWidget(manage_cat_btn)
        
        create_cat_btn = QPushButton("新建")
        create_cat_btn.setFixedSize(60, 32)
        create_cat_btn.setStyleSheet(self.get_secondary_button_style())
        create_cat_btn.clicked.connect(self.show_create_category_dialog)
        category_layout.addWidget(create_cat_btn)
        
        basic_layout.addRow("分类:", category_widget)
        
        # 标签输入
        self.tag_input_widget = TagInputWidget()
        if self.model:
            self.tag_input_widget.set_model(self.model)
        basic_layout.addRow("标签:", self.tag_input_widget)
        
        layout.addWidget(basic_group)
        
        # 媒体上传区域（动态显示）
        self.media_upload = MediaUploadArea()
        layout.addWidget(self.media_upload)
        self.media_upload.hide()  # 初始隐藏
        
        # 连接类型选择信号
        self.text_radio.toggled.connect(self.on_type_changed)
        self.image_radio.toggled.connect(self.on_type_changed)
        self.video_radio.toggled.connect(self.on_type_changed)
        
        # 内容区域
        content_group = QGroupBox("提示词内容")
        content_group.setStyleSheet(basic_group.styleSheet())
        
        self.content_layout_inner = QVBoxLayout(content_group)
        self.content_layout_inner.setContentsMargins(16, 20, 16, 16)
        self.content_layout_inner.setSpacing(12)
        
        # 提示词指令
        instruction_label = QLabel("提示词指令:")
        instruction_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 13px;
                font-weight: 500;
            }
        """)
        self.content_layout_inner.addWidget(instruction_label)
        
        self.instruction_text = QTextEdit()
        self.instruction_text.setMinimumHeight(120)
        self.instruction_text.setPlaceholderText("请输入详细的提示词指令...")
        self.instruction_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 12px;
                font-size: 13px;
                background-color: white;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        """)
        self.content_layout_inner.addWidget(self.instruction_text)
        
        # 动态字段容器
        self.dynamic_fields_container = QWidget()
        self.dynamic_fields_layout = QVBoxLayout(self.dynamic_fields_container)
        self.dynamic_fields_layout.setContentsMargins(0, 0, 0, 0)
        self.dynamic_fields_layout.setSpacing(12)
        self.content_layout_inner.addWidget(self.dynamic_fields_container)
        
        # 备注
        remark_label = QLabel("备注:")
        remark_label.setStyleSheet(instruction_label.styleSheet())
        self.content_layout_inner.addWidget(remark_label)
        
        self.remark_text = QTextEdit()
        self.remark_text.setMaximumHeight(80)
        self.remark_text.setPlaceholderText("添加备注信息（可选）...")
        self.remark_text.setStyleSheet(self.instruction_text.styleSheet())
        self.content_layout_inner.addWidget(self.remark_text)
        
        layout.addWidget(content_group)
        
    def get_input_style(self):
        """获取输入框样式"""
        return """
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        """
        
    def get_combo_style(self):
        """获取下拉框样式"""
        return """
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 13px;
                background-color: white;
                min-width: 150px;
                max-width: 200px;
            }
            QComboBox:focus {
                border-color: #3B82F6;
            }
            QComboBox:hover {
                border-color: #9CA3AF;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6B7280;
                margin-right: 5px;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                background-color: white;
                selection-background-color: #EBF4FF;
                selection-color: #1E40AF;
                padding: 4px;
                min-width: 150px;
            }
            QComboBox QAbstractItemView::item {
                padding: 8px 12px;
                border: none;
                min-height: 20px;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #F3F4F6;
                color: #374151;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
        """
        
    def get_secondary_button_style(self):
        """获取次要按钮样式"""
        return """
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """
        
    def on_type_changed(self):
        """类型选择改变事件"""
        if self.image_radio.isChecked() or self.video_radio.isChecked():
            self.media_upload.show()
        else:
            self.media_upload.hide()
            
    def create_button_area(self, layout):
        """创建底部按钮区域"""
        button_area = QWidget()
        button_area.setFixedHeight(70)
        button_area.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-top: 1px solid #E5E7EB;
            }
        """)
        
        button_layout = QHBoxLayout(button_area)
        button_layout.setContentsMargins(24, 15, 24, 15)
        button_layout.setSpacing(12)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.close)
        button_layout.addWidget(cancel_btn)
        
        button_layout.addStretch()
        
        # 智能解析按钮
        self.parse_btn = QPushButton("智能解析")
        self.parse_btn.setStyleSheet("""
            QPushButton {
                background-color: #F59E0B;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #D97706;
            }
        """)
        self.parse_btn.clicked.connect(self.smart_parse)
        button_layout.addWidget(self.parse_btn)
        
        # 辅助选择按钮
        auxiliary_btn = QPushButton("辅助选择")
        auxiliary_btn.setStyleSheet("""
            QPushButton {
                background-color: #8B5CF6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #7C3AED;
            }
        """)
        auxiliary_btn.clicked.connect(self.show_auxiliary_dialog)
        button_layout.addWidget(auxiliary_btn)
        
        # 保存按钮
        save_btn = QPushButton("保存")
        save_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
        """)
        save_btn.clicked.connect(self.save_prompt)
        button_layout.addWidget(save_btn)
        
        layout.addWidget(button_area)
        
    def smart_parse(self):
        """智能解析提示词指令，自动填充表单字段"""
        instruction_text = self.instruction_text.toPlainText().strip()
        if not instruction_text:
            QMessageBox.warning(self, "提示", "请先输入提示词指令内容")
            return
            
        try:
            # 解析标题（取第一行作为标题）
            lines = instruction_text.split('\n')
            title = lines[0].strip()
            if len(title) > 50:  # 如果第一行太长，截取前50个字符
                title = title[:50] + "..."
            
            # 设置标题
            self.title_input.setText(title)
            
            # 解析类型
            text_lower = instruction_text.lower()
            if any(keyword in text_lower for keyword in ['图片', 'image', 'img', 'photo', 'picture']):
                self.image_radio.setChecked(True)
            elif any(keyword in text_lower for keyword in ['视频', 'video', 'mp4', 'avi', 'mov']):
                self.video_radio.setChecked(True)
            else:
                self.text_radio.setChecked(True)
            
            # 解析分类（基于关键词）
            category_keywords = {
                '人物': ['人物', '人像', 'portrait', 'person', 'face', '人物'],
                '风景': ['风景', 'landscape', 'nature', 'scenery', '山水'],
                '动物': ['动物', 'animal', 'pet', 'cat', 'dog', 'bird'],
                '建筑': ['建筑', 'building', 'architecture', 'house', 'city'],
                '艺术': ['艺术', 'art', 'painting', 'drawing', 'style'],
                '科技': ['科技', 'technology', 'robot', 'future', 'cyber'],
                '美食': ['美食', 'food', 'dish', 'cooking', 'cuisine'],
                '其他': []
            }
            
            found_category = None
            for category, keywords in category_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    found_category = category
                    break
            
            if found_category:
                # 查找分类在combo box中的索引
                for i in range(self.category_combo.count()):
                    if self.category_combo.itemText(i) == found_category:
                        self.category_combo.setCurrentIndex(i)
                        break
            
            # 解析标签（提取关键词）
            tags = []
            # 提取常见的标签关键词
            tag_keywords = [
                '高质量', 'high quality', 'best quality', 'masterpiece',
                '详细', 'detailed', 'ultra detailed', '8k', '4k',
                '真实', 'realistic', 'photorealistic',
                '艺术', 'artistic', 'stylized',
                '可爱', 'cute', 'kawaii',
                '性感', 'sexy', 'attractive',
                '恐怖', 'horror', 'scary',
                '科幻', 'sci-fi', 'futuristic',
                '复古', 'vintage', 'retro',
                '现代', 'modern', 'contemporary'
            ]
            
            for keyword in tag_keywords:
                if keyword.lower() in text_lower:
                    tags.append(keyword)
            
            # 设置标签
            if tags:
                self.tag_input_widget.set_selected_tags(tags)
            
            # 解析负向提示词
            negative_prompt = ""
            if "负面" in instruction_text or "negative" in text_lower:
                # 查找负向提示词部分
                lines = instruction_text.split('\n')
                for i, line in enumerate(lines):
                    if "负面" in line or "negative" in line.lower():
                        # 提取后续行作为负向提示词
                        negative_lines = []
                        for j in range(i + 1, len(lines)):
                            if lines[j].strip():
                                negative_lines.append(lines[j].strip())
                            else:
                                break
                        negative_prompt = '\n'.join(negative_lines)
                        break
            
            # 如果找到负向提示词，添加到动态字段
            if negative_prompt and "negative_prompt" not in self.dynamic_fields:
                self.add_single_dynamic_field("negative_prompt", "negative_prompt")
                # 设置负向提示词内容
                if "negative_prompt" in self.dynamic_fields:
                    field_info = self.dynamic_fields["negative_prompt"]
                    if hasattr(field_info['input'], 'setPlainText'):
                        field_info['input'].setPlainText(negative_prompt)
            
            QMessageBox.information(self, "智能解析完成", 
                                  f"已自动解析并填充以下信息：\n"
                                  f"• 标题：{title}\n"
                                  f"• 类型：{self.get_selected_type()}\n"
                                  f"• 分类：{self.category_combo.currentText()}\n"
                                  f"• 标签：{', '.join(tags) if tags else '无'}\n"
                                  f"• 负向提示词：{'已添加' if negative_prompt else '未找到'}")
            
        except Exception as e:
            QMessageBox.warning(self, "解析错误", f"智能解析过程中出现错误：{str(e)}")
    
    def show_auxiliary_dialog(self):
        """显示辅助选择对话框"""
        dialog = AuxiliaryDialog(self, self.dynamic_fields)
        if dialog.exec() == QDialog.Accepted:
            selected_options = dialog.get_selected_options()
            self.update_dynamic_fields(selected_options)
            
    def update_dynamic_fields(self, selected_options):
        """更新动态字段（支持添加和移除）"""
        # 获取当前选中的字段名称
        selected_field_names = {option["name"] for option in selected_options}
        
        # 移除未选中的字段
        fields_to_remove = []
        for field_name in self.dynamic_fields:
            if field_name not in selected_field_names:
                fields_to_remove.append(field_name)
                
        for field_name in fields_to_remove:
            self.remove_dynamic_field(field_name)
            
        # 添加新选中的字段
        for option in selected_options:
            field_type = option["type"]
            field_name = option["name"]
            
            # 如果字段已存在，跳过
            if field_name in self.dynamic_fields:
                continue
                
            self.add_single_dynamic_field(field_type, field_name)
            
    def add_single_dynamic_field(self, field_type, field_name):
        """添加单个动态字段"""
        # 创建字段容器
        field_container = QWidget()
        field_layout = QVBoxLayout(field_container)
        field_layout.setContentsMargins(0, 0, 0, 0)
        field_layout.setSpacing(6)
        
        # 字段标签和删除按钮
        header_layout = QHBoxLayout()
        field_label = QLabel(f"{field_name}:")
        field_label.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 13px;
                font-weight: 500;
            }
        """)
        header_layout.addWidget(field_label)
        header_layout.addStretch()
        
        # 删除按钮
        delete_btn = QPushButton("×")
        delete_btn.setFixedSize(20, 20)
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #EF4444;
                color: white;
                border: none;
                border-radius: 10px;
                font-size: 12px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #DC2626;
            }
        """)
        delete_btn.clicked.connect(lambda checked, name=field_name: self.remove_dynamic_field(name))
        header_layout.addWidget(delete_btn)
        
        field_layout.addLayout(header_layout)
        
        # 根据字段类型创建输入控件
        if field_type == "negative_prompt":
            field_input = QTextEdit()
            field_input.setMaximumHeight(80)
            field_input.setPlaceholderText("输入负向提示词...")
        elif field_type == "parameters":
            field_input = QLineEdit()
            field_input.setPlaceholderText("例如: steps=20, cfg=7.5")
        else:  # custom
            field_input = QLineEdit()
            field_input.setPlaceholderText(f"输入{field_name}...")
            
        # 设置输入控件样式
        if isinstance(field_input, QTextEdit):
            field_input.setStyleSheet("""
                QTextEdit {
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 8px;
                    font-size: 13px;
                    background-color: white;
                }
                QTextEdit:focus {
                    border-color: #3B82F6;
                }
            """)
        else:
            field_input.setStyleSheet(self.get_input_style())
            
        field_layout.addWidget(field_input)
        
        # 将字段添加到布局
        self.dynamic_fields_layout.addWidget(field_container)
        
        # 存储字段引用
        self.dynamic_fields[field_name] = {
            'container': field_container,
            'label': field_label,
            'input': field_input
        }
            
    def remove_dynamic_field(self, field_name):
        """移除动态字段"""
        if field_name in self.dynamic_fields:
            field_info = self.dynamic_fields[field_name]
            field_info['container'].setParent(None)
            del self.dynamic_fields[field_name]
            
    def save_prompt(self):
        """保存提示词"""
        try:
            # 获取基本信息
            title = self.title_input.text().strip()
            if not title:
                QMessageBox.warning(self, "错误", "请填写标题")
                return
                
            # 获取分类
            category = self.category_combo.currentText().strip()
            
            # 获取标签
            tags = self.tag_input_widget.get_selected_tags()
                
            instruction = self.instruction_text.toPlainText().strip()
            remarks = self.remark_text.toPlainText().strip()
            media_files = self.media_upload.media_files if hasattr(self, 'media_upload') else []
            dynamic_fields_data = self.get_dynamic_fields_data()
            
            # 验证dynamic_fields_data不包含文件路径
            for field_name, field_value in dynamic_fields_data.items():
                if field_value and (field_value.startswith('file:///') or field_value.startswith('C:/') or field_value.startswith('C:\\')):
                    print(f"[ERROR] 检测到文件路径被错误赋值给辅助字段 '{field_name}': {field_value}")
                    QMessageBox.warning(self, "错误", f"辅助字段 '{field_name}' 包含无效的文件路径数据，已自动清理")
                    dynamic_fields_data[field_name] = ""
            
            # 获取选择的类型
            prompt_type = self.get_selected_type()
            
            from model import Prompt
            from datetime import datetime
            
            if self.edit_prompt:
                # 编辑现有提示词
                self.edit_prompt.title = title
                self.edit_prompt.content = instruction
                self.edit_prompt.category = category
                self.edit_prompt.tags = tags
                self.edit_prompt.media_files = media_files
                self.edit_prompt.auxiliary_fields = dynamic_fields_data  # 保存辅助选择内容
                self.edit_prompt.type = prompt_type  # 设置提示词类型
                self.edit_prompt.updated_at = datetime.now().isoformat()
                
                # 保存到数据库
                if self.model:
                    self.model.update_prompt(self.edit_prompt)
                    
                    # 显示成功信息
                    message = f"提示词更新成功！"
                    QMessageBox.information(self, "成功", message)
                    
                    self.accept()
                    
                    # 通知父窗口刷新提示词列表
                    if hasattr(self.parent(), 'refresh_prompt_list'):
                        self.parent().refresh_prompt_list()
                else:
                    QMessageBox.warning(self, "错误", "无法访问数据模型")
            else:
                # 创建新提示词对象
                prompt = Prompt(
                    title=title,
                    content=instruction,  # model.py 中使用 content 而不是 instruction
                    category=category,
                    tags=tags,
                    media_files=media_files,
                    auxiliary_fields=dynamic_fields_data,  # 保存辅助选择内容
                    type=prompt_type  # 设置提示词类型
                )
                
                # 保存到数据库
                if self.model:
                    prompt_id = self.model.add_prompt(prompt)
                    
                    # 显示成功信息，包括标签数量
                    message = f"提示词保存成功！\n添加了 {len(tags)} 个标签" if tags else "提示词保存成功！"
                    QMessageBox.information(self, "成功", message)
                    
                    self.accept()
                    
                    # 通知父窗口刷新提示词列表
                    if hasattr(self.parent(), 'refresh_prompt_list'):
                        self.parent().refresh_prompt_list()
                else:
                    QMessageBox.warning(self, "错误", "无法访问数据模型")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存失败：{str(e)}")
        
    def get_selected_type(self):
        """获取选中的类型"""
        if self.text_radio.isChecked():
            return "文本"
        elif self.image_radio.isChecked():
            return "图片"
        elif self.video_radio.isChecked():
            return "视频"
        return "文本"
        
    def get_dynamic_fields_data(self):
        """获取动态字段数据"""
        data = {}
        for field_name, field_info in self.dynamic_fields.items():
            field_input = field_info['input']
            if isinstance(field_input, QTextEdit):
                value = field_input.toPlainText().strip()
            else:
                value = field_input.text().strip()
            
            # 验证值不是文件路径
            if value and (value.startswith('file:///') or value.startswith('C:/') or value.startswith('C:\\')):
                print(f"[WARNING] 检测到可能的文件路径值: {value}")
                # 如果是文件路径，跳过这个字段
                continue
                
            data[field_name] = value
        return data
        
    def init_categories(self):
        """初始化分类列表"""
        # 默认分类列表
        self.categories = ["通用", "AI绘画", "文案写作", "代码生成", "数据分析", "创意设计", "学习辅助", "工作效率"]
        self.category_combo.clear()
        self.category_combo.addItems(self.categories)
        
    def show_create_category_dialog(self):
        """显示新建分类对话框"""
        dialog = CreateCategoryDialog(self)
        if dialog.exec() == 1:  # QDialog.Accepted
            new_category = dialog.get_category_name()
            if new_category and new_category not in self.categories:
                self.categories.append(new_category)
                self.category_combo.addItem(new_category)
                self.category_combo.setCurrentText(new_category)
                QMessageBox.information(self, "成功", f"分类 '{new_category}' 创建成功！")
                
    def show_category_manager(self):
        """显示分类管理对话框"""
        dialog = CategoryManagerDialog(self, self.categories.copy())
        if dialog.exec() == 1:  # QDialog.Accepted
            updated_categories = dialog.get_categories()
            current_selection = self.category_combo.currentText()
            
            self.categories = updated_categories
            self.category_combo.clear()
            self.category_combo.addItems(self.categories)
            
            # 尝试恢复之前的选择
            if current_selection in self.categories:
                self.category_combo.setCurrentText(current_selection)
            else:
                self.category_combo.setCurrentIndex(0)
                
    def load_prompt_data(self):
        """加载提示词数据到表单（用于编辑模式）"""
        if not self.edit_prompt:
            return
            
        print(f"[DEBUG] 开始加载提示词数据到编辑表单，提示词ID: {self.edit_prompt.id}")
            
        # 设置标题栏文本
        title_bar = self.findChild(QWidget)
        if title_bar:
            title_label = title_bar.findChild(QLabel)
            if title_label:
                title_label.setText("编辑提示词")
            
        # 填充表单数据
        self.title_input.setText(self.edit_prompt.title)
        self.instruction_text.setText(self.edit_prompt.content)
        
        # 备注目前没有字段，可以在这里添加
        if hasattr(self.edit_prompt, 'remarks'):
            self.remark_text.setText(self.edit_prompt.remarks)
            
        # 根据媒体文件自动确定提示词类型
        prompt_type = "文本"  # 默认为文本类型
        
        # 检查是否已有类型属性
        if hasattr(self.edit_prompt, 'type') and self.edit_prompt.type:
            prompt_type = self.edit_prompt.type
            print(f"[DEBUG] 从提示词对象获取类型: {prompt_type}")
        # 否则根据媒体文件推断类型
        elif hasattr(self.edit_prompt, 'media_files') and self.edit_prompt.media_files:
            # 获取第一个媒体文件作为判断依据
            first_media = self.edit_prompt.media_files[0]
            if first_media:
                file_path = Path(first_media)
                # 判断文件类型
                if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                    prompt_type = "图片"
                    print(f"[DEBUG] 根据媒体文件推断类型为图片: {file_path}")
                elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                    prompt_type = "视频"
                    print(f"[DEBUG] 根据媒体文件推断类型为视频: {file_path}")
                    
        # 设置类型单选按钮
        if prompt_type == "图片":
            self.image_radio.setChecked(True)
            print(f"[DEBUG] 设置提示词类型为图片")
        elif prompt_type == "视频":
            self.video_radio.setChecked(True)
            print(f"[DEBUG] 设置提示词类型为视频")
        else:
            self.text_radio.setChecked(True)
            print(f"[DEBUG] 设置提示词类型为文本")
            
        # 设置分类
        if self.edit_prompt.category:
            index = self.category_combo.findText(self.edit_prompt.category)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
            else:
                # 如果分类不存在，添加它
                self.category_combo.addItem(self.edit_prompt.category)
                self.category_combo.setCurrentText(self.edit_prompt.category)
                
        # 设置标签
        if self.edit_prompt.tags:
            self.tag_input_widget.set_selected_tags(self.edit_prompt.tags)
            
        # 设置媒体文件（如果有）
        if hasattr(self.edit_prompt, 'media_files') and self.edit_prompt.media_files:
            print(f"[DEBUG] 加载媒体文件: {len(self.edit_prompt.media_files)}个文件")
            for file_path in self.edit_prompt.media_files:
                if file_path:  # 确保文件路径不为空
                    self.media_upload.add_media_file(file_path)
                
        # 加载辅助选择字段（如果有）
        if hasattr(self.edit_prompt, 'auxiliary_fields') and self.edit_prompt.auxiliary_fields and len(self.edit_prompt.auxiliary_fields) > 0:
            for field_name, field_value in self.edit_prompt.auxiliary_fields.items():
                # 验证字段值不是文件路径
                if field_value and (field_value.startswith('file:///') or field_value.startswith('C:/') or field_value.startswith('C:\\')):
                    continue
                
                # 根据字段名称确定字段类型
                field_type = "custom"
                if field_name == "负向提示词":
                    field_type = "negative_prompt"
                elif field_name == "参数设置":
                    field_type = "parameters"
                    
                # 添加动态字段
                self.add_single_dynamic_field(field_type, field_name)
                
                # 设置字段值
                if field_name in self.dynamic_fields:
                    field_input = self.dynamic_fields[field_name]['input']
                    if isinstance(field_input, QTextEdit):
                        field_input.setPlainText(field_value)
                    else:
                        field_input.setText(field_value)
