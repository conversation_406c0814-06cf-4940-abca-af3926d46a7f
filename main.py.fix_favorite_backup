#!/usr/bin/env python3
"""
Prompt Assistant - 重构版桌面应用界面
采用460x800固定尺寸，无标题栏设计
"""
import sys
import os
import base64
import json
from datetime import datetime
from pathlib import Path
from typing import Optional, List, Dict, Any, Tuple, Union

# 添加视频缩略图处理功能 - 放在所有导入前，以避免循环导入问题
try:
    import cv2
    OPENCV_AVAILABLE = True
    print("OpenCV已成功加载，视频缩略图功能可用")
except ImportError:
    OPENCV_AVAILABLE = False
    print("未检测到OpenCV，视频缩略图功能不可用，请安装: pip install opencv-python")

def extract_video_thumbnail(video_path: str, max_size: Tuple[int, int] = (150, 150)) -> Optional['QPixmap']:
    """
    从视频文件提取第一帧作为缩略图
    
    参数:
        video_path: 视频文件路径
        max_size: 缩略图最大尺寸 (宽, 高)
        
    返回:
        QPixmap对象或None（如果失败）
    """
    if not OPENCV_AVAILABLE:
        print(f"无法提取视频缩略图: OpenCV不可用")
        return None
    
    try:
        print(f"尝试提取视频缩略图: {video_path}")
        # 打开视频文件
        cap = cv2.VideoCapture(str(video_path))
        
        # 检查是否成功打开
        if not cap.isOpened():
            print(f"无法打开视频文件: {video_path}")
            return None
        
        # 读取第一帧
        ret, frame = cap.read()
        if not ret:
            print(f"无法读取视频第一帧: {video_path}")
            return None
        
        # 释放视频对象
        cap.release()
        
        # 将BGR转换为RGB（OpenCV使用BGR格式）
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 计算缩放比例，保持宽高比
        height, width, _ = frame_rgb.shape
        scale_w = max_size[0] / width
        scale_h = max_size[1] / height
        scale = min(scale_w, scale_h)
        
        # 缩放图像
        if scale < 1:
            new_width = int(width * scale)
            new_height = int(height * scale)
            frame_rgb = cv2.resize(frame_rgb, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # 转换为QPixmap
        from PySide6.QtGui import QImage, QPixmap
        height, width, channel = frame_rgb.shape
        bytes_per_line = channel * width
        q_img = QImage(frame_rgb.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_img)
        
        print(f"成功提取视频缩略图: {video_path}, 尺寸: {pixmap.width()}x{pixmap.height()}")
        return pixmap
        
    except Exception as e:
        print(f"提取视频缩略图时出错: {e}")
        return None

from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QPushButton, QLabel, QFrame, 
                               QStackedWidget, QListWidget, QTextEdit, 
                               QLineEdit, QScrollArea, QMessageBox, QGraphicsDropShadowEffect,
                               QDialog, QSizePolicy, QLayout, QMenu,
                               QListWidgetItem, QComboBox, QFileDialog)
from PySide6.QtCore import Qt, QSize, QPoint, QRect, QSettings, QByteArray, QEvent, Signal, Property, QTimer
from PySide6.QtGui import QIcon, QPalette, QColor, QFont, QPixmap, QPainter, QPen, QImage, QTransform, QCloseEvent
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QSvgWidget
from model import PromptModel, Prompt
from create_prompt_dialog import CreatePromptDialog
from flow_layout import FlowLayout

# 重构基础设施导入
from components.button_factory import ButtonFactory
from components.style_manager import StyleManager
from components.card_builder import PromptCardBuilder
from components.navigation_bar import NavigationBar
from components.status_bar import StatusBar
from components.filter_buttons import FilterToggleButton, ToolboxButton, ViewToggleButton
from components.sort_button import SortButton
from pages.trash_page import TrashPage
from pages.settings_page import SettingsPage
from pages.help_page import HelpPage
from dialogs.share_prompt_dialog import SharePromptDialog
from dialogs.import_prompt_dialog import ImportPromptDialog
from dialogs.image_viewer_dialog import ImageViewerDialog, ZoomableImageView, open_video_with_system_player
from dialogs.export_prompt_dialog import ExportPromptDialog
from dialogs.import_file_dialog import ImportFileDialog
from prompt_history_dialog import PromptHistoryDialog


# 导入设计系统常量
from config.design_system import COLORS, SPACING, RADIUS, TYPOGRAPHY, BUTTON_STYLES

class CustomTitleBar(QWidget):
    """自定义标题栏组件 (400x50)"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(400, 50)
        self.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        self.init_ui()
        
        # 用于拖拽窗口
        self.drag_position = QPoint()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 0, 10, 0)
        layout.setSpacing(10)
        
        # 标题标签
        self.title_label = QLabel("Prompt收藏助手")
        self.title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        layout.addWidget(self.title_label)
        
        layout.addStretch()
        
        # 窗口控制按钮
        self.create_control_buttons(layout)
        
    def create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: #6B7280;
                font-size: 16px;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setStyleSheet(button_style)
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        close_style = button_style.replace("#E5E7EB", "#FEE2E2").replace("#D1D5DB", "#FECACA")
        close_style = close_style.replace("color: #6B7280;", "color: #DC2626;")
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet(close_style)
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def minimize_window(self):
        if self.parent_window:
            self.parent_window.showMinimized()
            
    def close_window(self):
        if self.parent_window:
            self.parent_window.close()
            
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)

class AddIconButton(QPushButton):
    """方形SVG新建按钮 - 三种状态"""
    
    def __init__(self, svg_content, tooltip, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.setFixedSize(36, 36)  # 方形尺寸
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(4)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(1)
        self.shadow_effect.setColor(QColor(59, 130, 246, 40))  # 蓝色阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        # 检查shadow_effect是否仍然有效
        if not hasattr(self, 'shadow_effect') or self.shadow_effect is None:
            return
        try:
            if state == "active":
                # 激活状态：更深的蓝色阴影
                self.shadow_effect.setBlurRadius(6)
                self.shadow_effect.setYOffset(2)
                self.shadow_effect.setColor(QColor(29, 78, 216, 80))
            elif state == "hover":
                # 悬停状态：增强的蓝色阴影
                self.shadow_effect.setBlurRadius(5)
                self.shadow_effect.setYOffset(1)
                self.shadow_effect.setColor(QColor(37, 99, 235, 60))
            else:
                # 默认状态：轻微蓝色阴影
                self.shadow_effect.setBlurRadius(4)
                self.shadow_effect.setYOffset(1)
                self.shadow_effect.setColor(QColor(59, 130, 246, 40))
        except RuntimeError:
            # Qt对象已被删除，忽略此操作
            pass
        
    def update_icon(self, state="default"):
        """更新图标状态"""
        # 三种状态的颜色和背景
        if state == "active":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#1D4ED8"  # 深蓝色背景
        elif state == "hover":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#2563EB"  # 中蓝色背景
        else:
            color = "#FFFFFF"  # 白色图标
            bg_color = "#3B82F6"  # 默认蓝色背景
            
        # 设置按钮样式
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: none;
                border-radius: 6px;
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(20, 20))
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.update_icon("hover")
        self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon("default")
        self.update_shadow("default")
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("active")
            self.update_shadow("active")
        super().mousePressEvent(event)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("hover")
            self.update_shadow("hover")
        super().mouseReleaseEvent(event)

class ThumbnailCarousel(QWidget):
    """缩略图轮播组件，用于在提示词卡片中显示可滑动的缩略图"""
    
    # 自定义信号，点击缩略图时发射，传递当前图片索引和媒体文件列表
    thumbnailClicked = Signal(int, list)
    
    def __init__(self, media_files=None, parent=None):
        super().__init__(parent)
        self.media_files = media_files if media_files else []
        self.current_index = 0
        self.drag_start_x = 0
        self.is_dragging = False
        self.is_hovered = False  # 添加悬停状态变量
        self.animation = None
        
        # 设置固定大小
        self.setFixedSize(80, 80)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        # 主布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        self.layout.setSpacing(0)
        
        # 缩略图容器
        self.thumbnail_container = QWidget()
        self.thumbnail_container.setFixedSize(80, 80)
        self.thumbnail_container.setStyleSheet("""
            QWidget {
                background-color: #F3F4F6;
                border: 1px solid #E5E7EB;
                border-radius: 4px;
            }
        """)
        
        # 缩略图标签
        self.thumbnail_label = QLabel(self.thumbnail_container)
        self.thumbnail_label.setFixedSize(78, 78)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: transparent;
                border: none;
            }
        """)
        # 不使用setScaledContents，而是在加载图片时手动按比例缩放
        
        # 左箭头按钮
        self.left_button = QPushButton("<", self.thumbnail_container)
        self.left_button.setFixedSize(20, 20)
        self.left_button.move(5, 30)
        self.left_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.7);
            }
        """)
        self.left_button.clicked.connect(self.show_previous)
        self.left_button.hide()  # 初始隐藏
        
        # 右箭头按钮
        self.right_button = QPushButton(">", self.thumbnail_container)
        self.right_button.setFixedSize(20, 20)
        self.right_button.move(55, 30)
        self.right_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 10px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: rgba(0, 0, 0, 0.7);
            }
        """)
        self.right_button.clicked.connect(self.show_next)
        self.right_button.hide()  # 初始隐藏
        
        # 图片索引指示器
        self.indicator_label = QLabel(self.thumbnail_container)
        self.indicator_label.setFixedSize(40, 16)
        self.indicator_label.move(20, 60)
        self.indicator_label.setAlignment(Qt.AlignCenter)
        self.indicator_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 0, 0, 0.5);
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 10px;
            }
        """)
        self.indicator_label.hide()  # 初始隐藏
        
        self.layout.addWidget(self.thumbnail_container)
        
        # 安装事件过滤器，用于处理鼠标悬停事件
        self.thumbnail_container.installEventFilter(self)
        
        # 如果有媒体文件，加载第一个
        if self.media_files:
            self.load_current_thumbnail()
            self.update_controls_visibility()
            
    def clear(self):
        """清空媒体文件列表"""
        self.media_files = []
        self.current_index = 0
        self.thumbnail_label.setText("📷")
        self.thumbnail_label.setStyleSheet("""
            font-size: 24px;
            background-color: transparent;
            border: none;
        """)
        self.update_controls_visibility()
        
    def add_item(self, media_path):
        """添加单个媒体文件到列表"""
        if media_path == "无媒体文件":
            # 特殊情况：显示提示文本
            self.clear()
            self.thumbnail_label.setText("无媒体文件")
            self.thumbnail_label.setStyleSheet("""
                font-size: 12px;
                background-color: transparent;
                border: none;
                color: #6B7280;
            """)
            return
            
        self.media_files.append(media_path)
        # 如果这是添加的第一个媒体文件，则加载它
        if len(self.media_files) == 1:
            self.current_index = 0
            self.load_current_thumbnail()
        self.update_controls_visibility()
    
    def set_media_files(self, media_files):
        """设置媒体文件列表"""
        self.media_files = media_files if media_files else []
        self.current_index = 0
        self.load_current_thumbnail()
        self.update_controls_visibility()
        
    def load_current_thumbnail(self):
        """加载当前索引的缩略图"""
        if not self.media_files or self.current_index >= len(self.media_files):
            # 没有媒体文件，显示默认图标
            self.thumbnail_label.setText("📷")
            self.thumbnail_label.setStyleSheet("""
                font-size: 24px;
                background-color: transparent;
                border: none;
            """)
            return
            
        media_path = self.media_files[self.current_index]
        file_path = Path(media_path)
        
        if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            # 图片文件
            pixmap = QPixmap(media_path)
            if not pixmap.isNull():
                # 获取原始图片尺寸
                orig_width = pixmap.width()
                orig_height = pixmap.height()
                
                # 计算缩放后的尺寸，保持原始比例
                container_size = 78  # 缩略图容器的大小
                
                if orig_width >= orig_height:
                    # 宽图
                    new_width = container_size
                    new_height = int(orig_height * container_size / orig_width)
                else:
                    # 高图
                    new_height = container_size
                    new_width = int(orig_width * container_size / orig_height)
                
                # 按比例缩放图片
                scaled_pixmap = pixmap.scaled(new_width, new_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                
                # 创建一个透明背景的图片，大小与容器相同
                result_pixmap = QPixmap(container_size, container_size)
                result_pixmap.fill(Qt.transparent)
                
                # 在透明背景上绘制缩放后的图片，使其居中
                painter = QPainter(result_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                painter.setRenderHint(QPainter.SmoothPixmapTransform)
                
                # 计算居中位置
                x = (container_size - new_width) // 2
                y = (container_size - new_height) // 2
                
                # 绘制图片
                painter.drawPixmap(x, y, scaled_pixmap)
                painter.end()
                
                # 设置缩略图
                self.thumbnail_label.setPixmap(result_pixmap)
                self.thumbnail_label.setStyleSheet("""
                    background-color: transparent;
                    border: none;
                """)
            else:
                # 加载失败，显示占位符
                self.thumbnail_label.setText("🖼️")
                self.thumbnail_label.setStyleSheet("""
                    font-size: 24px;
                    background-color: transparent;
                    border: none;
                """)
        elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件 - 尝试提取第一帧作为缩略图
            container_size = 78  # 缩略图容器的大小
            result_pixmap = None
            
            print(f"ThumbnailCarousel - 正在处理视频文件: {file_path}, OpenCV可用: {OPENCV_AVAILABLE}")
            if OPENCV_AVAILABLE:
                # 尝试使用OpenCV提取视频缩略图
                print(f"ThumbnailCarousel - 开始提取视频缩略图: {file_path}")
                thumb_pixmap = extract_video_thumbnail(file_path, (container_size, container_size))
                print(f"ThumbnailCarousel - 提取结果: {'成功' if thumb_pixmap and not thumb_pixmap.isNull() else '失败'}")
                
                if thumb_pixmap and not thumb_pixmap.isNull():
                    # 创建一个透明背景的图片，大小与容器相同
                    result_pixmap = QPixmap(container_size, container_size)
                    result_pixmap.fill(Qt.transparent)
                    
                    # 在透明背景上绘制缩略图和播放图标
                    painter = QPainter(result_pixmap)
                    painter.setRenderHint(QPainter.Antialiasing)
                    
                    # 先绘制视频缩略图
                    painter.drawPixmap(0, 0, thumb_pixmap)
                    
                    # 只在悬停时绘制播放按钮
                    if self.is_hovered:
                        # 绘制半透明遮罩
                        painter.setBrush(QColor(0, 0, 0, 100))  # 半透明黑色
                        painter.setPen(Qt.NoPen)
                        painter.drawRect(0, 0, container_size, container_size)
                        
                        # 绘制播放图标
                        painter.setPen(QColor(255, 255, 255))
                        painter.setBrush(QColor(255, 255, 255))
                        
                        # 绘制三角形播放图标
                        play_size = 30
                        center_x = container_size // 2
                        center_y = container_size // 2
                        
                        # 创建三角形的三个点
                        points = [
                            QPoint(center_x - play_size // 3, center_y - play_size // 2),
                            QPoint(center_x + play_size // 2, center_y),
                            QPoint(center_x - play_size // 3, center_y + play_size // 2)
                        ]
                        
                        # 绘制三角形
                        painter.drawPolygon(points)
                    
                    painter.end()
            
            # 如果无法提取缩略图，则使用默认的播放图标
            if result_pixmap is None:
                result_pixmap = QPixmap(container_size, container_size)
                result_pixmap.fill(Qt.transparent)
                
                # 在透明背景上绘制播放图标
                painter = QPainter(result_pixmap)
                painter.setRenderHint(QPainter.Antialiasing)
                
                # 绘制视频图标背景
                painter.setBrush(QColor(0, 0, 0, 128))
                painter.setPen(Qt.NoPen)
                painter.drawRect(0, 0, container_size, container_size)
                
                # 只在悬停时绘制播放图标
                if self.is_hovered:
                    # 绘制播放图标
                    painter.setPen(QColor(255, 255, 255))
                    painter.setBrush(QColor(255, 255, 255))
                    
                    # 绘制三角形播放图标
                    play_size = 30
                    center_x = container_size // 2
                    center_y = container_size // 2
                    
                    # 创建三角形的三个点
                    points = [
                        QPoint(center_x - play_size // 3, center_y - play_size // 2),
                        QPoint(center_x + play_size // 2, center_y),
                        QPoint(center_x - play_size // 3, center_y + play_size // 2)
                    ]
                    
                    # 绘制三角形
                    painter.drawPolygon(points)
                
                painter.end()
            
            # 设置缩略图
            self.thumbnail_label.setPixmap(result_pixmap)
            self.thumbnail_label.setStyleSheet("""
                background-color: transparent;
                border: none;
            """)
        else:
            # 其他文件
            self.thumbnail_label.setText("📄")
            self.thumbnail_label.setStyleSheet("""
                font-size: 24px;
                background-color: transparent;
                border: none;
            """)
            
        # 更新指示器文本
        self.indicator_label.setText(f"{self.current_index + 1}/{len(self.media_files)}")
        
    def show_next(self):
        """显示下一张缩略图"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index + 1) % len(self.media_files)
        self.load_current_thumbnail()
        
    def show_previous(self):
        """显示上一张缩略图"""
        if not self.media_files or len(self.media_files) <= 1:
            return
            
        self.current_index = (self.current_index - 1) % len(self.media_files)
        self.load_current_thumbnail()
        
    def update_controls_visibility(self):
        """更新控件可见性"""
        has_multiple_files = len(self.media_files) > 1
        self.left_button.setVisible(has_multiple_files)
        self.right_button.setVisible(has_multiple_files)
        self.indicator_label.setVisible(has_multiple_files)
        
    def update_thumbnail_display(self):
        """更新缩略图显示（重新绘制以反映悬停状态）"""
        if self.media_files:
            self.load_current_thumbnail()
        
    def eventFilter(self, obj, event):
        """事件过滤器，用于处理鼠标事件"""
        if obj == self.thumbnail_container:
            if event.type() == QEvent.MouseButtonPress and event.button() == Qt.LeftButton:
                # 记录拖动起始位置
                self.drag_start_x = event.position().x()
                self.is_dragging = True
                return True
                
            elif event.type() == QEvent.MouseMove and self.is_dragging:
                # 处理拖动事件
                if len(self.media_files) > 1:
                    delta = event.position().x() - self.drag_start_x
                    if abs(delta) > 30:  # 拖动距离超过阈值
                        if delta > 0:
                            self.show_previous()
                        else:
                            self.show_next()
                        self.is_dragging = False
                return True
                
            elif event.type() == QEvent.MouseButtonRelease:
                # 如果不是拖动，则视为点击
                if self.is_dragging and abs(event.position().x() - self.drag_start_x) < 10:
                    self.thumbnailClicked.emit(self.current_index, self.media_files)
                self.is_dragging = False
                return True
                
            elif event.type() == QEvent.Enter:
                # 鼠标进入时显示控件和播放按钮
                self.is_hovered = True
                self.update_controls_visibility()
                self.update_thumbnail_display()
                return True
                
            elif event.type() == QEvent.Leave:
                # 鼠标离开时隐藏控件和播放按钮
                self.is_hovered = False
                if len(self.media_files) > 1:
                    self.left_button.hide()
                    self.right_button.hide()
                    self.indicator_label.hide()
                self.update_thumbnail_display()
                return True
                
        return super().eventFilter(obj, event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            # 点击缩略图时发射信号
            self.thumbnailClicked.emit(self.current_index, self.media_files)
        super().mousePressEvent(event)

class PromptCardWidget(QFrame):
    """提示词卡片组件 - 自适应宽度，高度自适应"""
    
    def __init__(self, prompt, parent=None):
        super().__init__(parent)
        self.prompt = prompt
        
        # 通过父组件获取颜色服务
        self.color_service = self.get_color_service()
        
        # 根据提示词是否包含媒体文件设置不同的最小高度
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        if has_media:
            self.setMinimumHeight(160)  # 带媒体文件的提示词卡片高度更高
        else:
            self.setMinimumHeight(120)  # 纯文本提示词卡片保持原有高度
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)  # 水平扩展，垂直最小
        
        # 设置基础框架样式
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        
        # 初始化鼠标悬停状态
        self.hovered = False
        # 初始化选中状态
        self.is_selected = False
        
        # 创建右键菜单
        self.context_menu = QMenu(self)
        
        self.setup_ui()
        self.setup_context_menu()
        
        # 设置接受右键菜单事件
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def setup_context_menu(self):
        """设置右键菜单"""
        # 设置菜单样式
        self.context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
                margin: 2px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
                min-width: 120px;
            }
            QMenu::item:selected {
                background-color: #3B82F6;
                color: white;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 创建删除操作
        delete_action = self.context_menu.addAction("删除提示词")
        delete_action.triggered.connect(self.on_delete_action)
        
        # 创建编辑操作
        edit_action = self.context_menu.addAction("编辑提示词")
        edit_action.triggered.connect(self.on_edit_action)
        
        # 创建复制操作
        copy_action = self.context_menu.addAction("复制提示词")
        copy_action.triggered.connect(self.on_copy_action)
        
        # 创建收藏/取消收藏操作
        is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
        favorite_text = "取消收藏" if is_favorited else "收藏提示词"
        favorite_action = self.context_menu.addAction(favorite_text)
        favorite_action.triggered.connect(self.on_favorite_action)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        # 更新收藏菜单项文本
        for action in self.context_menu.actions():
            if action.text() in ["收藏提示词", "取消收藏"]:
                is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
                action.setText("取消收藏" if is_favorited else "收藏提示词")
                break
        
        # 显示菜单 - 使用exec代替exec_以解决弃用警告
        self.context_menu.exec(self.mapToGlobal(position))
    
    def on_delete_action(self):
        """处理删除操作（移至回收站）"""
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area:
            # 如果有选中的卡片，则删除所有选中的卡片
            if content_area.selected_cards:
                content_area.delete_selected_prompts()
            else:
                # 否则只删除当前卡片
                content_area.delete_prompt(self.prompt.id)
    
    def on_edit_action(self):
        """处理编辑操作"""
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
        
        if parent and hasattr(parent, 'content_area') and hasattr(parent.content_area, 'add_prompt'):
            # 显示编辑对话框 - 打开创建提示词对话框并传入当前提示词
            parent.status_bar.set_status("打开编辑提示词对话框...")
            
            # 导入对话框类
            from create_prompt_dialog import CreatePromptDialog
            
            # 创建编辑提示词对话框，传入当前提示词对象
            dialog = CreatePromptDialog(parent, edit_prompt=self.prompt)
            
            # 连接对话框的accepted和rejected信号
            dialog.accepted.connect(lambda: self.on_dialog_accepted(parent, dialog))
            dialog.rejected.connect(lambda: self.on_dialog_rejected(parent, dialog))
            
            # 保存对话框引用并显示（非模态）
            parent.content_area.active_dialogs.append(dialog)
            dialog.show()
            
    def on_dialog_accepted(self, parent, dialog):
        """对话框接受时的处理"""
        parent.status_bar.set_status("提示词更新成功")
        # 刷新提示词列表
        if hasattr(parent.content_area, 'refresh_prompt_list'):
            parent.content_area.refresh_prompt_list()
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_dialog_rejected(self, parent, dialog):
        """对话框拒绝时的处理"""
        parent.status_bar.set_status("取消编辑提示词")
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_history_action(self):
        """处理历史操作"""
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
                
        if parent and hasattr(parent, 'model'):
            # 显示历史对话框
            parent.status_bar.set_status("打开历史版本对话框...")
            
            # 导入历史对话框类
            from prompt_history_dialog import PromptHistoryDialog
            
            # 创建历史版本对话框
            try:
                dialog = PromptHistoryDialog(self.prompt.id, parent)
                
                # 连接对话框的accepted和rejected信号
                dialog.accepted.connect(lambda: self.on_history_dialog_accepted(parent, dialog))
                dialog.rejected.connect(lambda: self.on_history_dialog_rejected(parent, dialog))
                
                # 保存对话框引用并显示（非模态）
                parent.content_area.active_dialogs.append(dialog)
                dialog.show()
            except Exception as e:
                parent.status_bar.set_status(f"显示历史版本出错: {e}")
                
    def on_history_dialog_accepted(self, parent, dialog):
        """历史对话框接受时的处理"""
        parent.status_bar.set_status("历史版本操作已完成")
        # 刷新提示词列表
        if hasattr(parent, 'content_area') and hasattr(parent.content_area, 'refresh_prompt_list'):
            parent.content_area.refresh_prompt_list()
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_history_dialog_rejected(self, parent, dialog):
        """历史对话框拒绝时的处理"""
        parent.status_bar.set_status("取消历史版本操作")
        
        # 从活动对话框列表中移除
        if dialog in parent.content_area.active_dialogs:
            parent.content_area.active_dialogs.remove(dialog)
    
    def on_copy_action(self):
        """处理复制操作"""
        clipboard = QApplication.clipboard()
        clipboard.setText(self.prompt.content)
        
        # 取消选中状态
        self.clear_selection()
        
        # 显示状态提示
        parent = self
        while parent and not hasattr(parent, 'status_bar'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
        
        if parent and hasattr(parent, 'status_bar'):
            parent.status_bar.set_status("提示词内容已复制到剪贴板")
    
    def on_favorite_action(self):
        """处理收藏操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
                
        if parent and parent.model:
            # 调用模型的toggle_favorite方法
            if parent.model.toggle_favorite(self.prompt.id):
                # 更新本地提示词的收藏状态
                self.prompt.is_favorite = 1 if self.prompt.is_favorite == 0 else 0
                
                # 更新收藏按钮状态
                if 'favorite' in self.action_buttons:
                    # 设置按钮状态
                    self.action_buttons['favorite'].setProperty('is_active', self.prompt.is_favorite == 1)
                    # 更新按钮样式
                    self.action_buttons['favorite'].style().polish(self.action_buttons['favorite'])
                    
                # 更新状态栏
                if hasattr(parent, 'status_bar'):
                    status = "已收藏提示词" if self.prompt.is_favorite == 1 else "已取消收藏提示词"
                    parent.status_bar.set_status(status)
                    
                # 刷新提示词列表，以更新收藏状态显示
                if hasattr(parent.content_area, 'refresh_prompt_list'):
                    parent.content_area.refresh_prompt_list()
    
    def on_pin_action(self):
        """处理置顶操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级窗口
        parent = self
        while parent and not hasattr(parent, 'model'):
            if hasattr(parent, 'parent_window'):
                parent = parent.parent_window
            else:
                parent = parent.parent()
                
        if parent and parent.model:
            # 调用模型的toggle_pin方法
            if parent.model.toggle_pin(self.prompt.id):
                # 更新本地提示词的置顶状态
                self.prompt.is_pinned = 1 if self.prompt.is_pinned == 0 else 0
                
                # 更新置顶按钮状态
                if 'pin' in self.action_buttons:
                    # 设置按钮状态
                    self.action_buttons['pin'].setProperty('is_active', self.prompt.is_pinned == 1)
                    # 更新按钮样式
                    self.action_buttons['pin'].style().polish(self.action_buttons['pin'])
                    
                # 更新状态栏
                if hasattr(parent, 'status_bar'):
                    status = "已置顶提示词" if self.prompt.is_pinned == 1 else "已取消置顶提示词"
                    parent.status_bar.set_status(status)
                    
                # 刷新提示词列表，以重新排序
                if hasattr(parent.content_area, 'refresh_prompt_list'):
                    parent.content_area.refresh_prompt_list()
                
    def get_content_area(self):
        """获取父级ContentArea对象"""
        parent = self
        while parent and not isinstance(parent, ContentArea):
            parent = parent.parent()
        return parent
    
    def get_color_service(self):
        """获取颜色服务实例"""
        content_area = self.get_content_area()
        if content_area and hasattr(content_area, 'color_service'):
            return content_area.color_service
        # 如果找不到，创建一个默认的颜色服务
        from services.color_service import ColorService
        return ColorService()
    
    def setup_ui(self):
        """设置UI布局 - 使用PromptCardBuilder重构"""
        # 使用PromptCardBuilder创建卡片布局
        builder = PromptCardBuilder().create_card(self.prompt, self.color_service)
        
        # 构建完整的卡片
        card_frame = builder.add_header().add_title().add_content().add_media_carousel().add_tags().add_actions().build()
        
        # 将构建的组件添加到当前frame中
        # 复制builder创建的布局到当前widget
        if card_frame.layout():
            # 获取builder创建的布局和所有子widget
            builder_layout = card_frame.layout()
            
            # 为当前widget设置相同的布局
            main_layout = QVBoxLayout(self)
            main_layout.setContentsMargins(12, 10, 12, 10)
            main_layout.setSpacing(8)
            
            # 将builder创建的所有widget添加到当前布局中
            while builder_layout.count():
                child = builder_layout.takeAt(0)
                if child.widget():
                    main_layout.addWidget(child.widget())
            
            # 设置样式
        self.setStyleSheet("""
            QFrame {
                background-color: white;
            }
        """)
        
        # 不复制事件处理器，使用自己的方法
        if hasattr(card_frame, 'action_buttons'):
            self.action_buttons = card_frame.action_buttons
    
    def paintEvent(self, event):
        """重写绘制事件，手动绘制边框"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        
        # 检查置顶状态和媒体状态
        is_pinned = hasattr(self.prompt, 'is_pinned') and self.prompt.is_pinned == 1
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        # 设置画笔
        pen = QPen()
        if self.is_selected:
            # 选中状态：蓝色边框
            pen.setColor(QColor("#3B82F6"))
            pen.setWidth(2)
        elif is_pinned:
            # 置顶状态：浅蓝色边框
            pen.setColor(QColor("#93C5FD"))
            pen.setWidth(2)
        elif has_media:
            # 带媒体文件状态：紫色边框
            pen.setColor(QColor("#C4B5FD"))
            pen.setWidth(2)
        elif self.hovered:
            pen.setColor(QColor("#9ca3af"))  # 悬停时深灰色
            pen.setWidth(2)
        else:
            pen.setColor(QColor("#e0e0e0"))  # 默认浅灰色
            pen.setWidth(1)
        
        painter.setPen(pen)
        
        # 设置背景填充
        if is_pinned and not self.is_selected:
            # 置顶状态：淡蓝色背景
            painter.setBrush(QColor("#F0F7FF"))
        elif has_media and not self.is_selected and not is_pinned:
            # 带媒体文件状态：淡紫色背景
            painter.setBrush(QColor("#F5F3FF"))
        else:
            painter.setBrush(Qt.NoBrush)  # 不填充
        
        # 绘制圆角矩形
        rect = self.rect().adjusted(1, 1, -1, -1)  # 调整绘制区域，避免边缘被裁剪
        painter.drawRoundedRect(rect, 6, 6)
        
    def create_top_section(self):
        """创建顶部区域"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 左侧分类信息 - 应用颜色服务
        category_name = getattr(self.prompt, 'category', '通用')
        category_color = self.color_service.get_category_color(category_name)
        
        category_label = QLabel(category_name)
        category_label.setStyleSheet(f"""
            QLabel {{
                background-color: {category_color};
                color: #1F2937;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }}
        """)
        layout.addWidget(category_label)
        
        layout.addStretch()
        
        # 右侧操作按钮组
        buttons_widget = self.create_action_buttons()
        layout.addWidget(buttons_widget)
        
        return widget
        
    def _get_action_button_icons(self) -> Dict[str, str]:
        """获取操作按钮的SVG图标定义"""
        return {
            'favorite': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'favorite_active': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin_active': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="#3B82F6" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="#2563EB" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'copy': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" stroke-width="2"/>
            </svg>''',
            'edit': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'delete': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'history': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V12L16 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            </svg>'''
        }

    def create_action_buttons(self):
        """创建操作按钮组"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 获取按钮图标
        button_icons = self._get_action_button_icons()
        
        button_tooltips = {
            'favorite': '收藏',
            'favorite_active': '取消收藏',
            'pin': '置顶', 
            'pin_active': '取消置顶',
            'copy': '复制',
            'edit': '编辑',
            'history': '历史',
            'delete': '删除'
        }
        
        # 存储按钮引用
        self.action_buttons = {}
        
        for key, svg in button_icons.items():
            # 跳过添加active状态的图标，它们只是用于显示激活状态
            if key == 'favorite_active' or key == 'pin_active':
                continue
                
            # 为带状态的按钮创建特殊按钮
            if key == 'favorite':
                # 检查提示词是否已收藏
                is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
                
                # 创建按钮并传递是否已收藏信息
                btn = ButtonFactory.create_favorite_button(
                    button_icons['favorite'], 
                    button_icons['favorite_active'],
                    button_tooltips[key],
                    is_favorited
                )
            elif key == 'pin':
                # 检查提示词是否已置顶
                is_pinned = hasattr(self.prompt, 'is_pinned') and self.prompt.is_pinned == 1
                
                # 创建按钮并传递是否已置顶信息
                btn = ButtonFactory.create_pin_button(
                    button_icons['pin'], 
                    button_icons['pin_active'],
                    button_tooltips[key],
                    is_pinned
                )
            else:
                # 其他按钮正常创建
                btn = ButtonFactory.create_small_icon_button(svg, button_tooltips[key])
                
                # 为按钮添加点击事件
                if key == 'delete':
                    btn.clicked.connect(self.on_delete_action)
                elif key == 'copy':
                    btn.clicked.connect(self.on_copy_action)
                elif key == 'edit':
                    btn.clicked.connect(self.on_edit_action)
                elif key == 'history':
                    btn.clicked.connect(self.on_history_action)
            
            # 存储按钮引用
            self.action_buttons[key] = btn
            layout.addWidget(btn)
            
        return widget
        


        
    def set_button_icon(self, button: QPushButton, svg_content: str, color: str) -> None:
        """设置按钮图标"""
        if color:
            svg_data = svg_content.replace("currentColor", color)
        else:
            # 如果没有指定颜色，直接使用SVG内容（已经包含颜色信息）
            svg_data = svg_content
            
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(12, 12)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(12, 12))
        
    def create_title_section(self):
        """创建标题区域"""
        title_label = QLabel(self.prompt.title)
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)  # 最多显示2行标题
        return title_label
        
    def create_content_section(self):
        """创建内容区域"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        
        # 检查是否有媒体文件
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        if has_media:
            # 状态B：带媒体布局
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(8)
            
            # 左侧缩略图轮播
            self.thumbnail_carousel = ThumbnailCarousel(self.prompt.media_files, self)
            self.thumbnail_carousel.thumbnailClicked.connect(self.on_thumbnail_clicked)
            layout.addWidget(self.thumbnail_carousel)
            
            # 右侧内容预览
            content_layout = QVBoxLayout()
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(4)
            
            # 添加"内容预览"标签
            preview_label = QLabel("内容预览")
            preview_label.setStyleSheet("""
                QLabel {
                    color: #4B5563;
                    font-size: 11px;
                    font-weight: bold;
                    background: transparent;
                    border: none;
                }
            """)
            content_layout.addWidget(preview_label)
            
            # 添加内容预览
            content_label = self.create_content_preview()
            content_layout.addWidget(content_label)
            
            layout.addLayout(content_layout, 1)  # 1表示拉伸因子，使内容区域占据更多空间
            
        else:
            # 状态A：纯文本布局
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        return widget
        
    def on_thumbnail_clicked(self, index, media_files):
        """处理缩略图点击事件"""
        if not media_files:
            return
            
        media_path = media_files[index]
        file_path = Path(media_path)
        
        print(f"缩略图被点击: {file_path}")
        
        if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件，使用系统默认播放器打开
            print(f"尝试使用系统播放器打开视频: {file_path}")
            success = open_video_with_system_player(file_path)
            if not success:
                print(f"打开视频失败: {file_path}")
                QMessageBox.warning(self.window(), "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
            else:
                print(f"成功打开视频: {file_path}")
        else:
            # 图片文件，打开图片查看对话框
            print(f"打开图片查看对话框: {file_path}")
            dialog = ImageViewerDialog(media_files, index, self.window())
            dialog.exec()
        
    def create_content_preview(self):
        """创建内容预览标签"""
        # 检查是否有媒体文件
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        # 截取内容前100个字符作为预览
        preview_text = self.prompt.content[:100] + "..." if len(self.prompt.content) > 100 else self.prompt.content
        
        content_label = QLabel(preview_text)
        
        if has_media:
            # 带媒体文件的提示词，预览文本可以更紧凑
            content_label.setStyleSheet("""
                QLabel {
                    color: #6B7280;
                    font-size: 11px;
                    line-height: 1.3;
                    background: transparent;
                    border: none;
                }
            """)
            content_label.setWordWrap(True)
            content_label.setMaximumHeight(55)  # 较小的高度
        else:
            # 纯文本提示词，预览文本可以更宽松
            content_label.setStyleSheet("""
                QLabel {
                    color: #6B7280;
                    font-size: 12px;
                    line-height: 1.4;
                    background: transparent;
                    border: none;
                }
            """)
            content_label.setWordWrap(True)
            content_label.setMaximumHeight(60)  # 标准高度
        
        content_label.setAlignment(Qt.AlignTop)
        
        return content_label
        
    def create_tags_section(self):
        """创建标签区域"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        
        # 使用FlowLayout代替HBoxLayout以实现标签自动换行
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 获取标签 - 确保标签正确显示
        tags = []
        
        if hasattr(self.prompt, 'tags'):
            if self.prompt.tags is not None:
                # 如果是字符串，按逗号分割
                if isinstance(self.prompt.tags, str):
                    tags = [tag.strip() for tag in self.prompt.tags.split(',') if tag.strip()]
                else:
                    # 确保是列表类型
                    tags = list(self.prompt.tags)
                    
        # 仅当tags完全不存在或为None时才使用默认标签
        if not tags and not hasattr(self.prompt, 'tags'):
            tags = ['AI助手', '提示词', '创意']
        
        # 最多显示5个标签
        tag_count = 0
        for tag in tags:
            if tag_count >= 5:
                break
                
            tag_label = self.create_tag_label(tag)
            layout.addWidget(tag_label)
            tag_count += 1
            
        # 如果有更多标签，显示+N标签
        if len(tags) > 5:
            more_label = self.create_more_tags_label(len(tags) - 5)
            layout.addWidget(more_label)
            
        layout.addStretch()
        return widget
    

        
    def create_tag_label(self, tag_text: str) -> QWidget:
        """创建单个标签"""
        label = QLabel(tag_text)
        
        # 获取标签颜色（字体色）
        tag_color = self.color_service.get_tag_color(tag_text)
        
        label.setStyleSheet(f"""
            QLabel {{
                background-color: #F3F4F6;
                color: {tag_color};
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }}
        """)
        return label
        
    def create_more_tags_label(self, count: int) -> QWidget:
        """创建显示更多标签的标签"""
        label = QLabel(f"+{count}")
        label.setStyleSheet("""
            QLabel {
                background-color: #F3F4F6;
                color: #6B7280;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        """)
        return label
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.hovered = True
        self.update()  # 触发重绘
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.hovered = False
        self.update()  # 触发重绘
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标点击事件，支持正确的选中状态管理"""
        if event.button() == Qt.LeftButton:
            # 获取当前键盘修饰符
            modifiers = QApplication.keyboardModifiers()
            
            # 检查是否按住了Ctrl键
            if modifiers & Qt.ControlModifier:
                # Ctrl+点击：切换当前卡片的选中状态（多选模式）
                self.is_selected = not self.is_selected
                self.update()  # 触发重绘
                
                # 通知父容器更新选中状态
                parent = self.parent()
                while parent and not hasattr(parent, 'on_card_selection_changed'):
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break
                        
                if parent and hasattr(parent, 'on_card_selection_changed'):
                    parent.on_card_selection_changed(self, self.is_selected)
            else:
                # 普通点击：正确的单选逻辑
                parent = self.parent()
                while parent and not hasattr(parent, 'on_card_selection_changed'):
                    if hasattr(parent, 'parent'):
                        parent = parent.parent()
                    else:
                        break
                
                if parent and hasattr(parent, 'on_card_selection_changed'):
                    # 检查是否在全选状态下
                    is_all_selected = hasattr(parent, 'selected_cards') and len(parent.selected_cards) > 1
                    
                    if self.is_selected and not is_all_selected:
                        # 如果当前卡片已选中且不是全选状态，则取消选中
                        self.is_selected = False
                        self.update()  # 触发重绘
                        parent.on_card_selection_changed(self, False)
                    else:
                        # 如果当前卡片未选中，或者是全选状态下的点击，则选中当前卡片并取消其他所有卡片
                        # 普通点击始终是单选逻辑：清除所有其他卡片，选中当前卡片
                        if hasattr(parent, 'selected_cards'):
                            # 清除所有其他卡片的选中状态
                            for card in parent.selected_cards[:]:  # 使用切片创建副本
                                if card != self:
                                    card.is_selected = False
                                    card.update()
                            parent.selected_cards.clear()
                            parent.selected_cards.append(self)
                            parent.update_selection_status()
                        
                        # 选中当前卡片
                        self.is_selected = True
                        self.update()  # 触发重绘
                        parent.on_card_selection_changed(self, True)
        else:
            super().mousePressEvent(event)
    
    def set_selected(self, selected: bool) -> None:
        """设置选中状态"""
        if self.is_selected != selected:
            self.is_selected = selected
            self.update()  # 触发重绘
    
    def clear_selection(self):
        """取消当前卡片的选中状态"""
        if self.is_selected:
            self.is_selected = False
            self.update()  # 触发重绘
            
            # 通知父容器更新选中状态
            parent = self.parent()
            while parent and not hasattr(parent, 'on_card_selection_changed'):
                if hasattr(parent, 'parent'):
                    parent = parent.parent()
                else:
                    break
                    
            if parent and hasattr(parent, 'on_card_selection_changed'):
                parent.on_card_selection_changed(self, False)
            
            # 如果是回收站卡片，还需要从回收站选中列表中移除
            content_area = self.get_content_area()
            if content_area and hasattr(content_area, 'trash_selected_cards'):
                if self in content_area.trash_selected_cards:
                    content_area.trash_selected_cards.remove(self)



class SvgIconButton(QPushButton):
    """自定义SVG图标按钮"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
        # 设置按钮样式
        self.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """)
        
    def update_icon(self):
        """更新图标状态"""
        # 根据状态选择颜色 - 避免白色系，确保在深色背景下清晰可见
        if self.is_active:
            color = "#3B82F6"  # 激活状态：蓝色
        else:
            color = "#6B7280"  # 默认状态：中灰色
            
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(24, 24))
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(8)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        # 检查shadow_effect是否仍然有效
        if not hasattr(self, 'shadow_effect') or self.shadow_effect is None:
            return
        try:
            if state == "active":
                # 激活状态：蓝色阴影，更明显
                self.shadow_effect.setBlurRadius(12)
                self.shadow_effect.setYOffset(3)
                self.shadow_effect.setColor(QColor(59, 130, 246, 100))  # 蓝色阴影
            elif state == "hover":
                # 悬停状态：稍微增强的阴影
                self.shadow_effect.setBlurRadius(10)
                self.shadow_effect.setYOffset(2)
                self.shadow_effect.setColor(QColor(0, 0, 0, 80))  # 深一点的阴影
            else:
                # 默认状态：轻微阴影
                self.shadow_effect.setBlurRadius(8)
                self.shadow_effect.setYOffset(2)
                self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        except RuntimeError:
            # Qt对象已被删除，忽略此操作
            pass
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_icon()
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态：亮灰色，避免白色系
            svg_data = self.svg_content.replace("currentColor", "#9CA3AF")
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            self.setIcon(QIcon(pixmap))
            self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon()
        if self.is_active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        super().leaveEvent(event)





class ContentArea(QWidget):
    """内容显示区域 (400x718)"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.all_prompts = []  # 所有提示词
        self.active_filters = {
            'favorite': False,  # 收藏筛选
            'category': '全部',  # 分类筛选
            'tags': set(),  # 标签筛选（多选）
            'search': ''  # 搜索关键词筛选
        }
        
        # 通过父窗口获取颜色服务
        self.color_service = parent.color_service if parent else None
        
        # 从设置中读取排序和视图设置
        settings = QSettings("PromptAssistant", "AppSettings")
        self.current_sort = settings.value("sort_method", "updated_desc")  # 默认排序方式：更新时间降序
        self.current_view = settings.value("view_type", "card")  # 默认视图类型：卡片视图
        self.selected_cards = []  # 当前选中的卡片
        self.category_buttons = {}  # 分类按钮字典
        self.tag_buttons = {}  # 标签按钮字典
        self.filter_buttons = {}  # 筛选按钮字典
        self.active_dialogs = []  # 活动对话框列表
        
        # 设置焦点策略，确保可以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        self.init_ui()
    
    def load_svg_icon(self, svg_file_path, size=24):
        """加载SVG图标"""
        if self.parent_window and hasattr(self.parent_window, 'load_svg_icon'):
            return self.parent_window.load_svg_icon(svg_file_path, size)
        else:
            # 如果没有父窗口或父窗口没有load_svg_icon方法，使用默认实现
            try:
                with open(svg_file_path, 'r', encoding='utf-8') as f:
                    svg_content = f.read()
                return svg_content
            except FileNotFoundError:
                print(f"SVG文件未找到: {svg_file_path}")
                return ""
        
    def get_active_filter_button_style(self):
        """获取激活状态的筛选按钮样式"""
        return """
            QPushButton {
                background-color: #EBF4FF;
                color: #1E40AF;
                border: 1px solid #BFDBFE;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
                min-width: 0px;
            }
            QPushButton:hover {
                background-color: #DBEAFE;
                border-color: #93C5FD;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """
        
    def get_filter_button_style(self):
        """获取默认状态的筛选按钮样式"""
        return """
            QPushButton {
                background-color: white;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
                min-width: 0px;
            }
            QPushButton:hover {
                background-color: #F3F4F6;
                border-color: #9CA3AF;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """
    def init_ui(self):
        self.setStyleSheet("""
            QWidget {
                background-color: white;
            }
        """)
        
        # 使用堆叠布局来切换不同页面
        self.stacked_widget = QStackedWidget()
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stacked_widget)
        
        # 创建各个页面
        self.create_pages()
        
    def create_pages(self):
        """创建各个页面"""
        # 首页
        self.home_page = self.create_home_page()
        self.stacked_widget.addWidget(self.home_page)
        
        # 回收站页面 - 使用新的TrashPage组件
        self.trash_page = TrashPage(self)
        # BasePage.__init__已经调用了setup_ui()，不需要重复调用
        # 连接信号到ContentArea的方法
        self.trash_page.restore_requested.connect(self.restore_prompt)
        self.trash_page.permanent_delete_requested.connect(self.permanently_delete_prompt)
        # 连接批量操作信号
        self.trash_page.batch_restore_requested.connect(self.batch_restore_prompts)
        self.trash_page.batch_delete_requested.connect(self.batch_delete_prompts)
        self.trash_page.empty_trash_requested.connect(self.empty_trash)
        self.stacked_widget.addWidget(self.trash_page)
        
        # 设置页面 - 使用新的SettingsPage组件
        self.settings_page = SettingsPage(self)
        # BasePage.__init__已经调用了setup_ui()，不需要重复调用
        # 连接设置变更信号
        self.settings_page.setting_changed.connect(self.on_setting_changed)
        self.stacked_widget.addWidget(self.settings_page)
        
        # 帮助页面 - 使用新的HelpPage组件
        self.help_page = HelpPage(self)
        # BasePage.__init__已经调用了setup_ui()，不需要重复调用
        # 连接帮助操作信号
        self.help_page.help_action_requested.connect(self.on_help_action)
        self.stacked_widget.addWidget(self.help_page)
        
    def create_home_page(self):
        """创建首页"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 8, 15, 15)  # 减少顶部间距从15px到8px
        layout.setSpacing(12)  # 减少整体间距从15px到12px
        
        # 导入SearchBox类
        from search_box import SearchBox
        
        # 搜索框和新建按钮区域 - 调整到内容区域顶部
        search_layout = QHBoxLayout()
        
        # 使用自定义搜索框组件
        self.search_input = SearchBox()
        # 连接搜索信号到处理方法
        self.search_input.searchTextChanged.connect(self.on_search_text_changed)
        search_layout.addWidget(self.search_input)
        
        # 创建方形SVG图标按钮替换文本按钮
        add_btn = self.create_add_button()
        search_layout.addWidget(add_btn)
        
        layout.addLayout(search_layout)
        
        # 工具栏
        toolbar_widget = self.create_toolbar()
        layout.addWidget(toolbar_widget)
        
        # 分类筛选栏容器（默认隐藏）
        self.category_filter_bar = QWidget()
        self.category_filter_bar.setFixedHeight(150)  # 从50px增加到150px（3倍）
        self.category_filter_bar.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 0px;
            }
        """)
        
        # 为分类筛选栏设置流式布局容器
        category_scroll = QScrollArea(self.category_filter_bar)
        category_scroll.setWidgetResizable(True)
        category_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        category_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        category_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """)
        
        # 创建分类筛选栏的主布局
        category_main_layout = QVBoxLayout(self.category_filter_bar)
        category_main_layout.setContentsMargins(0, 0, 0, 0)
        category_main_layout.addWidget(category_scroll)
        
        # 创建可滚动的内容容器
        self.category_content_widget = QWidget()
        self.category_filter_layout = FlowLayout(self.category_content_widget)
        self.category_filter_layout.setContentsMargins(12, 8, 12, 8)
        self.category_filter_layout.setSpacing(8)
        
        category_scroll.setWidget(self.category_content_widget)
        
        # 添加分类筛选栏到布局
        layout.addWidget(self.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.tag_filter_area = QScrollArea()
        self.tag_filter_area.setFixedHeight(150)  # 从50px增加到150px（3倍）
        self.tag_filter_area.setWidgetResizable(True)
        self.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 改为按需显示垂直滚动条
        self.tag_filter_area.setStyleSheet("""
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #BAE6FD;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #93C5FD;
            }
        """)
        self.tag_filter_area.hide()
        
        # 标签筛选栏内容容器 - 使用流式布局
        tag_filter_widget = QWidget()
        self.tag_filter_layout = FlowLayout(tag_filter_widget)
        self.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.tag_filter_layout.setSpacing(6)
        self.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.tag_filter_area)
        
        # 初始状态下隐藏分类筛选栏
        self.category_filter_bar.hide()
        
        # 创建堆叠容器来切换列表视图和卡片视图
        self.view_stack = QStackedWidget()
        
        # 列表视图
        self.prompt_list = QListWidget()
        self.prompt_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #F9FAFB;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
        """)
        self.view_stack.addWidget(self.prompt_list)
        
        # 卡片视图
        self.card_view = self.create_card_view()
        self.view_stack.addWidget(self.card_view)
        
        # 根据保存的设置显示相应视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
        
        layout.addWidget(self.view_stack)
        
        return page
        

        


        
    def on_trash_thumbnail_clicked(self, index, media_files):
        """处理回收站中缩略图点击事件"""
        if not media_files:
            return
            
        media_path = media_files[index]
        file_path = Path(media_path)
        
        print(f"回收站缩略图被点击: {file_path}")
        
        if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件，使用系统默认播放器打开
            print(f"尝试使用系统播放器打开视频: {file_path}")
            success = open_video_with_system_player(file_path)
            if not success:
                print(f"打开视频失败: {file_path}")
                QMessageBox.warning(self.window(), "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
            else:
                print(f"成功打开视频: {file_path}")
        else:
            # 图片文件，打开图片查看对话框
            print(f"打开图片查看对话框: {file_path}")
            dialog = ImageViewerDialog(media_files, index, self.window())
            dialog.exec()
    
    def restore_prompt(self, prompt_id, show_confirmation=True):
        """从回收站恢复提示词"""
        if show_confirmation:
            reply = QMessageBox.question(
                self,
                "确认恢复",
                "确定要恢复这个提示词吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            
            if reply != QMessageBox.Yes:
                return
        
        if hasattr(self.parent_window, 'model'):
            try:
                self.parent_window.model.restore_prompt(prompt_id)
                
                # 刷新回收站和主页
                self.trash_page.load_data_with_model(model=self.parent_window.model)
                self.refresh_prompt_list()
                
                # 显示状态提示
                if show_confirmation:  # 只在单个操作时显示状态
                    self.parent_window.status_bar.set_status("提示词已恢复")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "恢复失败",
                    f"恢复提示词时出错: {str(e)}"
                )
    
    def permanently_delete_prompt(self, prompt_id, show_confirmation=True):
        """永久删除提示词"""
        if show_confirmation:
            reply = QMessageBox.warning(
                self,
                "确认永久删除",
                "确定要永久删除这个提示词吗？此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
        
        if hasattr(self.parent_window, 'model'):
            try:
                self.parent_window.model.permanently_delete_prompt(prompt_id)
                
                # 刷新回收站
                self.trash_page.load_data_with_model(model=self.parent_window.model)
                
                # 显示状态提示
                if show_confirmation:  # 只在单个操作时显示状态
                    self.parent_window.status_bar.set_status("提示词已永久删除")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "删除失败",
                    f"永久删除提示词时出错: {str(e)}"
                )
    
    def empty_trash(self):
        """清空回收站"""
        if hasattr(self.parent_window, 'model'):
            deleted_prompts = self.parent_window.model.get_deleted_prompts()
            if not deleted_prompts:
                self.parent_window.status_bar.set_status("回收站已经是空的")
                return
                
            reply = QMessageBox.warning(
                self,
                "确认清空回收站",
                f"确定要永久删除回收站中的所有 {len(deleted_prompts)} 个提示词吗？此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    # 永久删除所有回收站中的提示词
                    for prompt in deleted_prompts:
                        self.parent_window.model.permanently_delete_prompt(prompt.id)
                    
                    # 刷新回收站
                    self.trash_page.load_data_with_model(model=self.parent_window.model)
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status("回收站已清空")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "清空失败",
                        f"清空回收站时出错: {str(e)}"
                    )
        

        
    def switch_to_page(self, page_key):
        """切换到指定页面"""
        if page_key == "home":
            self.stacked_widget.setCurrentWidget(self.home_page)
            # 刷新提示词列表
            self.refresh_prompt_list()
        elif page_key == "trash":
            print(f"🔄 切换到回收站页面...")
            self.stacked_widget.setCurrentWidget(self.trash_page)
            print(f"📄 当前页面: {self.stacked_widget.currentWidget()}")
            print(f"📄 回收站页面: {self.trash_page}")
            # 调用新实现的refresh方法
            if hasattr(self.trash_page, 'refresh'):
                print(f"🔄 调用回收站refresh方法...")
                self.trash_page.refresh()
            else:
                print(f"❌ 回收站页面没有refresh方法")
        elif page_key == "settings":
            self.stacked_widget.setCurrentWidget(self.settings_page)
        elif page_key == "help":
            self.stacked_widget.setCurrentWidget(self.help_page)
            
    def on_setting_changed(self, setting_name, value):
        """处理设置变更信号"""
        # 可以在这里添加设置变更的具体处理逻辑
        print(f"设置变更: {setting_name} = {value}")
        
    def on_help_action(self, action_name):
        """处理帮助操作信号"""
        # 可以在这里添加帮助操作的具体处理逻辑
        print(f"帮助操作: {action_name}")
            
    def create_toolbar(self):
        """创建工具栏 - 无边框设计"""
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(40)
        toolbar_widget.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-radius: 6px;
                border: none;
            }
        """)
        
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(12, 0, 12, 0)
        toolbar_layout.setSpacing(8)
        
        # 左侧工具栏标识
        toolbar_label = QLabel("工具栏")
        toolbar_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        """)
        toolbar_layout.addWidget(toolbar_label)
        
        # 添加筛选按钮组
        filter_buttons_layout = QHBoxLayout()
        filter_buttons_layout.setSpacing(4)
        
        # 准备 SVG 图标内容
        filter_icons = self.get_filter_icons()
        
        # 创建三个筛选按钮
        self.favorite_filter_btn = FilterToggleButton(
            filter_icons['favorite'], "收藏筛选", "favorite", self
        )
        self.favorite_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("favorite"))
        filter_buttons_layout.addWidget(self.favorite_filter_btn)
        
        self.category_filter_btn = FilterToggleButton(
            filter_icons['category'], "分类筛选", "category", self
        )
        self.category_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("category"))
        filter_buttons_layout.addWidget(self.category_filter_btn)
        
        self.tag_filter_btn = FilterToggleButton(
            filter_icons['tag'], "标签筛选", "tag", self
        )
        self.tag_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("tag"))
        filter_buttons_layout.addWidget(self.tag_filter_btn)
        
        # 添加工具箱按钮
        self.toolbox_btn = ToolboxButton(
            filter_icons['toolbox'], "工具箱", "toolbox", self
        )
        filter_buttons_layout.addWidget(self.toolbox_btn)
        
        toolbar_layout.addLayout(filter_buttons_layout)
        toolbar_layout.addStretch()
        
        # 创建排序按钮
        self.sort_button = SortButton(self)
        toolbar_layout.addWidget(self.sort_button)
        
        # 右侧单个视图切换按钮
        self.view_toggle_btn = self.create_single_view_toggle_button()
        toolbar_layout.addWidget(self.view_toggle_btn)
        
        return toolbar_widget
    
    def create_single_view_toggle_button(self):
        """创建单个视图切换按钮"""
        # 卡片视图图标（默认显示）
        card_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 列表视图图标
        list_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 创建按钮，存储两个图标
        btn = QPushButton()
        btn.setFixedSize(32, 28)
        btn.card_svg = card_svg
        btn.list_svg = list_svg
        btn.current_view = "card"  # 默认卡片视图
        
        # 设置初始样式和图标
        self.update_toggle_button_style(btn)
        
        # 连接点击事件
        btn.clicked.connect(self.toggle_view)
        
        return btn
    
    def toggle_view(self):
        """切换视图（循环切换）"""
        # 切换视图类型
        if self.current_view == "card":
            self.current_view = "list"
        else:
            self.current_view = "card"
        
        # 保存视图设置
        settings = QSettings("PromptAssistant", "AppSettings")
        settings.setValue("view_type", self.current_view)
        
        # 更新按钮显示
        self.update_toggle_button_style(self.view_toggle_btn)
        
        # 切换显示的视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if self.current_view == 'card' else '列表'}视图")
    
    def create_card_view(self):
        """创建卡片视图容器 - 一行一个卡片，自适应宽度"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)     # 启用垂直滚动条
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """)
        
        # 创建卡片容器
        self.card_container = QWidget()
        self.card_layout = QVBoxLayout(self.card_container)
        self.card_layout.setContentsMargins(15, 15, 15, 15)  # 外部容器边距
        self.card_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.card_layout.setAlignment(Qt.AlignTop)
        
        # 创建垂直布局来放置卡片（一行一个）
        self.cards_list_widget = QWidget()
        self.cards_list_layout = QVBoxLayout(self.cards_list_widget)
        self.cards_list_layout.setContentsMargins(0, 0, 0, 0)
        self.cards_list_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.cards_list_layout.setAlignment(Qt.AlignTop)
        
        self.card_layout.addWidget(self.cards_list_widget)
        self.card_layout.addStretch()
        
        scroll_area.setWidget(self.card_container)
        return scroll_area

    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        self.update_view_buttons()
        
        # 切换显示的视图
        if view_type == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        # 保存视图设置到QSettings中
        settings = QSettings("PromptAssistant", "AppSettings")
        settings.setValue("view_type", view_type)
        
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if view_type == 'card' else '列表'}视图")
    
    def update_toggle_button_style(self, btn):
        """更新切换按钮样式和图标"""
        # 根据当前视图选择图标和提示文本
        if self.current_view == "card":
            svg_content = btn.card_svg
            tooltip = "当前：卡片视图 (点击切换到列表视图)"
            bg_color = "#3B82F6"  # 蓝色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#3B82F6"
        else:
            svg_content = btn.list_svg
            tooltip = "当前：列表视图 (点击切换到卡片视图)"
            bg_color = "#10B981"  # 绿色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#10B981"
        
        # 设置按钮样式
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 6px;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.current_view == "card" else "#059669"};
                border-color: {"#2563EB" if self.current_view == "card" else "#059669"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.current_view == "card" else "#047857"};
            }}
        """)
        
        # 设置工具提示
        btn.setToolTip(tooltip)
        
        # 更新图标
        self.set_button_svg_icon(btn, svg_content, icon_color)
    
    def set_button_svg_icon(self, button, svg_content, color):
        """设置按钮SVG图标"""
        
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(16, 16))

    def update_card_view(self, prompts):
        """更新卡片视图显示 - 一行一个卡片"""
        # 保存已选中卡片的ID
        selected_prompt_ids = [card.prompt.id for card in self.selected_cards] if self.selected_cards else []
        
        # 清除现有卡片
        if hasattr(self, 'cards_list_layout'):
            for i in reversed(range(self.cards_list_layout.count())):
                child = self.cards_list_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
        
        # 清空选中卡片列表
        self.selected_cards = []
        
        # 创建卡片，每行一个
        for prompt in prompts:
            # 创建卡片外围容器 - 用于实现边框效果
            container = QFrame()
            container.setFrameShape(QFrame.Box)
            container.setFrameShadow(QFrame.Plain)
            container.setLineWidth(1)
            # 使用中性色边框
            container.setStyleSheet("""
                QFrame {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    padding: 0px;
                    background-color: white;
                }
                QFrame:hover {
                    border: 1px solid #9ca3af;
                }
            """)
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
            container_layout.setSpacing(0)
            
            # 创建卡片内容
            card = PromptCardWidget(prompt, self)
            # 使用ContentArea的颜色服务
            card.color_service = self.color_service
            # 卡片自身不显示边框
            card.setStyleSheet("QFrame { border: none; background-color: white; }")
            card.setFrameShape(QFrame.NoFrame)
            
            # 恢复选中状态
            if prompt.id in selected_prompt_ids:
                card.set_selected(True)
                self.selected_cards.append(card)
                
            container_layout.addWidget(card)
            
            # 添加到卡片列表布局
            self.cards_list_layout.addWidget(container)
        
        # 更新状态栏显示选中数量
        self.update_selection_status()

    def update_view_buttons(self):
        """更新视图按钮状态"""
        self.card_view_btn.set_active(self.current_view == "card")
        self.list_view_btn.set_active(self.current_view == "list")

    def create_add_button(self):
        """创建方形SVG新建按钮"""
        # 定义新建按钮的SVG图标
        add_svg = '''<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 12H19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        btn = AddIconButton(add_svg, "新建提示词", self)
        btn.clicked.connect(self.add_prompt)
        return btn
        
    def add_prompt(self):
        """添加新提示词"""
        from create_prompt_dialog import CreatePromptDialog
        
        if self.parent_window:
            self.parent_window.status_bar.set_status("打开新建提示词对话框...")
            
            # 创建新建提示词对话框
            dialog = CreatePromptDialog(self.parent_window)
            
            # 连接对话框的accepted和rejected信号
            dialog.accepted.connect(lambda: self.on_dialog_accepted(dialog, "提示词创建成功"))
            dialog.rejected.connect(lambda: self.on_dialog_rejected(dialog, "取消创建提示词"))
            
            # 保存对话框引用并显示（非模态）
            self.active_dialogs.append(dialog)
            dialog.show()
    
    def on_dialog_accepted(self, dialog, message):
        """对话框接受时的处理"""
        if self.parent_window:
            self.parent_window.status_bar.set_status(message)
            # 刷新提示词列表
            self.refresh_prompt_list()
        
        # 从活动对话框列表中移除
        if dialog in self.active_dialogs:
            self.active_dialogs.remove(dialog)
    
    def on_dialog_rejected(self, dialog, message):
        """对话框拒绝时的处理"""
        if self.parent_window:
            self.parent_window.status_bar.set_status(message)
        
        # 从活动对话框列表中移除
        if dialog in self.active_dialogs:
            self.active_dialogs.remove(dialog)
    
    def get_filter_icons(self):
        """获取筛选按钮的 SVG 图标"""
        return {
            'favorite': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'tag': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0136 20.9135 12.7709 21.0141C12.5282 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4818 21.1148 11.2391 21.0141C10.9964 20.9135 10.7757 20.766 10.59 20.58L2 12V2H12L20.59 10.59C20.9625 10.9647 21.1716 11.4716 21.1716 12C21.1716 12.5284 20.9625 13.0353 20.59 13.41V13.41Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 7H7.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'toolbox': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M14.7 6.3a1 1 0 000 1.4l1.6 1.6a1 1 0 001.4 0l3.77-3.77a6 6 0 01-7.94 7.94l-6.91 6.91a2.12 2.12 0 01-3-3l6.91-6.91a6 6 0 017.94-7.94l-3.76 3.76z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
    
    def apply_filters_and_update_views(self):
        """应用筛选并更新视图 - 所有筛选逻辑的中心"""
        # 从完整列表创建副本
        filtered_prompts = self.all_prompts[:]
        
        # 首先过滤掉已删除的提示词（仅在主页显示未删除的）
        filtered_prompts = [prompt for prompt in filtered_prompts if not hasattr(prompt, 'is_deleted') or prompt.is_deleted == 0]
        
        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选（多选支持）
        if self.active_filters.get('tags') and len(self.active_filters['tags']) > 0:
            selected_tags = self.active_filters['tags']
            filtered_prompts = [prompt for prompt in filtered_prompts
                              if hasattr(prompt, 'tags') and prompt.tags and 
                              selected_tags.issubset(set(prompt.tags))]
        
        # 检查搜索关键词筛选
        search_keyword = self.active_filters.get('search', '').strip()
        if search_keyword:
            filtered_prompts = [
                prompt for prompt in filtered_prompts
                if (
                    search_keyword.lower() in prompt.title.lower() or
                    search_keyword.lower() in prompt.content.lower() or
                    any(search_keyword.lower() in tag.lower() for tag in prompt.tags if tag)
                )
            ]
        
        # 应用排序
        filtered_prompts = self.sort_prompts(filtered_prompts)
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if search_keyword:
                self.parent_window.status_bar.set_info(f'搜索"{search_keyword}": {filtered_count}/{total_count} 个提示词')
            elif self.active_filters.get('favorite', False):
                self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
            else:
                self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
    
    def apply_sort(self):
        """应用当前排序方式"""
        # 直接调用apply_filters_and_update_views，它会处理排序逻辑
        self.apply_filters_and_update_views()
    
    def sort_prompts(self, prompts_list):
        """根据当前排序方式对提示词列表进行排序"""
        # 加载排序设置
        settings = QSettings("PromptAssistant", "AppSettings")
        self.current_sort = settings.value("sort_method", "updated_desc")
        
        # 首先，根据是否置顶将列表分为两部分：置顶和非置顶
        pinned_prompts = []
        unpinned_prompts = []
        
        for prompt in prompts_list:
            # 检查是否有置顶属性，并根据置顶状态分类
            is_pinned = hasattr(prompt, 'is_pinned') and prompt.is_pinned == 1
            if is_pinned:
                pinned_prompts.append(prompt)
            else:
                unpinned_prompts.append(prompt)
        
        # 对置顶和非置顶部分分别进行排序
        def sort_by_method(prompts):
            if self.current_sort == "updated_desc":
                # 按更新时间降序（最新的在前）
                prompts.sort(key=lambda p: p.updated_at if hasattr(p, 'updated_at') and p.updated_at else "", reverse=True)
            elif self.current_sort == "updated_asc":
                # 按更新时间升序（最旧的在前）
                prompts.sort(key=lambda p: p.updated_at if hasattr(p, 'updated_at') and p.updated_at else "")
            elif self.current_sort == "created_desc":
                # 按创建时间降序（最新的在前）
                prompts.sort(key=lambda p: p.created_at if hasattr(p, 'created_at') and p.created_at else "", reverse=True)
            elif self.current_sort == "created_asc":
                # 按创建时间升序（最旧的在前）
                prompts.sort(key=lambda p: p.created_at if hasattr(p, 'created_at') and p.created_at else "")
            elif self.current_sort == "title_asc":
                # 按标题升序（A-Z）
                prompts.sort(key=lambda p: p.title.lower())
            elif self.current_sort == "title_desc":
                # 按标题降序（Z-A）
                prompts.sort(key=lambda p: p.title.lower(), reverse=True)
            elif self.current_sort == "category":
                # 按类别排序
                prompts.sort(key=lambda p: (p.category.lower() if p.category else "", p.title.lower()))
            return prompts
            
        # 分别对置顶和非置顶部分进行排序
        pinned_prompts = sort_by_method(pinned_prompts)
        unpinned_prompts = sort_by_method(unpinned_prompts)
        
        # 合并结果：置顶部分在前，非置顶部分在后
        return pinned_prompts + unpinned_prompts

    def on_category_button_clicked(self, category_name):
        """分类按钮点击处理器"""
        # 更新分类筛选状态
        self.active_filters['category'] = category_name
        
        # 更新按钮状态
        self.update_category_button_states()
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def update_category_button_states(self):
        """更新分类按钮的激活状态"""
        current_category = self.active_filters.get('category', '全部')
        
        for category_name, button in self.category_buttons.items():
            if category_name == current_category:
                # 激活状态样式
                if category_name == "全部":
                    button.setStyleSheet('''
                        QPushButton {
                            background-color: #3B82F6;
                            color: white;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #2563EB;
                            border-color: #2563EB;
                        }
                    ''')
                else:
                    button.setStyleSheet('''
                        QPushButton {
                            background-color: #EBF4FF;
                            color: #1E40AF;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #DBEAFE;
                            border-color: #2563EB;
                        }
                    ''')
            else:
                # 默认状态样式
                button.setStyleSheet('''
                    QPushButton {
                        background-color: white;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        border-radius: 6px;
                        padding: 6px 12px;
                        font-size: 12px;
                        font-weight: 400;
                        min-width: 0px;
                    }
                    QPushButton:hover {
                        background-color: #F3F4F6;
                        border-color: #9CA3AF;
                    }
                ''')

    def populate_tag_filters(self):
        """动态填充标签筛选按钮"""
        
        # 清除现有按钮和引用
        self.tag_buttons.clear()
        while self.tag_filter_layout.count():
            item = self.tag_filter_layout.takeAt(0)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
        
        # 从所有提示词中提取唯一标签
        all_tags = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'tags') and prompt.tags:
                for tag in prompt.tags:
                    if tag and tag.strip():
                        all_tags.add(tag.strip())
        
        # 转换为排序列表
        unique_tags = sorted(list(all_tags))
        
        # 添加"全部"按钮
        all_button = QPushButton("全部")
        all_button.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        all_button.adjustSize()
        all_button.clicked.connect(lambda: self.on_tag_button_clicked("全部"))
        self.tag_buttons["全部"] = all_button
        self.tag_filter_layout.addWidget(all_button)
        
        # 添加各标签按钮
        for tag in unique_tags:
            button = QPushButton(tag)
            button.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
            button.adjustSize()
            button.clicked.connect(lambda checked, t=tag: self.on_tag_button_clicked(t))
            self.tag_buttons[tag] = button
            self.tag_filter_layout.addWidget(button)
        
        # 更新按钮状态
        self.update_tag_button_states()

    def on_tag_button_clicked(self, tag_name):
        """标签按钮点击处理器（支持多选）"""
        if tag_name == '全部':
            # 点击"全部"按钮，清空所有选中的标签
            self.active_filters['tags'].clear()
        else:
            # 处理普通标签按钮的多选逻辑
            if tag_name in self.active_filters['tags']:
                # 如果标签已选中，则取消选择
                self.active_filters['tags'].remove(tag_name)
            else:
                # 如果标签未选中，则添加到选中集合
                self.active_filters['tags'].add(tag_name)
        
        # 更新按钮状态
        self.update_tag_button_states()
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def update_tag_button_states(self):
        """更新标签按钮的激活状态（支持多选）"""
        selected_tags = self.active_filters.get('tags', set())
        
        # 更新所有标签按钮的状态
        for tag_name, button in self.tag_buttons.items():
            if tag_name == '全部':
                # "全部"按钮：当没有其他标签选中时激活
                if len(selected_tags) == 0:
                    button.setStyleSheet(self.get_active_filter_button_style())
                else:
                    button.setStyleSheet(self.get_filter_button_style())
            else:
                # 普通标签按钮：根据是否在选中集合中设置状态
                if tag_name in selected_tags:
                    button.setStyleSheet(self.get_active_filter_button_style())
                else:
                    button.setStyleSheet(self.get_filter_button_style())

    def on_filter_button_clicked(self, filter_key):
        """筛选按钮点击处理方法"""
        print(f"筛选按钮被点击: {filter_key}")
        
        # 切换按钮激活状态和筛选器状态
        if filter_key == "favorite":
            current_state = self.favorite_filter_btn.is_active
            new_state = not current_state
            self.favorite_filter_btn.set_active(new_state)
            self.active_filters['favorite'] = new_state
            
            # 不再需要完全刷新，直接应用筛选即可，前面更新了收藏状态会自动同步
            # 应用筛选并更新视图
            self.apply_filters_and_update_views()
            
            # 更新状态栏的收藏提示
            if self.parent_window:
                if new_state:
                    self.parent_window.status_bar.set_status("已激活收藏筛选")
                else:
                    self.parent_window.status_bar.set_status("已取消收藏筛选")
            
        elif filter_key == "category":
            # 切换分类筛选栏的显示/隐藏
            current_state = self.category_filter_btn.is_active
            new_state = not current_state
            
            # 如果要显示分类筛选框，先隐藏标签筛选框
            if new_state:
                # 隐藏标签筛选框
                self.tag_filter_btn.set_active(False)
                self.tag_filter_area.hide()
                # 重置标签筛选状态
                if self.active_filters['tags']:
                    self.active_filters['tags'].clear()
                    self.update_tag_button_states()
                    self.apply_filters_and_update_views()
            
            self.category_filter_btn.set_active(new_state)
            self.category_filter_bar.setVisible(new_state)
            
            # 如果隐藏分类筛选框，重置分类筛选为"全部"
            if not new_state:
                self.active_filters['category'] = '全部'
                self.update_category_button_states()
                self.apply_filters_and_update_views()
            
            # 当激活分类筛选时，重置标签筛选
            if new_state:
                self.on_tag_button_clicked('全部')
            
        elif filter_key == "tag":
            # 切换标签筛选栏的显示/隐藏
            current_state = self.tag_filter_btn.is_active
            new_state = not current_state
            
            # 如果要显示标签筛选框，先隐藏分类筛选框
            if new_state:
                # 隐藏分类筛选框并重置其状态
                self.category_filter_btn.set_active(False)
                self.category_filter_bar.hide()
                # 重置分类筛选状态
                if self.active_filters['category'] != '全部':
                    self.active_filters['category'] = '全部'
                    self.update_category_button_states()
                    self.apply_filters_and_update_views()
            
            # 更新标签筛选按钮状态和显示
            self.tag_filter_btn.set_active(new_state)
            self.tag_filter_area.setVisible(new_state)
            
            # 如果隐藏标签筛选框，重置标签筛选
            if not new_state:
                if self.active_filters['tags']:
                    self.active_filters['tags'].clear()
                    self.update_tag_button_states()
                    self.apply_filters_and_update_views()
        # 更新状态栏显示
        if self.parent_window:
            filter_names = {
                "favorite": "收藏",
                "category": "分类", 
                "tag": "标签"
            }
            if filter_key == "favorite":
                status = "激活" if self.active_filters['favorite'] else "取消"
            elif filter_key == "category":
                status = "显示" if self.category_filter_btn.is_active else "隐藏"
            else:
                status = "激活" if getattr(getattr(self, f"{filter_key}_filter_btn"), "is_active") else "取消"
            self.parent_window.status_bar.set_status(f"{status}{filter_names[filter_key]}筛选")

    def get_all_categories(self):
        """获取所有唯一分类"""
        categories = set()
        for prompt in self.all_prompts:
            if prompt.category:
                categories.add(prompt.category)
        return sorted(list(categories))
    
    def populate_category_filters(self):
        """动态填充分类筛选按钮"""
        # 获取所有分类
        categories = self.get_all_categories()
        
        # 清除现有按钮
        while self.category_filter_layout.count():
            item = self.category_filter_layout.takeAt(0)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)
        
        # 从所有提示词中提取唯一的分类名称
        categories = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'category') and prompt.category and prompt.category.strip():
                categories.add(prompt.category.strip())
        
        # 排序分类列表
        sorted_categories = sorted(list(categories))
        
        # 创建"全部"按钮
        all_btn = self.create_category_button("全部", is_all_button=True)
        all_btn.clicked.connect(lambda: self.on_category_filter_clicked("全部"))
        self.category_buttons["全部"] = all_btn
        self.category_filter_layout.addWidget(all_btn)
        
        # 为每个分类创建按钮
        for category in sorted_categories:
            btn = self.create_category_button(category)
            btn.clicked.connect(lambda checked, cat=category: self.on_category_filter_clicked(cat))
            self.category_buttons[category] = btn
            self.category_filter_layout.addWidget(btn)
        
    def create_category_button(self, category_name, is_all_button=False):
        """创建分类按钮"""
        btn = QPushButton(category_name)
        btn.setFixedHeight(32)
        btn.category_name = category_name
        btn.is_active = False
        
        # 设置按钮自适应宽度
        btn.setSizePolicy(QSizePolicy.Minimum, QSizePolicy.Fixed)
        btn.adjustSize()
        
        # 设置按钮样式
        if is_all_button:
            # "全部"按钮的特殊样式
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: 1px solid #3B82F6;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 500;
                    min-width: 0px;
                }
                QPushButton:hover {
                    background-color: #2563EB;
                    border-color: #2563EB;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            """)
            btn.is_active = True  # "全部"按钮默认激活
        else:
            # 普通分类按钮样式
            btn.setStyleSheet("""
                QPushButton {
                    background-color: white;
                    color: #374151;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 400;
                    min-width: 0px;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                    border-color: #9CA3AF;
                }
                QPushButton:pressed {
                    background-color: #E5E7EB;
                }
            """)
        
        return btn
    
    def on_category_filter_clicked(self, category_name):
        """分类筛选按钮点击处理 - 支持取消选择"""
        print(f"分类筛选被点击: {category_name}")
        
        # 检查当前选中的分类，支持取消选择
        current_category = self.active_filters.get('category', '全部')
        
        # 如果点击的是已经选中的分类（且不是"全部"），则取消选择，回到"全部"
        if category_name == current_category and category_name != "全部":
            category_name = "全部"
        
        # 更新筛选状态
        self.active_filters['category'] = category_name
        
        # 更新按钮状态 - 使用category_buttons字典直接访问
        for btn_category_name, btn in self.category_buttons.items():
            is_selected = btn_category_name == category_name
            btn.is_active = is_selected
            
            if is_selected:
                if category_name == "全部":
                    # "全部"按钮激活样式
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #3B82F6;
                            color: white;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #2563EB;
                            border-color: #2563EB;
                        }
                    """)
                else:
                    # 普通分类按钮激活样式
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #EBF4FF;
                            color: #1E40AF;
                            border: 1px solid #3B82F6;
                            border-radius: 6px;
                            padding: 6px 12px;
                            font-size: 12px;
                            font-weight: 500;
                            min-width: 0px;
                        }
                        QPushButton:hover {
                            background-color: #DBEAFE;
                            border-color: #2563EB;
                        }
                    """)
            else:
                # 未激活状态样式
                btn.setStyleSheet("""
                    QPushButton {
                        background-color: white;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        border-radius: 6px;
                        padding: 6px 12px;
                        font-size: 12px;
                        font-weight: 400;
                        min-width: 0px;
                    }
                    QPushButton:hover {
                        background-color: #F3F4F6;
                        border-color: #9CA3AF;
                    }
                """)
        
        # 应用分类筛选
        self.apply_category_filter(category_name)
        
        # 更新状态栏
        if self.parent_window:
            if category_name == "全部":
                self.parent_window.status_bar.set_status("显示所有分类")
            else:
                self.parent_window.status_bar.set_status(f"筛选分类: {category_name}")
    
    def apply_category_filter(self, category_name):
        """应用分类筛选"""
        if category_name == "全部":
            # 显示所有提示词（但仍要考虑其他筛选器）
            filtered_prompts = self.all_prompts[:]
        else:
            # 按分类筛选
            filtered_prompts = [prompt for prompt in self.all_prompts 
                              if hasattr(prompt, 'category') and prompt.category == category_name]
        
        # 如果收藏筛选也激活，需要进一步筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if category_name == "全部":
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
            else:
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"{category_name} (收藏): {filtered_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"{category_name}: {filtered_count} 个提示词")

    def refresh_prompt_list(self):
        """刷新提示词列表"""
        if self.parent_window:
            # 加载数据（从数据库中获取未删除的提示词）
            self.parent_window.load_data()
            
    def share_prompts(self):
        """打开分享提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = SharePromptDialog(self.parent_window, self.parent_window.model)
            dialog.exec()
    
    def share_prompts_batch(self):
        """打开批量分享提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = SharePromptDialog(self.parent_window, self.parent_window.model)
            dialog.exec()
    
    def import_prompts_batch(self):
        """打开批量获取提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ImportPromptDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("批量提示词导入成功")
    
    def import_prompt(self):
        """打开获取提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ImportPromptDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("提示词导入成功")
    
    def export_prompts(self):
        """打开导出提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ExportPromptDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("提示词导出成功")
    
    def import_prompts_from_file(self):
        """打开从文件导入提示词对话框"""
        if self.parent_window and hasattr(self.parent_window, 'model'):
            dialog = ImportFileDialog(self.parent_window, self.parent_window.model)
            if dialog.exec() == QDialog.Accepted:
                # 刷新提示词列表
                self.refresh_prompt_list()
                self.parent_window.status_bar.set_status("文件导入成功")

    def on_card_selection_changed(self, card, is_selected):
        """处理卡片选中状态变化"""
        if is_selected and card not in self.selected_cards:
            self.selected_cards.append(card)
        elif not is_selected and card in self.selected_cards:
            self.selected_cards.remove(card)
            
        # 更新状态栏显示
        self.update_selection_status()
    
    def update_selection_status(self):
        """更新状态栏显示选中卡片数量"""
        if self.parent_window and hasattr(self.parent_window, 'status_bar'):
            if self.selected_cards:
                self.parent_window.status_bar.set_info(f"已选择 {len(self.selected_cards)} 个提示词")
            else:
                # 恢复显示总数或筛选信息
                total_count = len(self.all_prompts)
                
                # 计算当前筛选后显示的卡片数量
                filtered_count = 0
                if hasattr(self, 'cards_list_layout'):
                    filtered_count = self.cards_list_layout.count()
                
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
    
    def clear_card_selections(self):
        """清除所有卡片的选中状态"""
        for card in self.selected_cards:
            card.set_selected(False)
        self.selected_cards.clear()
        self.update_selection_status()
        
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 按下Escape键清除所有选择
        if event.key() == Qt.Key_Escape:
            # 在主页
            if hasattr(self, 'selected_cards') and self.selected_cards:
                self.clear_card_selections()
            # 更新状态栏
            if self.parent_window:
                self.parent_window.status_bar.set_status("已取消所有选择")

        # 按下Delete键删除选中的提示词
        elif event.key() == Qt.Key_Delete:
            # 在主页
            if hasattr(self, 'selected_cards') and self.selected_cards:
                self.delete_selected_prompts()

        # 按下Ctrl+A全选
        elif event.key() == Qt.Key_A and event.modifiers() & Qt.ControlModifier:
            # 检查当前页面
            current_page = self.stacked_widget.currentWidget()
            
            # 根据当前页面判断
            if current_page == self.home_page:
                if hasattr(self, 'selected_cards') and hasattr(self, 'cards_list_layout'):
                    # 先清除现有选择
                    self.clear_card_selections()
                    
                    # 全选主页卡片
                    for i in range(self.cards_list_layout.count()):
                        item = self.cards_list_layout.itemAt(i)
                        if item and item.widget():
                            container = item.widget()
                            # 主页卡片被包装在容器中，需要找到实际的卡片
                            if hasattr(container, 'layout'):
                                container_layout = container.layout()
                                if container_layout and container_layout.count() > 0:
                                    card_item = container_layout.itemAt(0)
                                    if card_item and card_item.widget():
                                        card = card_item.widget()
                                        if hasattr(card, 'set_selected') and hasattr(card, 'is_selected'):
                                            card.is_selected = True
                                            card.set_selected(True)
                                            # 通知父容器卡片选择状态变化
                                            self.on_card_selection_changed(card, True)
                                            # 强制刷新卡片UI
                                            card.update()
                    
                    # 强制刷新整个卡片列表区域
                    if hasattr(self, 'cards_list_layout'):
                        cards_widget = self.cards_list_layout.parent()
                        if cards_widget:
                            cards_widget.update()
                    
                    # 更新状态栏
                    if self.parent_window:
                        self.parent_window.status_bar.set_status("已全选所有提示词")
            elif current_page == self.trash_page:
                # 回收站的全选现在由TrashPage自己处理
                pass
        else:
            super().keyPressEvent(event)
    
    def delete_selected_prompts(self):
        """删除所有选中的提示词"""
        if not self.selected_cards:
            return
        
        # 确认是否要删除
        if self.parent_window:
            prompt_count = len(self.selected_cards)
            reply = QMessageBox.question(
                self,
                "确认删除",
                f"确定要删除选中的 {prompt_count} 个提示词吗？删除的提示词将移至回收站。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                try:
                    # 收集所有选中卡片的提示词ID
                    prompt_ids = [card.prompt.id for card in self.selected_cards]
                    
                    # 从数据库中删除所有选中的提示词
                    if hasattr(self.parent_window, 'model'):
                        for prompt_id in prompt_ids:
                            self.parent_window.model.delete_prompt(prompt_id)
                        
                        # 清空选中状态
                        self.selected_cards.clear()
                        
                        # 更新界面
                        self.refresh_prompt_list()
                        
                        # 如果当前正在显示回收站页面，也需要刷新回收站
                        if (hasattr(self, 'stacked_widget') and 
                            self.stacked_widget.currentWidget() == self.trash_page):
                            self.trash_page.load_data_with_model(model=self.parent_window.model)
                            print("🔄 回收站页面已刷新")
                        
                        # 显示状态提示
                        self.parent_window.status_bar.set_status(f"已将 {prompt_count} 个提示词移至回收站")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "删除失败",
                        f"删除提示词时出错: {str(e)}"
                    )

    def delete_prompt(self, prompt_id):
        """删除单个提示词"""
        # 确认是否要删除
        reply = QMessageBox.question(
            self, 
            "确认删除",
            "确定要删除选中的提示词吗？删除的提示词将移至回收站。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # 从数据库中删除提示词
                if hasattr(self.parent_window, 'model'):
                    self.parent_window.model.delete_prompt(prompt_id)
                    
                    # 更新界面
                    self.refresh_prompt_list()
                    
                    # 如果当前正在显示回收站页面，也需要刷新回收站
                    if (hasattr(self, 'stacked_widget') and 
                        self.stacked_widget.currentWidget() == self.trash_page):
                        self.trash_page.load_data_with_model(model=self.parent_window.model)
                        print("🔄 回收站页面已刷新")
                    
                    # 显示状态提示
                    self.parent_window.status_bar.set_status("提示词已移至回收站")
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "删除失败",
                    f"删除提示词时出错: {str(e)}"
                )

    def clear_all_prompts(self):
        """清空所有提示词"""
        # 确认对话框
        reply = QMessageBox.question(
            self, 
            "确认清空", 
            "确定要清空所有提示词吗？\n\n提示词将被移动到回收站，您可以在回收站中恢复或永久删除它们。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No  # 默认No按钮
        )
        
        if reply == QMessageBox.Yes:
            if self.parent_window and hasattr(self.parent_window, 'model'):
                # 调用模型清空提示词
                success = self.parent_window.model.clear_all_prompts()
                
                if success:
                    # 刷新提示词列表
                    self.refresh_prompt_list()
                    
                    # 如果当前正在显示回收站页面，也需要刷新回收站
                    if (hasattr(self, 'stacked_widget') and 
                        self.stacked_widget.currentWidget() == self.trash_page):
                        self.trash_page.load_data_with_model(model=self.parent_window.model)
                        print("🔄 回收站页面已刷新")
                        
                    self.parent_window.status_bar.set_status("所有提示词已清空")
                else:
                    QMessageBox.warning(self, "操作失败", "清空提示词失败，请重试")
        else:
            self.parent_window.status_bar.set_status("已取消清空操作")

    def on_search_text_changed(self, text):
        """
        处理搜索框文本变化
        
        参数:
            text (str): 搜索框中的文本
        """
        # 更新搜索关键词筛选条件
        self.active_filters['search'] = text
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def batch_restore_prompts(self, prompt_ids):
        """批量恢复提示词"""
        if hasattr(self.parent_window, 'model'):
            success_count = 0
            failed_count = 0
            
            for prompt_id in prompt_ids:
                try:
                    self.parent_window.model.restore_prompt(prompt_id)
                    success_count += 1
                except Exception as e:
                    failed_count += 1
                    print(f"恢复提示词 {prompt_id} 失败: {str(e)}")
            
            # 刷新回收站和主页
            self.trash_page.load_data_with_model(model=self.parent_window.model)
            self.refresh_prompt_list()
            
            # 显示状态提示
            if failed_count == 0:
                self.parent_window.status_bar.set_status(f"已成功恢复 {success_count} 个提示词")
            else:
                self.parent_window.status_bar.set_status(f"恢复完成：成功 {success_count} 个，失败 {failed_count} 个")

    def batch_delete_prompts(self, prompt_ids):
        """批量永久删除提示词"""
        if hasattr(self.parent_window, 'model'):
            success_count = 0
            failed_count = 0
            
            for prompt_id in prompt_ids:
                try:
                    self.parent_window.model.permanently_delete_prompt(prompt_id)
                    success_count += 1
                except Exception as e:
                    failed_count += 1
                    print(f"删除提示词 {prompt_id} 失败: {str(e)}")
            
            # 刷新回收站
            self.trash_page.load_data_with_model(model=self.parent_window.model)
            
            # 显示状态提示
            if failed_count == 0:
                self.parent_window.status_bar.set_status(f"已成功删除 {success_count} 个提示词")
            else:
                self.parent_window.status_bar.set_status(f"删除完成：成功 {success_count} 个，失败 {failed_count} 个")


class TrashPromptCard(PromptCardWidget):
    """回收站中的提示词卡片，隐藏操作按钮"""
    
    def __init__(self, prompt, parent=None):
        super().__init__(prompt, parent)
        
        # 确保卡片有最小高度，能够被看见
        self.setMinimumHeight(80)
        self.setMaximumHeight(200)  # 增加最大高度以容纳缩略图
        
        # 设置基本样式，确保可见
        self.setStyleSheet("""
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                margin: 2px;
            }
            QFrame:hover {
                border-color: #D1D5DB;
                background-color: #F3F4F6;
            }
        """)
        
        # 连接缩略图点击信号（如果存在）
        self._connect_thumbnail_signals()
        
        # 隐藏操作按钮
        self._hide_action_buttons()
        
        # 应用彻底的CSS样式来隐藏所有可能的图标显示
        self._apply_icon_hiding_styles()
    
    def _hide_action_buttons(self):
        """隐藏所有操作按钮"""
        # 延迟执行，确保UI构建完成
        QTimer.singleShot(0, self._delayed_hide_buttons)
        # 再次延迟执行，确保所有动态创建的控件都被处理
        QTimer.singleShot(100, self._delayed_hide_buttons)
    
    def _delayed_hide_buttons(self):
        """延迟隐藏按钮，确保UI构建完成"""
        # 查找所有可能的控件类型
        widgets_to_check = []
        widgets_to_check.extend(self.findChildren(QPushButton))  # 按钮
        widgets_to_check.extend(self.findChildren(QLabel))       # 标签（可能用于图标显示）
        widgets_to_check.extend(self.findChildren(QWidget))      # 通用控件
        
        for widget in widgets_to_check:
            should_hide = False
            hide_reason = ""
            
            # 通过objectName判断
            if hasattr(widget, 'objectName') and widget.objectName() in ['favorite_btn', 'pin_btn', 'edit_btn', 'delete_btn', 'copy_btn', 'share_btn']:
                should_hide = True
                hide_reason = f"objectName: {widget.objectName()}"
            
            # 通过工具提示判断
            elif hasattr(widget, 'toolTip') and widget.toolTip() in ['收藏', '置顶', '编辑', '删除', '复制', '分享']:
                should_hide = True
                hide_reason = f"toolTip: {widget.toolTip()}"
            
            elif hasattr(widget, 'styleSheet') and widget.styleSheet():
                style = widget.styleSheet()
                if any(icon in style for icon in ['favorite', 'pin', 'edit', 'delete', 'copy', 'share', '⭐', '📌', '✏️', '🗑️', '📋', '📤']):
                    should_hide = True
                    hide_reason = f"styleSheet contains action icon"
            
            # 通过控件文本内容判断（如果是显示图标的标签）
            elif hasattr(widget, 'text') and widget.text():
                text = widget.text()
                if any(icon in text for icon in ['⭐', '📌', '✏️', '🗑️', '📋', '📤']):
                    should_hide = True
                    hide_reason = f"text contains icon: {text}"
            
            # 检查控件的父级容器，如果是操作按钮区域则隐藏
            elif hasattr(widget, 'parent') and widget.parent():
                parent = widget.parent()
                if hasattr(parent, 'objectName') and 'action' in parent.objectName().lower():
                    should_hide = True
                    hide_reason = f"parent is action container"
            
            if should_hide:
                widget.hide()
                widget.setVisible(False)  # 双重保险
                print(f"隐藏操作控件: {widget.__class__.__name__} - {hide_reason}")
        
        # 额外处理：查找可能包含操作按钮的容器并隐藏整个容器
        self._hide_action_containers()
    
    def _hide_action_containers(self):
        """隐藏可能包含操作按钮的容器"""
        # 查找所有容器控件
        containers = self.findChildren(QWidget)
        for container in containers:
            # 检查容器中是否包含多个按钮控件
            child_buttons = container.findChildren(QPushButton)
            if len(child_buttons) >= 3:  # 如果容器包含3个或更多按钮，可能是操作按钮组
                # 检查这些按钮是否都是操作按钮
                action_button_count = 0
                for btn in child_buttons:
                    if (hasattr(btn, 'toolTip') and btn.toolTip() in ['收藏', '置顶', '编辑', '删除', '复制', '分享']) or \
                       (hasattr(btn, 'objectName') and btn.objectName() in ['favorite_btn', 'pin_btn', 'edit_btn', 'delete_btn', 'copy_btn', 'share_btn']):
                        action_button_count += 1
                
                # 如果大部分都是操作按钮，则隐藏整个容器
                if action_button_count >= len(child_buttons) * 0.6:  # 60%以上是操作按钮
                    container.hide()
                    container.setVisible(False)
                    print(f"隐藏操作按钮容器: {container.__class__.__name__} (包含{action_button_count}个操作按钮)")
    
    def _apply_icon_hiding_styles(self):
        """应用CSS样式彻底隐藏所有可能的图标显示"""
        # 延迟执行，确保UI完全构建
        QTimer.singleShot(0, self._delayed_apply_styles)
        QTimer.singleShot(100, self._delayed_apply_styles)
    
    def _delayed_apply_styles(self):
        """延迟应用样式"""
        # 使用Qt支持的样式，只针对操作按钮
        hide_icons_style = """
        /* 只隐藏操作按钮，使用Qt支持的属性 */
        QPushButton[toolTip="收藏"],
        QPushButton[toolTip="置顶"],
        QPushButton[toolTip="编辑"],
        QPushButton[toolTip="删除"],
        QPushButton[toolTip="复制"],
        QPushButton[toolTip="分享"] {
            max-width: 0px;
            max-height: 0px;
            min-width: 0px;
            min-height: 0px;
            margin: 0px;
            padding: 0px;
            border: none;
            background: transparent;
        }
        """
        
        # 隐藏所有可能显示图标的控件类型
        
        # 1. 隐藏QPushButton
        buttons = self.findChildren(QPushButton)
        for button in buttons:
            if button.toolTip() in ['收藏', '置顶', '编辑', '删除', '复制', '分享']:
                button.setStyleSheet(hide_icons_style)
                button.hide()
                button.setVisible(False)
        
        # 2. 隐藏可能显示图标的QLabel
        labels = self.findChildren(QLabel)
        for label in labels:
            if label.toolTip() in ['收藏', '置顶', '编辑', '删除', '复制', '分享']:
                label.hide()
                label.setVisible(False)
                print(f"隐藏图标标签: {label.toolTip()}")
            # 检查文本内容
            elif hasattr(label, 'text') and label.text():
                text = label.text()
                if any(icon in text for icon in ['⭐', '📌', '✏️', '🗑️', '📋', '📤']):
                    label.hide()
                    label.setVisible(False)
                    print(f"隐藏图标文本: {text}")
        
        # 3. 隐藏可能包含图标的QWidget
        widgets = self.findChildren(QWidget)
        for widget in widgets:
            if hasattr(widget, 'toolTip') and widget.toolTip() in ['收藏', '置顶', '编辑', '删除', '复制', '分享']:
                widget.hide()
                widget.setVisible(False)
                print(f"隐藏图标控件: {widget.__class__.__name__} - {widget.toolTip()}")
            
            # 检查objectName
            if hasattr(widget, 'objectName') and widget.objectName():
                obj_name = widget.objectName().lower()
                if any(name in obj_name for name in ['favorite', 'pin', 'edit', 'delete', 'copy', 'share']):
                    widget.hide()
                    widget.setVisible(False)
                    print(f"隐藏图标控件: {widget.__class__.__name__} - objectName: {widget.objectName()}")
        
        # 4. 特别处理：查找右上角区域的所有小控件
        self._hide_corner_icons()
        
        print("已应用全面的图标隐藏样式")
    
    def _hide_corner_icons(self):
        """特别处理右上角的图标"""
        # 查找卡片的几何信息
        card_rect = self.geometry()
        card_width = card_rect.width()
        card_height = card_rect.height()
        
        # 查找可能在右上角的小控件
        all_widgets = self.findChildren(QWidget)
        for widget in all_widgets:
            try:
                # 获取控件的几何信息
                widget_rect = widget.geometry()
                widget_x = widget_rect.x()
                widget_y = widget_rect.y()
                widget_width = widget_rect.width()
                widget_height = widget_rect.height()
                
                # 判断是否在右上角区域（右边1/3，上边1/3）
                if (widget_x > card_width * 0.66 and 
                    widget_y < card_height * 0.33 and 
                    widget_width < 50 and widget_height < 50):  # 小控件
                    
                    # 检查是否可能是图标
                    is_icon = False
                    
                    # 检查样式表是否包含图标
                    if hasattr(widget, 'styleSheet') and widget.styleSheet():
                        style = widget.styleSheet()
                        if any(keyword in style.lower() for keyword in ['star', 'pin', 'favorite', 'bookmark', 'icon', 'svg']):
                            is_icon = True
                    
                    # 检查是否是小尺寸的控件（可能是图标）
                    if widget_width <= 30 and widget_height <= 30:
                        is_icon = True
                    
                    # 如果判断为图标，则隐藏
                    if is_icon:
                        widget.hide()
                        widget.setVisible(False)
                        print(f"隐藏右上角图标: {widget.__class__.__name__} at ({widget_x}, {widget_y}) size ({widget_width}x{widget_height})")
                        
            except Exception as e:
                # 忽略获取几何信息失败的情况
                pass
    
    def _connect_thumbnail_signals(self):
        """查找并连接所有ThumbnailCarousel组件的信号"""
        # 递归查找所有ThumbnailCarousel组件
        def find_thumbnail_carousels(widget):
            carousels = []
            if isinstance(widget, ThumbnailCarousel):
                carousels.append(widget)
            for child in widget.findChildren(ThumbnailCarousel):
                carousels.append(child)
            return carousels
        
        # 延迟连接，确保UI已经构建完成
        QTimer.singleShot(0, self._delayed_connect_signals)
    
    def _delayed_connect_signals(self):
        """延迟连接信号，确保UI构建完成"""
        carousels = self.findChildren(ThumbnailCarousel)
        for carousel in carousels:
            try:
                # 断开可能已存在的连接，避免重复
                carousel.thumbnailClicked.disconnect()
            except (TypeError, RuntimeError):
                # TypeError: 信号未连接时会抛出此异常
                # RuntimeError: 对象已被删除时会抛出此异常
                pass
            # 连接到我们的处理方法
            carousel.thumbnailClicked.connect(self.on_thumbnail_clicked)
            print(f"回收站卡片已连接缩略图信号: {carousel}")
    
    def on_thumbnail_clicked(self, index, media_files):
        """处理缩略图点击事件"""
        if not media_files:
            return
            
        media_path = media_files[index]
        file_path = Path(media_path)
        
        print(f"回收站缩略图被点击: {file_path}")
        
        if file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
            # 视频文件，使用系统默认播放器打开
            print(f"尝试使用系统播放器打开视频: {file_path}")
            success = open_video_with_system_player(file_path)
            if not success:
                print(f"打开视频失败: {file_path}")
                QMessageBox.warning(self.window(), "视频播放错误", "无法打开视频文件，请确认您的系统已安装视频播放器。")
            else:
                print(f"成功打开视频: {file_path}")
        else:
            # 图片文件，打开图片查看对话框
            print(f"打开图片查看对话框: {file_path}")
            dialog = ImageViewerDialog(media_files, index, self.window())
            dialog.exec()
        
    def create_top_section(self):
        """创建顶部区域 - 分类和标题在一行"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(8)
        
        # 创建分类和标题的组合标签
        category = getattr(self.prompt, 'category', '通用')
        title = self.prompt.title
        
        combined_label = QLabel(f"{category} | {title}")
        combined_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        combined_label.setWordWrap(True)
        combined_label.setMaximumHeight(40)  # 最多显示2行标题
        
        layout.addWidget(combined_label, 1)  # 1表示伸展因子
        
        # 不添加操作按钮
        
        return widget
        
    def create_title_section(self):
        """重写标题区域方法，返回空组件，避免重复显示标题"""
        empty_widget = QWidget()
        StyleManager.apply_transparent_background(empty_widget)
        # 不设置高度为0，让它自然适应布局
        return empty_widget
    
    def create_action_buttons(self):
        """重写方法，不创建操作按钮"""
        return None
    
    def setup_context_menu(self):
        """设置右键菜单 - 只包含恢复和永久删除选项"""
        # 设置菜单样式
        self.context_menu = QMenu(self)
        self.context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
                margin: 2px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
                min-width: 120px;
            }
            QMenu::item:selected {
                background-color: #3B82F6;
                color: white;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 创建恢复操作
        restore_action = self.context_menu.addAction("恢复提示词")
        restore_action.triggered.connect(self.on_restore_action)
        
        # 创建永久删除操作
        delete_action = self.context_menu.addAction("永久删除")
        delete_action.triggered.connect(self.on_permanent_delete_action)
    
    def on_restore_action(self):
        """处理恢复操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area:
            content_area.restore_prompt(self.prompt.id)
    
    def on_permanent_delete_action(self):
        """处理永久删除操作"""
        # 取消选中状态
        self.clear_selection()
        
        # 查找父级内容区域
        content_area = self.get_content_area()
        if content_area:
            content_area.permanently_delete_prompt(self.prompt.id)
            
    def mousePressEvent(self, event):
        """重写鼠标点击事件，处理正确的选中状态管理"""
        super(PromptCardWidget, self).mousePressEvent(event)  # 调用基类的方法，而不是直接父类
        
        # 发射点击信号
        self.clicked.emit(True)
        
        # 查找父级TrashPage
        trash_page = None
        parent = self.parent()
        while parent:
            if hasattr(parent, '__class__') and parent.__class__.__name__ == 'TrashPage':
                trash_page = parent
                break
            parent = parent.parent()
        
        if trash_page and hasattr(trash_page, 'trash_selected_cards'):
            # 如果按住Ctrl键，则切换选中状态（多选模式）
            if event.modifiers() & Qt.ControlModifier:
                self.is_selected = not self.is_selected
                self.set_selected(self.is_selected)
                
                # 更新选中列表
                if self.is_selected and self not in trash_page.trash_selected_cards:
                    trash_page.trash_selected_cards.append(self)
                elif not self.is_selected and self in trash_page.trash_selected_cards:
                    trash_page.trash_selected_cards.remove(self)
            # 普通点击：正确的单选逻辑
            else:
                # 检查是否在全选状态下
                is_all_selected = len(trash_page.trash_selected_cards) > 1
                
                if self.is_selected and not is_all_selected:
                    # 如果当前卡片已选中且不是全选状态，则取消选中
                    self.is_selected = False
                    self.set_selected(False)
                    
                    # 从选中列表中移除
                    if self in trash_page.trash_selected_cards:
                        trash_page.trash_selected_cards.remove(self)
                else:
                    # 如果当前卡片未选中，或者是全选状态下的点击，则选中当前卡片并取消其他所有卡片
                    # 普通点击始终是单选逻辑：清除所有其他卡片，选中当前卡片
                    # 清除所有其他卡片的选中状态
                    for card in trash_page.trash_selected_cards[:]:  # 使用切片创建副本
                        if card != self:
                            card.is_selected = False
                            card.update()
                    trash_page.trash_selected_cards.clear()
                    trash_page.trash_selected_cards.append(self)
                    
                    # 选中当前卡片
                    self.is_selected = True
                    self.set_selected(True)
            
            # 更新状态栏
            parent_window = trash_page.get_parent_window()
            if parent_window and hasattr(parent_window, 'status_bar'):
                if trash_page.trash_selected_cards:
                    parent_window.status_bar.set_info(f"已选择 {len(trash_page.trash_selected_cards)} 个提示词")
                else:
                    parent_window.status_bar.set_info("")

class PromptAssistantRedesigned(QMainWindow):
    """重构版主窗口"""

    def __init__(self) -> None:
        super().__init__()
        # 初始化设置状态
        self.debug_mode: bool = False
        self.performance_mode: bool = False
        self.settings: QSettings = QSettings("PromptAssistant", "AppSettings")
        
        # 初始化应用工厂和依赖注入
        from core.app_factory import get_app_factory
        self.app_factory = get_app_factory()
        
        # 获取所有服务实例
        self.model = self.app_factory.get_model()
        self.prompt_service = self.app_factory.get_prompt_service()
        self.tag_service = self.app_factory.get_tag_service()
        self.category_service = self.app_factory.get_category_service()
        self.color_service = self.app_factory.get_color_service()
        self.app_controller = self.app_factory.get_app_controller()
        
        self.init_ui()
    
    def load_svg_icon(self, svg_file_path: str, size: int = 24) -> QIcon:
        """加载SVG图标并转换为QIcon"""
        try:
            from pathlib import Path
            icon_path = Path(svg_file_path)
            if icon_path.exists():
                renderer = QSvgRenderer(str(icon_path))
                pixmap = QPixmap(size, size)
                pixmap.fill(Qt.transparent)
                painter = QPainter(pixmap)
                renderer.render(painter)
                painter.end()
                return QIcon(pixmap)
            else:
                print(f"SVG图标文件不存在: {svg_file_path}")
                return QIcon()
        except Exception as e:
            print(f"加载SVG图标失败: {e}")
            return QIcon()

    # =============================================================================
    # 私有样式方法 - 抽取重复的样式代码，使用design_system常量
    # =============================================================================
    
    def _get_tooltip_style(self) -> str:
        """获取统一的工具提示样式"""
        return f"""
            QToolTip {{
                background-color: {COLORS['gray_700']};
                color: {COLORS['white']};
                border: 1px solid {COLORS['gray_600']};
                border-radius: {RADIUS['md']};
                padding: 4px;
                font-size: 11px;
            }}
        """
    
    def _get_main_window_style(self) -> str:
        """获取主窗口样式，使用design_system常量"""
        return f"""
            QMainWindow {{
                background-color: {COLORS['white']};
                border: 1px solid {COLORS['gray_300']};
                border-radius: 0px;
            }}
            {self._get_tooltip_style()}
        """
    
    def _apply_transparent_background(self, widget) -> None:
        """为组件应用透明背景 - 使用StyleManager统一管理"""
        StyleManager.apply_transparent_background(widget)
    
    def _create_icon_button(self, size: Tuple[int, int] = (32, 32), icon_path: Optional[str] = None, 
                           tooltip: str = "", hover_color: Optional[str] = None, 
                           icon_size: int = 20) -> QPushButton:
        """创建图标按钮的通用方法
        
        Args:
            size: 按钮尺寸 (width, height)
            icon_path: SVG图标路径
            tooltip: 工具提示文本
            hover_color: 悬停背景色（使用design_system常量）
            icon_size: 图标尺寸
        """
        btn = QPushButton()
        btn.setFixedSize(*size)
        btn.setCursor(Qt.PointingHandCursor)
        btn.setToolTip(tooltip)
        
        # 设置图标
        if icon_path:
            icon = self.load_svg_icon(icon_path, icon_size)
            btn.setIcon(icon)
            btn.setIconSize(QSize(icon_size, icon_size))
        
        # 设置样式
        hover_bg = hover_color or COLORS['gray_100']
        btn.setStyleSheet(f"""
            QPushButton {{
                background: transparent;
                border: none;
                border-radius: {RADIUS['md']};
            }}
            QPushButton:hover {{
                background-color: {hover_bg};
            }}
            {self._get_tooltip_style()}
        """)
        
        return btn
    
    def _create_primary_button(self, text: str, enabled: bool = True) -> QPushButton:
        """创建主要操作按钮"""
        btn = QPushButton(text)
        btn.setEnabled(enabled)
        
        disabled_bg = COLORS['gray_300'] if not enabled else COLORS['primary']
        disabled_color = COLORS['gray_500'] if not enabled else COLORS['white']
        
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['primary']};
                color: {COLORS['white']};
                border: none;
                border-radius: {RADIUS['md']};
                padding: 8px 16px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['primary_hover']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['primary_active']};
            }}
            QPushButton:disabled {{
                background-color: {disabled_bg};
                color: {disabled_color};
            }}
        """)
        
        return btn
    
    def _create_secondary_button(self, text: str, color: Optional[str] = None) -> QPushButton:
        """创建次要操作按钮"""
        btn = QPushButton(text)
        bg_color = color or COLORS['success']
        
        # 根据主色调计算悬停色，使用design_system常量
        hover_colors = {
            COLORS['success']: COLORS['success_hover'],
            COLORS['warning']: COLORS['warning_hover'], 
            COLORS['error']: COLORS['error_hover']
        }
        hover_color = hover_colors.get(bg_color, COLORS['success_hover'])
        
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                color: {COLORS['white']};
                border: none;
                border-radius: {RADIUS['md']};
                padding: 8px 16px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {hover_color};
            }}
            QPushButton:disabled {{
                background-color: {COLORS['gray_300']};
                color: {COLORS['gray_500']};
            }}
        """)
        
        return btn
    
    def _create_outline_button(self, text: str) -> QPushButton:
        """创建轮廓按钮"""
        btn = QPushButton(text)
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {COLORS['white']};
                color: {COLORS['gray_600']};
                border: 1px solid {COLORS['gray_300']};
                border-radius: {RADIUS['md']};
                padding: 8px 16px;
                font-size: 14px;
            }}
            QPushButton:hover {{
                background-color: {COLORS['gray_50']};
            }}
            QPushButton:pressed {{
                background-color: {COLORS['gray_100']};
            }}
        """)
        
        return btn
    

    def init_ui(self) -> None:
        """初始化用户界面"""
        # model已经通过AppFactory注入，无需再次创建
        self.setup_ui()
        self.load_data()
        # 加载初始设置（调试模式、性能模式等）
        self.load_initial_settings()
        
    def closeEvent(self, event: QCloseEvent) -> None:
        """关闭事件，同时关闭所有打开的对话框"""
        # 关闭所有活动对话框
        if hasattr(self, 'content_area') and hasattr(self.content_area, 'active_dialogs'):
            for dialog in self.content_area.active_dialogs[:]:  # 使用副本进行遍历
                dialog.close()
        
        # 调用父类方法
        super().closeEvent(event)
        
    def setup_ui(self):
        # 设置窗口属性
        self.setWindowTitle("Prompt收藏助手")
        self.setFixedSize(460, 800)
        self.setWindowFlags(Qt.FramelessWindowHint)  # 无标题栏
        
        # 设置窗口样式 - 使用design_system常量
        self.setStyleSheet(self._get_main_window_style())
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 (水平布局)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.nav_bar = NavigationBar(self)
        main_layout.addWidget(self.nav_bar)
        
        # 右侧主区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 自定义标题栏
        self.title_bar = CustomTitleBar(self)
        right_layout.addWidget(self.title_bar)
        
        # 内容区域
        self.content_area = ContentArea(self)
        right_layout.addWidget(self.content_area)
        
        # 状态栏
        self.status_bar = StatusBar(self)
        right_layout.addWidget(self.status_bar)
        
        main_layout.addWidget(right_widget)
        
        # 居中显示窗口
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def switch_page(self, page_key):
        """切换页面"""
        self.content_area.switch_to_page(page_key)
        
        # 更新标题栏标题
        titles = {
            "home": "我的提示词",
            "trash": "回收站",
            "settings": "设置",
            "help": "帮助"
        }
        
        if page_key in titles:
            self.title_bar.set_title(titles[page_key])
            self.status_bar.set_status(f"已切换到{titles[page_key]}")
            
    def load_data(self):
        """加载数据"""
        try:
            # 只获取未删除的提示词
            prompts = self.model.get_all_prompts(include_deleted=False)
            # 将数据传递给 ContentArea 并应用筛选
            self.content_area.all_prompts = prompts
            # 填充分类筛选按钮
            self.content_area.populate_category_filters()
            # 填充标签筛选按钮
            self.content_area.populate_tag_filters()
            self.content_area.apply_filters_and_update_views()
            self.status_bar.set_status("数据加载完成")
        except Exception as e:
            print(f"加载数据时出错: {e}")
            self.status_bar.set_status(f"加载失败: {str(e)}")
            
    def update_prompt_list(self, prompts):
        """更新提示词列表显示"""
        if hasattr(self.content_area, 'prompt_list'):
            # 更新列表视图
            self.content_area.prompt_list.clear()
            for prompt in prompts:
                item_text = f"{prompt.title}\n{prompt.content[:50]}..." if len(prompt.content) > 50 else f"{prompt.title}\n{prompt.content}"
                self.content_area.prompt_list.addItem(item_text)
            
            # 更新卡片视图
            try:
                if hasattr(self.content_area, 'update_card_view') and hasattr(self.content_area, 'cards_list_layout'):
                    self.content_area.update_card_view(prompts)
            except Exception as e:
                import traceback
                print(f"更新卡片视图时出错: {e}")
                traceback.print_exc()
            
    def refresh_prompt_list(self):
        """刷新提示词列表"""
        self.load_data()
        self.status_bar.set_status("提示词列表已刷新")
    
    def on_settings_changed(self, setting_name, value):
        """处理设置变更"""
        try:
            if setting_name == "database_path":
                self.apply_database_path_change(value)
            elif setting_name == "debug_mode":
                self.apply_debug_mode_change(value)
            elif setting_name == "performance_mode":
                self.apply_performance_mode_change(value)
            
            # 更新状态栏
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status(f"设置已更新: {setting_name}")
                
        except Exception as e:
            print(f"应用设置变更时出错: {e}")
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status(f"设置更新失败: {str(e)}")
        
    def apply_database_path_change(self, db_path):
        """应用数据库路径变更"""
        # 这里可以实现数据库路径切换逻辑
        print(f"数据库路径已更改到: {db_path}")
    
    def apply_debug_mode_change(self, enabled):
        """应用调试模式变更"""
        self.debug_mode = enabled
        
        if enabled:
            print("🐛 调试模式已启用")
            # 启用详细日志输出
            import logging
            logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
            
            # 在状态栏显示调试指示器
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("🐛 调试模式已启用", 3000)
            
            # 启用错误详情显示
            import sys
            sys.tracebacklimit = 100  # 显示完整的错误堆栈
            
            print("调试功能已激活:")
            print("- 详细日志输出")
            print("- 完整错误堆栈跟踪") 
            print("- 状态栏调试指示")
            
        else:
            print("调试模式已禁用")
            # 恢复正常日志级别
            import logging
            logging.basicConfig(level=logging.WARNING)
            
            # 移除状态栏调试指示
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("调试模式已禁用", 2000)
            
            # 恢复默认错误显示
            import sys
            sys.tracebacklimit = 10
    
    def apply_performance_mode_change(self, enabled):
        """应用性能优化模式变更"""
        self.performance_mode = enabled
        
        if enabled:
            print("⚡ 性能优化模式已启用")
            
            # 禁用动画效果
            if hasattr(self, 'content_area'):
                # 禁用内容区域的动画
                for widget in self.content_area.findChildren(QWidget):
                    if hasattr(widget, 'setGraphicsEffect'):
                        widget.setGraphicsEffect(None)
            
            # 设置性能优化的样式（减少阴影和特效）
            performance_style = """
                QWidget {
                    /* 禁用所有动画效果 */
                }
                QPushButton {
                    /* Qt不支持transition属性，已移除 */
                }
                QFrame {
                    /* 简化边框效果 */
                }
            """
            
            # 应用性能优化样式
            current_style = self.styleSheet()
            self.setStyleSheet(current_style + performance_style)
            
            # 在状态栏显示性能模式指示器
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("⚡ 性能优化模式已启用", 3000)
            
            print("性能优化功能已激活:")
            print("- 禁用UI动画效果")
            print("- 简化视觉特效")
            print("- 优化界面渲染")
            
        else:
            print("性能优化模式已禁用")
            
            # 重新加载原始样式（移除性能优化样式）
            original_style = """
            QMainWindow {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 0px;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
            """
            self.setStyleSheet(original_style)
            
            # 移除状态栏性能指示
            if hasattr(self, 'status_bar'):
                self.status_bar.set_status("性能优化模式已禁用", 2000)
    
    def load_initial_settings(self):
        """加载初始设置"""
        try:
            # 加载调试模式设置
            debug_mode = self.settings.value("debug_mode", False, type=bool)
            self.apply_debug_mode_change(debug_mode)
            
            # 加载性能模式设置
            performance_mode = self.settings.value("performance_mode", False, type=bool)
            self.apply_performance_mode_change(performance_mode)
            
            if debug_mode or performance_mode:
                modes = []
                if debug_mode:
                    modes.append("调试模式")
                if performance_mode:
                    modes.append("性能优化模式")
                print(f"已加载设置: {', '.join(modes)}")
                
        except Exception as e:
            print(f"加载初始设置时出错: {e}")












def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    # 初始化应用工厂和依赖注入
    from core.app_factory import initialize_app
    app_factory = initialize_app()
    
    # 创建主窗口
    window = PromptAssistantRedesigned()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main() 