#!/usr/bin/env python3
"""
为标签筛选功能创建完整的UI界面
按照用户指令逐步实现所有功能
"""

def implement_tag_filter_ui():
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("开始实现标签筛选UI界面...")
    
    # 第一步：在 create_home_page 方法中添加标签筛选栏容器
    print("第一步：创建标签筛选栏容器")
    
    # 查找分类筛选栏的位置，在其后添加标签筛选栏
    category_filter_section = """        layout.addWidget(self.category_filter_bar)"""
    
    if category_filter_section in content:
        tag_filter_section = """        layout.addWidget(self.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.tag_filter_area = QScrollArea()
        self.tag_filter_area.setFixedHeight(50)
        self.tag_filter_area.setWidgetResizable(True)
        self.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tag_filter_area.setStyleSheet(\"\"\"
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
        \"\"\")
        self.tag_filter_area.hide()
        
        # 标签筛选栏内容容器
        tag_filter_widget = QWidget()
        self.tag_filter_layout = QHBoxLayout(tag_filter_widget)
        self.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.tag_filter_layout.setSpacing(6)
        self.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.tag_filter_area)"""
        
        content = content.replace(category_filter_section, tag_filter_section)
        print("✅ 标签筛选栏容器创建完成")
    else:
        print("❌ 未找到分类筛选栏位置")
    
    # 第二步：在 ContentArea 初始化中添加标签按钮引用管理
    print("第二步：添加标签按钮引用管理")
    
    # 查找 category_buttons 的初始化位置
    category_buttons_init = "self.category_buttons = {}  # 存储分类按钮的引用"
    if category_buttons_init in content:
        tag_buttons_init = """self.category_buttons = {}  # 存储分类按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用"""
        content = content.replace(category_buttons_init, tag_buttons_init)
        print("✅ 标签按钮引用管理添加完成")
    else:
        print("❌ 未找到分类按钮初始化位置")
    
    # 第三步：创建 populate_tag_filters 方法
    print("第三步：创建动态填充标签按钮的方法")
    
    # 在 update_category_button_states 方法后添加新方法
    lines = content.split('\n')
    method_inserted = False
    
    for i, line in enumerate(lines):
        if "def update_category_button_states(self):" in line:
            # 找到这个方法的结束位置
            method_start = i
            indent_level = len(line) - len(line.lstrip())
            method_end = -1
            
            for j in range(method_start + 1, len(lines)):
                current_line = lines[j]
                if current_line.strip() and not current_line.startswith(' ' * (indent_level + 4)):
                    method_end = j
                    break
            
            if method_end != -1:
                # 插入新方法
                new_methods = [
                    "",
                    "    def populate_tag_filters(self):",
                    '        """动态填充标签筛选按钮"""',
                    "        ",
                    "        # 清除现有按钮和引用",
                    "        self.tag_buttons.clear()",
                    "        for i in reversed(range(self.tag_filter_layout.count())):",
                    "            child = self.tag_filter_layout.itemAt(i).widget()",
                    "            if child:",
                    "                child.setParent(None)",
                    "        ",
                    "        # 从所有提示词中提取唯一标签",
                    "        all_tags = set()",
                    "        for prompt in self.all_prompts:",
                    "            if hasattr(prompt, 'tags') and prompt.tags:",
                    "                for tag in prompt.tags:",
                    "                    if tag and tag.strip():",
                    "                        all_tags.add(tag.strip())",
                    "        ",
                    "        # 转换为排序列表",
                    "        unique_tags = sorted(list(all_tags))",
                    "        ",
                    "        # 添加\"全部\"按钮",
                    '        all_button = QPushButton("全部")',
                    '        all_button.clicked.connect(lambda: self.on_tag_button_clicked("全部"))',
                    '        self.tag_buttons["全部"] = all_button',
                    "        self.tag_filter_layout.addWidget(all_button)",
                    "        ",
                    "        # 添加各标签按钮",
                    "        for tag in unique_tags:",
                    "            button = QPushButton(tag)",
                    "            button.clicked.connect(lambda checked, t=tag: self.on_tag_button_clicked(t))",
                    "            self.tag_buttons[tag] = button",
                    "            self.tag_filter_layout.addWidget(button)",
                    "        ",
                    "        # 添加弹性空间",
                    "        self.tag_filter_layout.addStretch()",
                    "        ",
                    "        # 更新按钮状态",
                    "        self.update_tag_button_states()",
                    "",
                    "    def on_tag_button_clicked(self, tag_name):",
                    '        """标签按钮点击处理器"""',
                    "        # 更新标签筛选状态",
                    "        self.active_filters['tag'] = tag_name",
                    "        ",
                    "        # 更新按钮状态",
                    "        self.update_tag_button_states()",
                    "        ",
                    "        # 应用筛选并更新视图",
                    "        self.apply_filters_and_update_views()",
                    "",
                    "    def update_tag_button_states(self):",
                    '        """更新标签按钮的激活状态"""',
                    "        current_tag = self.active_filters.get('tag', '全部')",
                    "        ",
                    "        for tag_name, button in self.tag_buttons.items():",
                    "            if tag_name == current_tag:",
                    "                # 激活状态样式（蓝色主题）",
                    "                button.setStyleSheet('''",
                    "                    QPushButton {",
                    "                        background-color: #0EA5E9;",
                    "                        color: white;",
                    "                        border: none;",
                    "                        border-radius: 4px;",
                    "                        padding: 6px 12px;",
                    "                        font-size: 12px;",
                    "                        margin: 2px;",
                    "                    }",
                    "                    QPushButton:hover {",
                    "                        background-color: #0284C7;",
                    "                    }",
                    "                ''')",
                    "            else:",
                    "                # 默认状态样式",
                    "                button.setStyleSheet('''",
                    "                    QPushButton {",
                    "                        background-color: #F0F9FF;",
                    "                        color: #0C4A6E;",
                    "                        border: 1px solid #BAE6FD;",
                    "                        border-radius: 4px;",
                    "                        padding: 6px 12px;",
                    "                        font-size: 12px;",
                    "                        margin: 2px;",
                    "                    }",
                    "                    QPushButton:hover {",
                    "                        background-color: #E0F2FE;",
                    "                        border-color: #7DD3FC;",
                    "                    }",
                    "                ''')"
                ]
                
                lines[method_end:method_end] = new_methods
                method_inserted = True
                break
    
    if method_inserted:
        content = '\n'.join(lines)
        print("✅ 标签筛选方法创建完成")
    else:
        print("❌ 未找到合适位置插入标签筛选方法")
    
    # 第四步：修改 active_filters 初始化，添加标签筛选状态
    print("第四步：添加标签筛选状态管理")
    
    old_active_filters = "self.active_filters = {'favorite': False, 'category': '全部'}  # 跟踪当前激活的筛选器"
    new_active_filters = "self.active_filters = {'favorite': False, 'category': '全部', 'tag': '全部'}  # 跟踪当前激活的筛选器"
    
    if old_active_filters in content:
        content = content.replace(old_active_filters, new_active_filters)
        print("✅ 标签筛选状态管理添加完成")
    else:
        print("❌ 未找到筛选状态初始化位置")
    
    # 第五步：修改 apply_filters_and_update_views 方法，添加标签筛选逻辑
    print("第五步：添加标签筛选逻辑")
    
    old_filter_logic = """        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]"""
    
    new_filter_logic = """        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]"""
    
    if old_filter_logic in content:
        content = content.replace(old_filter_logic, new_filter_logic)
        print("✅ 标签筛选逻辑添加完成")
    else:
        print("❌ 未找到筛选逻辑位置")
    
    # 第六步：修改 on_filter_button_clicked 方法，添加标签筛选的显示/隐藏逻辑
    print("第六步：添加标签筛选显示/隐藏逻辑")
    
    # 查找并修改标签筛选的处理逻辑
    old_tag_logic = '''            elif filter_key == "tag":
                self.toggle_tag_filter()'''
    
    new_tag_logic = '''            elif filter_key == "tag":
                if self.tag_filter_area.isVisible():
                    # 隐藏时重置为"全部"
                    self.active_filters['tag'] = '全部'
                    self.tag_filter_area.hide()
                    self.apply_filters_and_update_views()
                else:
                    # 显示标签筛选栏前，隐藏分类筛选栏并重置
                    if self.category_filter_bar.isVisible():
                        self.active_filters['category'] = '全部'
                        self.category_filter_bar.hide()
                    
                    self.populate_tag_filters()
                    self.tag_filter_area.show()'''
    
    if old_tag_logic in content:
        content = content.replace(old_tag_logic, new_tag_logic)
        print("✅ 标签筛选显示/隐藏逻辑添加完成")
    else:
        print("❌ 未找到标签筛选处理逻辑位置")
    
    # 第七步：修改分类筛选的显示逻辑，确保互斥
    print("第七步：修改分类筛选逻辑，确保与标签筛选互斥")
    
    old_category_logic = '''                else:
                    self.populate_category_filters()
                    self.category_filter_bar.show()'''
    
    new_category_logic = '''                else:
                    # 显示分类筛选栏前，隐藏标签筛选栏并重置
                    if self.tag_filter_area.isVisible():
                        self.active_filters['tag'] = '全部'
                        self.tag_filter_area.hide()
                    
                    self.populate_category_filters()
                    self.category_filter_bar.show()'''
    
    if old_category_logic in content:
        content = content.replace(old_category_logic, new_category_logic)
        print("✅ 分类筛选互斥逻辑添加完成")
    else:
        print("❌ 未找到分类筛选逻辑位置")
    
    # 写回文件
    with open('main.py', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("\n🎉 标签筛选UI界面实现完成！")
    print("已完成的功能：")
    print("1. ✅ 创建了标签筛选栏容器 (QScrollArea)")
    print("2. ✅ 添加了标签按钮引用管理 (tag_buttons)")
    print("3. ✅ 实现了动态填充标签按钮的方法 (populate_tag_filters)")
    print("4. ✅ 添加了标签按钮点击处理器 (on_tag_button_clicked)")
    print("5. ✅ 实现了标签按钮状态更新 (update_tag_button_states)")
    print("6. ✅ 添加了标签筛选状态管理 (active_filters['tag'])")
    print("7. ✅ 实现了标签筛选逻辑 (apply_filters_and_update_views)")
    print("8. ✅ 添加了标签筛选显示/隐藏逻辑 (on_filter_button_clicked)")
    print("9. ✅ 实现了与分类筛选的互斥显示")

def update_load_data_method():
    """更新主窗口的 load_data 方法，添加标签筛选的调用"""
    with open('main.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n第八步：更新 load_data 方法")
    
    # 查找 load_data 方法中调用 populate_category_filters 的位置
    old_load_data_call = "self.content_area.populate_category_filters()"
    new_load_data_call = """self.content_area.populate_category_filters()
        self.content_area.populate_tag_filters()"""
    
    if old_load_data_call in content:
        content = content.replace(old_load_data_call, new_load_data_call)
        print("✅ load_data 方法更新完成")
        
        with open('main.py', 'w', encoding='utf-8') as f:
            f.write(content)
    else:
        print("❌ 未找到 load_data 方法中的调用位置")

if __name__ == '__main__':
    implement_tag_filter_ui()
    update_load_data_method()