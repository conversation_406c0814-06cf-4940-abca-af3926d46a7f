# PySide6卡片边框优化

## Core Features

- 统一卡片边框样式 ✅

- 保持现有布局结构 ✅

- 中性色边框设计 ✅

- 响应式网格兼容 ✅

## Tech Stack

{
  "Desktop": "PySide6 + Qt样式表",
  "Component": "PromptCardWidget自定义组件",
  "Styling": "QSS样式系统"
}

## Design

将原有的蓝色边框改为中性色边框，确保所有卡片都有清晰可见的视觉边界 ✅

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[X] 定位main.py中的PromptCardWidget组件定义

[X] 修改卡片样式表，添加1像素中性色边框

[X] 验证边框在所有四个侧面的显示效果

[X] 测试卡片间距和布局保持不变

[X] 运行应用验证边框效果和响应式布局

## 实施细节

1. 将边框从2px改为1px，颜色从#d1d5db改为#e0e0e0
2. 将边框圆角从12px减小到6px，使设计更加现代简洁
3. 移除了卡片阴影效果，采用扁平化设计
4. 设置卡片间距为16px，创建更清晰的视觉层次
5. 修复了卡片鼠标悬停时的背景显示问题
   - 所有子部件都使用透明背景
   - 悬停状态下卡片边框颜色变为#9ca3af
6. 保持了响应式布局和网格结构
7. 采用外部QFrame容器方案解决卡片边框不显示问题
   - 将每个PromptCardWidget放入一个QFrame容器中
   - 在容器上应用边框样式，而非卡片本身
   - 添加QFrame:hover样式实现鼠标悬停效果

## 解决方案：边框不显示问题

在尝试多种方法后，我们发现直接在PromptCardWidget上设置边框无法正常显示。采用以下解决方案：

1. 使用额外的QFrame作为卡片容器
2. 在QFrame上设置边框和圆角样式
3. 在PromptCardWidget上移除边框样式
4. 使用CSS hover选择器实现鼠标悬停效果
5. 容器和卡片之间不添加额外间距，确保无缝对接

这种容器嵌套方式确保了边框可以正确显示，并且不会影响原有的卡片内容布局。
