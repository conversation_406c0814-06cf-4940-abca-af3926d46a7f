#!/usr/bin/env python3
import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QPushButton, QLabel, QFrame, 
                               QStackedWidget, QListWidget, QTextEdit, 
                               QLineEdit, QScrollArea, QMessageBox, QGraphicsDropShadowEffect,
                               QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QSize, QPoint
from PySide6.QtGui import QIcon, QPalette, QColor, QFont, QPixmap, QPainter, QPen
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QSvgWidget
import traceback

try:
    from model import PromptModel, Prompt
    from create_prompt_dialog import CreatePromptDialog
except ImportError:
    print("警告：无法导入 model 或 create_prompt_dialog 模块，将使用模拟数据")

class ContentArea(QWidget):
    """内容显示区域"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_view = "card"  # 默认为卡片视图
        self.all_prompts = []  # 存储从数据库加载的、未经筛选的完整提示词列表
        self.active_filters = {'favorite': False, 'category': '全部', 'tags': set()}  # 跟踪当前激活的筛选器，tags改为set支持多选
        self.category_buttons = {}  # 存储分类按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用
        
        # 激活状态的实心星形图标
        self.favorite_active_svg = '<svg width="16" height="16" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg"><path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>'
        self.tag_buttons = {}  # 存储标签按钮的引用
        self.setFixedSize(400, 718)  # 调整高度从700px到718px，补偿状态栏减少的18px
        self.init_ui()
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet("""
            QWidget {
                background-color: white;
            }
        """)
        
        # 使用垂直布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # 添加一个标签，表示这是一个测试页面
        label = QLabel("这是一个测试页面")
        label.setAlignment(Qt.AlignCenter)
        layout.addWidget(label)
        
        # 添加一些测试按钮
        self.tag_buttons = {}
        
        # 创建"全部"按钮
        all_button = QPushButton("全部")
        all_button.setStyleSheet(self.get_filter_button_style())
        self.tag_buttons["全部"] = all_button
        layout.addWidget(all_button)
        
        # 创建一些测试标签按钮
        for tag in ["测试标签1", "测试标签2", "测试标签3"]:
            tag_button = QPushButton(tag)
            tag_button.setStyleSheet(self.get_filter_button_style())
            self.tag_buttons[tag] = tag_button
            layout.addWidget(tag_button)
        
        # 更新按钮状态
        self.update_tag_button_states()
    def get_active_filter_button_style(self):
        """获取激活状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #0EA5E9;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0284C7;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''
    def get_filter_button_style(self):
        """获取默认状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #F0F9FF;
                color: #0C4A6E;
                border: 1px solid #BAE6FD;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #E0F2FE;
                border-color: #7DD3FC;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''
    def update_tag_button_states(self):
        """更新标签按钮的激活状态（支持多选）"""
        selected_tags = self.active_filters.get('tags', set())
        
        # 更新所有标签按钮的状态
        for tag_name, button in self.tag_buttons.items():
            if tag_name == '全部':
                # "全部"按钮：当没有其他标签选中时激活
                if len(selected_tags) == 0:
                    button.setStyleSheet(self.get_active_filter_button_style())
                else:
                    button.setStyleSheet(self.get_filter_button_style())
            else:
                # 普通标签按钮：根据是否在选中集合中设置状态
                if tag_name in selected_tags:
                    button.setStyleSheet(self.get_active_filter_button_style())
                else:
                    button.setStyleSheet(self.get_filter_button_style())

# 简单的主函数，用于测试
def main():
    app = QApplication(sys.argv)
    window = QMainWindow()
    window.setWindowTitle("测试")
    window.setGeometry(100, 100, 800, 600)
    
    try:
        content_area = ContentArea(window)
        window.setCentralWidget(content_area)
        
        window.show()
        sys.exit(app.exec())
    except Exception as e:
        print(f"错误: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
