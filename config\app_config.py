#!/usr/bin/env python3
"""
应用程序主配置文件
包含全局常量和配置选项
"""
import os
from pathlib import Path

# 应用程序信息
APP_NAME = "Prompt Assistant"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "提示词收藏助手"

# 文件路径
BASE_DIR = Path(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
DB_PATH = BASE_DIR / "prompts.db"
RESOURCES_DIR = BASE_DIR / "icons"

# 窗口设置
WINDOW_WIDTH = 460
WINDOW_HEIGHT = 800
WINDOW_TITLE = f"{APP_NAME} - {APP_DESCRIPTION}"

# 数据库设置
DB_TYPE = "sqlite"  # 可选: sqlite, json

# 日志设置
LOG_LEVEL = "INFO"
LOG_FILE = BASE_DIR / "app.log" 