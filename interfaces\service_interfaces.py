#!/usr/bin/env python3
"""
服务层接口定义
为服务层提供抽象接口，实现依赖倒置原则
"""
from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from model import Prompt, PromptHistory


class IPromptService(ABC):
    """提示词服务接口"""
    
    @abstractmethod
    def get_all_prompts(self, use_cache: bool = True) -> List[Prompt]:
        """
        获取所有提示词
        
        Args:
            use_cache: 是否使用缓存
            
        Returns:
            提示词列表
        """
        pass
    
    @abstractmethod
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        """
        获取特定提示词
        
        Args:
            prompt_id: 提示词ID
            
        Returns:
            提示词对象，如果不存在则返回None
        """
        pass
    
    @abstractmethod
    def create_prompt(self, prompt: Prompt) -> int:
        """
        创建新的提示词
        
        Args:
            prompt: 提示词对象
            
        Returns:
            新创建的提示词ID
        """
        pass
    
    @abstractmethod
    def update_prompt(self, prompt: Prompt) -> bool:
        """
        更新提示词
        
        Args:
            prompt: 提示词对象
            
        Returns:
            是否更新成功
        """
        pass
    
    @abstractmethod
    def delete_prompt(self, prompt_id: int) -> bool:
        """
        删除提示词
        
        Args:
            prompt_id: 提示词ID
            
        Returns:
            是否删除成功
        """
        pass
    
    @abstractmethod
    def toggle_favorite(self, prompt_id: int) -> bool:
        """
        切换提示词的收藏状态
        
        Args:
            prompt_id: 提示词ID
            
        Returns:
            新的收藏状态
        """
        pass
    
    @abstractmethod
    def toggle_pin(self, prompt_id: int) -> bool:
        """
        切换提示词的置顶状态
        
        Args:
            prompt_id: 提示词ID
            
        Returns:
            新的置顶状态
        """
        pass
    
    @abstractmethod
    def get_favorite_prompts(self) -> List[Prompt]:
        """
        获取收藏的提示词列表
        
        Returns:
            收藏的提示词列表
        """
        pass
    
    @abstractmethod
    def search_prompts(self, keyword: str) -> List[Prompt]:
        """
        根据关键词搜索提示词
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的提示词列表
        """
        pass
    
    @abstractmethod
    def filter_prompts(self, filters: Dict[str, Any]) -> List[Prompt]:
        """
        根据筛选条件过滤提示词
        
        Args:
            filters: 筛选条件
            
        Returns:
            过滤后的提示词列表
        """
        pass
    
    @abstractmethod
    def sort_prompts(self, prompts: List[Prompt], sort_method: str = "updated_desc") -> List[Prompt]:
        """
        对提示词列表排序
        
        Args:
            prompts: 提示词列表
            sort_method: 排序方法
            
        Returns:
            排序后的提示词列表
        """
        pass
    
    @abstractmethod
    def get_prompt_history(self, prompt_id: int) -> List[PromptHistory]:
        """
        获取提示词的历史版本列表
        
        Args:
            prompt_id: 提示词ID
            
        Returns:
            历史版本列表
        """
        pass
    
    @abstractmethod
    def restore_prompt_from_history(self, history_id: int) -> bool:
        """
        从历史版本恢复提示词
        
        Args:
            history_id: 历史版本ID
            
        Returns:
            是否恢复成功
        """
        pass
    
    @abstractmethod
    def clear_all_prompts(self) -> bool:
        """
        清空所有提示词
        
        Returns:
            是否清空成功
        """
        pass


class ITagService(ABC):
    """标签服务接口"""
    
    @abstractmethod
    def get_all_tags(self) -> List[str]:
        """
        获取所有标签
        
        Returns:
            标签列表
        """
        pass
    
    @abstractmethod
    def get_tag_counts(self) -> Dict[str, int]:
        """
        获取标签使用次数
        
        Returns:
            标签使用次数字典
        """
        pass
    
    @abstractmethod
    def normalize_tags(self, tags: List[str]) -> List[str]:
        """
        标准化标签
        
        Args:
            tags: 原始标签列表
            
        Returns:
            标准化后的标签列表
        """
        pass
    
    @abstractmethod
    def get_popular_tags(self, limit: int = 10) -> List[str]:
        """
        获取热门标签
        
        Args:
            limit: 返回的标签数量限制
            
        Returns:
            热门标签列表
        """
        pass
    
    @abstractmethod
    def search_tags(self, keyword: str) -> List[str]:
        """
        搜索标签
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的标签列表
        """
        pass


class ICategoryService(ABC):
    """分类服务接口"""
    
    @abstractmethod
    def get_all_categories(self) -> List[str]:
        """
        获取所有分类
        
        Returns:
            分类列表
        """
        pass
    
    @abstractmethod
    def get_category_counts(self) -> Dict[str, int]:
        """
        获取分类使用次数
        
        Returns:
            分类使用次数字典
        """
        pass
    
    @abstractmethod
    def normalize_category(self, category: str) -> str:
        """
        标准化分类
        
        Args:
            category: 原始分类
            
        Returns:
            标准化后的分类
        """
        pass
    
    @abstractmethod
    def get_popular_categories(self, limit: int = 10) -> List[str]:
        """
        获取热门分类
        
        Args:
            limit: 返回的分类数量限制
            
        Returns:
            热门分类列表
        """
        pass
    
    @abstractmethod
    def search_categories(self, keyword: str) -> List[str]:
        """
        搜索分类
        
        Args:
            keyword: 搜索关键词
            
        Returns:
            匹配的分类列表
        """
        pass


class IColorService(ABC):
    """颜色服务接口"""
    
    @abstractmethod
    def get_tag_color(self, tag: str) -> str:
        """
        获取标签颜色
        
        Args:
            tag: 标签名称
            
        Returns:
            颜色代码
        """
        pass
    
    @abstractmethod
    def get_category_color(self, category: str) -> str:
        """
        获取分类颜色
        
        Args:
            category: 分类名称
            
        Returns:
            颜色代码
        """
        pass


class IAIConfigManager(ABC):
    """AI配置管理器接口"""
    
    @abstractmethod
    def get_all_platforms(self) -> List[Dict[str, Any]]:
        """
        获取所有AI平台配置
        
        Returns:
            平台配置列表
        """
        pass
    
    @abstractmethod
    def get_platform_config(self, platform_id: str) -> Optional[Dict[str, Any]]:
        """
        获取指定平台配置
        
        Args:
            platform_id: 平台ID
            
        Returns:
            平台配置字典
        """
        pass
    
    @abstractmethod
    def save_platform_config(self, platform_id: str, config: Dict[str, Any]) -> bool:
        """
        保存平台配置
        
        Args:
            platform_id: 平台ID
            config: 配置字典
            
        Returns:
            是否保存成功
        """
        pass
    
    @abstractmethod
    def delete_platform_config(self, platform_id: str) -> bool:
        """
        删除平台配置
        
        Args:
            platform_id: 平台ID
            
        Returns:
            是否删除成功
        """
        pass


class IAIService(ABC):
    """AI服务接口"""
    
    @abstractmethod
    def send_prompt(self, platform_id: str, prompt: str, **kwargs) -> str:
        """
        发送提示词到AI平台
        
        Args:
            platform_id: 平台ID
            prompt: 提示词内容
            **kwargs: 其他参数
            
        Returns:
            AI响应结果
        """
        pass
    
    @abstractmethod
    def test_connection(self, platform_id: str, config: Dict[str, Any]) -> bool:
        """
        测试AI平台连接
        
        Args:
            platform_id: 平台ID
            config: 配置字典
            
        Returns:
            连接是否成功
        """
        pass


class IModelService(ABC):
    """数据模型服务接口"""
    
    @abstractmethod
    def get_model(self):
        """
        获取数据模型实例
        
        Returns:
            数据模型实例
        """
        pass
    
    @abstractmethod
    def initialize_database(self) -> bool:
        """
        初始化数据库
        
        Returns:
            是否初始化成功
        """
        pass
    
    @abstractmethod
    def backup_database(self, backup_path: str) -> bool:
        """
        备份数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否备份成功
        """
        pass
    
    @abstractmethod
    def restore_database(self, backup_path: str) -> bool:
        """
        恢复数据库
        
        Args:
            backup_path: 备份文件路径
            
        Returns:
            是否恢复成功
        """
        pass 