#!/usr/bin/env python3
"""
测试收藏功能的脚本
"""

from model import PromptModel, Prompt

def test_favorite_feature():
    """测试收藏功能"""
    print("开始测试收藏功能...")
    
    # 创建数据模型实例
    model = PromptModel("sqlite", "test_prompts.db")
    
    # 创建测试提示词
    test_prompt = Prompt(
        title="测试提示词",
        content="这是一个用于测试收藏功能的提示词",
        category="测试",
        tags=["测试", "收藏"],
        is_favorite=0
    )
    
    # 添加提示词
    prompt_id = model.add_prompt(test_prompt)
    print(f"添加了测试提示词，ID: {prompt_id}")
    
    # 获取提示词并检查初始收藏状态
    prompt = model.get_prompt(prompt_id)
    print(f"初始收藏状态: {prompt.is_favorite}")
    
    # 切换收藏状态
    success = model.toggle_favorite(prompt_id)
    print(f"切换收藏状态: {'成功' if success else '失败'}")
    
    # 再次获取提示词并检查收藏状态
    prompt = model.get_prompt(prompt_id)
    print(f"切换后收藏状态: {prompt.is_favorite}")
    
    # 获取所有收藏的提示词
    favorite_prompts = model.get_favorite_prompts()
    print(f"收藏的提示词数量: {len(favorite_prompts)}")
    
    # 再次切换收藏状态
    model.toggle_favorite(prompt_id)
    prompt = model.get_prompt(prompt_id)
    print(f"再次切换后收藏状态: {prompt.is_favorite}")
    
    # 清理测试数据
    model.delete_prompt(prompt_id)
    print("测试完成，已清理测试数据")

if __name__ == "__main__":
    test_favorite_feature()