#!/usr/bin/env python3
"""
AI适配器抽象基类
定义所有AI平台适配器的统一接口
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class AIRequest:
    """AI请求数据结构"""
    prompt: str
    model: str
    temperature: float = 0.7
    max_tokens: int = 1000
    top_p: float = 1.0
    presence_penalty: float = 0
    frequency_penalty: float = 0
    extra_params: Optional[Dict[str, Any]] = None

@dataclass
class AIResponse:
    """AI响应数据结构"""
    content: str
    model: str
    tokens_used: int
    cost: float
    latency: float  # 响应时间（秒）
    request_id: Optional[str] = None
    finish_reason: Optional[str] = None
    extra_data: Optional[Dict[str, Any]] = None

@dataclass
class APIError:
    """API错误信息"""
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None

class BaseAdapter(ABC):
    """AI适配器抽象基类"""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        """
        初始化适配器
        :param api_key: API密钥
        :param base_url: API基础URL（可选）
        """
        self.api_key = api_key
        self.base_url = base_url
        self._session = None
        
    @property
    @abstractmethod
    def platform_name(self) -> str:
        """平台名称"""
        pass
    
    @property
    @abstractmethod
    def supported_models(self) -> List[str]:
        """支持的模型列表"""
        pass
    
    @abstractmethod
    async def chat_completion(self, request: AIRequest) -> AIResponse:
        """
        发送聊天完成请求
        :param request: AI请求
        :return: AI响应
        :raises: APIError
        """
        pass
    
    @abstractmethod
    def validate_api_key(self) -> bool:
        """
        验证API密钥是否有效
        :return: True if valid, False otherwise
        """
        pass
    
    @abstractmethod
    def get_model_info(self, model: str) -> Dict[str, Any]:
        """
        获取模型信息
        :param model: 模型名称
        :return: 模型信息字典
        """
        pass
    
    def calculate_cost(self, model: str, input_tokens: int, output_tokens: int) -> float:
        """
        计算API调用费用
        :param model: 模型名称
        :param input_tokens: 输入token数
        :param output_tokens: 输出token数
        :return: 费用（USD）
        """
        model_info = self.get_model_info(model)
        pricing = model_info.get('pricing', {})
        
        input_cost = input_tokens * pricing.get('input', 0) / 1000
        output_cost = output_tokens * pricing.get('output', 0) / 1000
        
        return input_cost + output_cost
    
    def get_headers(self) -> Dict[str, str]:
        """
        获取请求头
        :return: 请求头字典
        """
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        if self._session:
            self._session.close() 