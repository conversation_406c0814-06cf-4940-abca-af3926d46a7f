#!/usr/bin/env python3
"""
筛选策略模块
使用策略模式实现筛选逻辑，解耦复杂的筛选条件
"""
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Set
from model import Prompt


class FilterStrategy(ABC):
    """筛选策略抽象基类"""
    
    @abstractmethod
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """
        应用筛选策略
        
        Args:
            prompts: 要筛选的提示词列表
            filter_value: 筛选值
            
        Returns:
            筛选后的提示词列表
        """
        pass
    
    @abstractmethod
    def is_applicable(self, filter_value: Any) -> bool:
        """
        检查筛选值是否适用于此策略
        
        Args:
            filter_value: 筛选值
            
        Returns:
            是否适用
        """
        pass


class CategoryFilterStrategy(FilterStrategy):
    """分类筛选策略 - 复制自原始filter_prompts方法"""
    
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """应用分类筛选"""
        if not self.is_applicable(filter_value):
            return prompts
            
        category = str(filter_value)
        return [p for p in prompts if p.category == category]
    
    def is_applicable(self, filter_value: Any) -> bool:
        """检查分类筛选是否适用"""
        return filter_value and str(filter_value) != '全部'


class TagFilterStrategy(FilterStrategy):
    """标签筛选策略 - 复制自原始filter_prompts方法"""
    
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """应用标签筛选（支持多选，使用交集逻辑）"""
        if not self.is_applicable(filter_value):
            return prompts
            
        # 处理不同类型的标签输入
        if isinstance(filter_value, str):
            selected_tags = {filter_value}
        elif isinstance(filter_value, (list, set)):
            selected_tags = set(filter_value)
        else:
            return prompts
            
        return [p for p in prompts if set(p.tags).intersection(selected_tags)]
    
    def is_applicable(self, filter_value: Any) -> bool:
        """检查标签筛选是否适用"""
        if not filter_value:
            return False
            
        if isinstance(filter_value, str):
            return bool(filter_value.strip())
        elif isinstance(filter_value, (list, set)):
            return len(filter_value) > 0
        else:
            return False


class FavoriteFilterStrategy(FilterStrategy):
    """收藏筛选策略 - 复制自原始filter_prompts方法"""
    
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """应用收藏筛选"""
        if not self.is_applicable(filter_value):
            return prompts
            
        return [p for p in prompts if p.is_favorite]
    
    def is_applicable(self, filter_value: Any) -> bool:
        """检查收藏筛选是否适用"""
        return bool(filter_value)


class SearchFilterStrategy(FilterStrategy):
    """搜索筛选策略 - 复制自原始filter_prompts方法"""
    
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """应用搜索筛选（在标题、内容、标签中搜索）"""
        if not self.is_applicable(filter_value):
            return prompts
            
        search_text = str(filter_value).lower()
        return [p for p in prompts if 
                search_text in p.title.lower() or 
                search_text in p.content.lower() or
                any(search_text in tag.lower() for tag in p.tags)]
    
    def is_applicable(self, filter_value: Any) -> bool:
        """检查搜索筛选是否适用"""
        return bool(filter_value and str(filter_value).strip())


class PinnedFilterStrategy(FilterStrategy):
    """置顶筛选策略"""
    
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """应用置顶筛选"""
        if not self.is_applicable(filter_value):
            return prompts
            
        return [p for p in prompts if p.is_pinned]
    
    def is_applicable(self, filter_value: Any) -> bool:
        """检查置顶筛选是否适用"""
        return bool(filter_value)


class MediaFilterStrategy(FilterStrategy):
    """媒体文件筛选策略"""
    
    def apply(self, prompts: List[Prompt], filter_value: Any) -> List[Prompt]:
        """应用媒体文件筛选"""
        if not self.is_applicable(filter_value):
            return prompts
            
        return [p for p in prompts if self._has_media(p)]
    
    def is_applicable(self, filter_value: Any) -> bool:
        """检查媒体文件筛选是否适用"""
        return bool(filter_value)
    
    def _has_media(self, prompt: Prompt) -> bool:
        """检查提示词是否包含媒体文件"""
        return prompt.has_media if hasattr(prompt, 'has_media') else bool(prompt.media_files)


class FilterChain:
    """筛选链 - 用于组合多个筛选策略"""
    
    def __init__(self):
        """初始化筛选链"""
        self._strategies: Dict[str, FilterStrategy] = {
            'category': CategoryFilterStrategy(),
            'tags': TagFilterStrategy(),
            'is_favorite': FavoriteFilterStrategy(),
            'search_text': SearchFilterStrategy(),
            'is_pinned': PinnedFilterStrategy(),
            'has_media': MediaFilterStrategy()
        }
    
    def add_strategy(self, key: str, strategy: FilterStrategy) -> None:
        """
        添加筛选策略
        
        Args:
            key: 策略键名
            strategy: 筛选策略实例
        """
        self._strategies[key] = strategy
    
    def remove_strategy(self, key: str) -> None:
        """
        移除筛选策略
        
        Args:
            key: 策略键名
        """
        if key in self._strategies:
            del self._strategies[key]
    
    def apply_all(self, prompts: List[Prompt], filters: Dict[str, Any]) -> List[Prompt]:
        """
        应用所有适用的筛选策略
        
        Args:
            prompts: 要筛选的提示词列表
            filters: 筛选条件字典
            
        Returns:
            筛选后的提示词列表
        """
        result = prompts.copy()
        
        # 按顺序应用每个筛选策略
        for filter_key, filter_value in filters.items():
            if filter_key in self._strategies:
                strategy = self._strategies[filter_key]
                if strategy.is_applicable(filter_value):
                    result = strategy.apply(result, filter_value)
        
        return result
    
    def get_available_strategies(self) -> List[str]:
        """
        获取可用的筛选策略列表
        
        Returns:
            策略键名列表
        """
        return list(self._strategies.keys())


class SortStrategy(ABC):
    """排序策略抽象基类"""
    
    @abstractmethod
    def sort(self, prompts: List[Prompt]) -> List[Prompt]:
        """
        排序提示词列表
        
        Args:
            prompts: 要排序的提示词列表
            
        Returns:
            排序后的提示词列表
        """
        pass


class UpdatedDescSortStrategy(SortStrategy):
    """按更新时间降序排序策略"""
    
    def sort(self, prompts: List[Prompt]) -> List[Prompt]:
        """按更新时间降序排序 - 复制自原始sort_prompts方法"""
        result = prompts.copy()
        
        # 置顶的提示词始终排在最前面
        pinned = [p for p in result if p.is_pinned]
        not_pinned = [p for p in result if not p.is_pinned]
        
        # 按更新时间降序排序非置顶提示词
        not_pinned.sort(key=lambda p: p.updated_at or "", reverse=True)
        
        return pinned + not_pinned


class CreatedDescSortStrategy(SortStrategy):
    """按创建时间降序排序策略"""
    
    def sort(self, prompts: List[Prompt]) -> List[Prompt]:
        """按创建时间降序排序 - 复制自原始sort_prompts方法"""
        result = prompts.copy()
        
        # 置顶的提示词始终排在最前面
        pinned = [p for p in result if p.is_pinned]
        not_pinned = [p for p in result if not p.is_pinned]
        
        # 按创建时间降序排序非置顶提示词
        not_pinned.sort(key=lambda p: p.created_at or "", reverse=True)
        
        return pinned + not_pinned


class TitleAscSortStrategy(SortStrategy):
    """按标题升序排序策略"""
    
    def sort(self, prompts: List[Prompt]) -> List[Prompt]:
        """按标题升序排序 - 复制自原始sort_prompts方法"""
        result = prompts.copy()
        
        # 置顶的提示词始终排在最前面
        pinned = [p for p in result if p.is_pinned]
        not_pinned = [p for p in result if not p.is_pinned]
        
        # 按标题升序排序非置顶提示词
        not_pinned.sort(key=lambda p: p.title.lower())
        
        return pinned + not_pinned


class CategoryAscSortStrategy(SortStrategy):
    """按分类升序排序策略"""
    
    def sort(self, prompts: List[Prompt]) -> List[Prompt]:
        """按分类升序排序 - 复制自原始sort_prompts方法"""
        result = prompts.copy()
        
        # 置顶的提示词始终排在最前面
        pinned = [p for p in result if p.is_pinned]
        not_pinned = [p for p in result if not p.is_pinned]
        
        # 按分类和标题升序排序非置顶提示词
        not_pinned.sort(key=lambda p: (p.category.lower(), p.title.lower()))
        
        return pinned + not_pinned


class SortChain:
    """排序链 - 用于管理排序策略"""
    
    def __init__(self):
        """初始化排序链"""
        self._strategies: Dict[str, SortStrategy] = {
            'updated_desc': UpdatedDescSortStrategy(),
            'created_desc': CreatedDescSortStrategy(),
            'title_asc': TitleAscSortStrategy(),
            'category_asc': CategoryAscSortStrategy()
        }
    
    def add_strategy(self, key: str, strategy: SortStrategy) -> None:
        """
        添加排序策略
        
        Args:
            key: 策略键名
            strategy: 排序策略实例
        """
        self._strategies[key] = strategy
    
    def sort(self, prompts: List[Prompt], sort_method: str = "updated_desc") -> List[Prompt]:
        """
        使用指定策略排序
        
        Args:
            prompts: 要排序的提示词列表
            sort_method: 排序方法
            
        Returns:
            排序后的提示词列表
        """
        if sort_method in self._strategies:
            return self._strategies[sort_method].sort(prompts)
        else:
            # 默认使用更新时间降序
            return self._strategies['updated_desc'].sort(prompts)
    
    def get_available_methods(self) -> List[str]:
        """
        获取可用的排序方法列表
        
        Returns:
            排序方法键名列表
        """
        return list(self._strategies.keys()) 