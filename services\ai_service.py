#!/usr/bin/env python3
"""
AI服务主接口
统一管理各种AI平台适配器，提供统一的AI调用接口
"""
import asyncio
import json
import os
from typing import Dict, List, Optional, Any, Type
from datetime import datetime
from pathlib import Path

from .api.base_adapter import BaseAdapter, AIRequest, AIResponse, APIError
from .api.openai_adapter import OpenAIAdapter, OpenAIAdapterSync
from config.ai_config import AI_PLATFORMS, DEFAULT_AI_PARAMS, API_KEY_CONFIG
from utils.error_handler import log_error

class AIService:
    """AI服务主类"""
    
    def __init__(self):
        """初始化AI服务"""
        self._adapters: Dict[str, BaseAdapter] = {}
        self._api_keys: Dict[str, str] = {}
        self._load_api_keys()
        
        # 注册适配器类
        self._adapter_classes: Dict[str, Type[BaseAdapter]] = {
            'openai': OpenAIAdapter,
            # 'claude': <PERSON><PERSON><PERSON>pt<PERSON>,  # 待实现
            # 'baidu': BaiduAdapter,    # 待实现
        }
    
    def _load_api_keys(self):
        """加载API密钥"""
        try:
            # 从环境变量加载
            for platform in AI_PLATFORMS.keys():
                env_key = f"{platform.upper()}_API_KEY"
                if env_key in os.environ:
                    self._api_keys[platform] = os.environ[env_key]
            
            # 从本地文件加载
            key_file = Path(API_KEY_CONFIG['key_file'])
            if key_file.exists():
                with open(key_file, 'r', encoding='utf-8') as f:
                    file_keys = json.load(f)
                    self._api_keys.update(file_keys)
                    
        except Exception as e:
            log_error(f"加载API密钥失败: {e}")
    
    def save_api_key(self, platform: str, api_key: str):
        """
        保存API密钥
        :param platform: 平台名称
        :param api_key: API密钥
        """
        self._api_keys[platform] = api_key
        
        # 保存到文件
        try:
            key_file = Path(API_KEY_CONFIG['key_file'])
            with open(key_file, 'w', encoding='utf-8') as f:
                json.dump(self._api_keys, f, indent=2)
        except Exception as e:
            log_error(f"保存API密钥失败: {e}")
    
    def get_adapter(self, platform: str) -> Optional[BaseAdapter]:
        """
        获取指定平台的适配器
        :param platform: 平台名称
        :return: 适配器实例或None
        """
        if platform not in self._adapters:
            if platform not in self._api_keys:
                return None
            
            if platform not in self._adapter_classes:
                return None
            
            try:
                adapter_class = self._adapter_classes[platform]
                api_key = self._api_keys[platform]
                self._adapters[platform] = adapter_class(api_key)
            except Exception as e:
                log_error(f"创建{platform}适配器失败: {e}")
                return None
        
        return self._adapters.get(platform)
    
    def get_available_platforms(self) -> List[str]:
        """
        获取可用的AI平台列表
        :return: 平台名称列表
        """
        available = []
        for platform in self._adapter_classes.keys():
            if platform in self._api_keys:
                available.append(platform)
        return available
    
    def get_platform_models(self, platform: str) -> List[str]:
        """
        获取指定平台支持的模型列表
        :param platform: 平台名称
        :return: 模型列表
        """
        adapter = self.get_adapter(platform)
        if adapter:
            return adapter.supported_models
        return []
    
    async def chat_completion(
        self, 
        platform: str, 
        prompt: str, 
        model: Optional[str] = None,
        **kwargs
    ) -> AIResponse:
        """
        发送聊天完成请求
        :param platform: AI平台名称
        :param prompt: 提示词
        :param model: 模型名称（可选）
        :param kwargs: 其他参数
        :return: AI响应
        :raises: APIError
        """
        adapter = self.get_adapter(platform)
        if not adapter:
            raise APIError(
                code="ADAPTER_NOT_FOUND",
                message=f"未找到平台 {platform} 的适配器或API密钥"
            )
        
        # 使用默认模型
        if not model:
            model = AI_PLATFORMS[platform]['default_model']
        
        # 构建请求
        request_params = DEFAULT_AI_PARAMS.copy()
        request_params.update(kwargs)
        
        request = AIRequest(
            prompt=prompt,
            model=model,
            **request_params
        )
        
        return await adapter.chat_completion(request)
    
    def chat_completion_sync(
        self, 
        platform: str, 
        prompt: str, 
        model: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        同步版本的聊天完成（用于简单测试）
        :param platform: AI平台名称
        :param prompt: 提示词
        :param model: 模型名称（可选）
        :return: 响应数据字典
        """
        if platform == 'openai':
            if platform not in self._api_keys:
                return {
                    'success': False,
                    'error': f'未找到 {platform} 的API密钥'
                }
            
            adapter = OpenAIAdapterSync(self._api_keys[platform])
            if not model:
                model = AI_PLATFORMS[platform]['default_model']
            
            return adapter.chat_completion_sync(prompt, model)
        
        return {
            'success': False,
            'error': f'平台 {platform} 暂不支持同步调用'
        }
    
    def validate_api_key(self, platform: str) -> bool:
        """
        验证API密钥是否有效
        :param platform: 平台名称
        :return: True if valid, False otherwise
        """
        adapter = self.get_adapter(platform)
        if adapter:
            return adapter.validate_api_key()
        return False
    
    def get_platform_info(self, platform: str) -> Dict[str, Any]:
        """
        获取平台信息
        :param platform: 平台名称
        :return: 平台信息字典
        """
        if platform in AI_PLATFORMS:
            return AI_PLATFORMS[platform].copy()
        return {}
    
    def test_connection(self, platform: str) -> Dict[str, Any]:
        """
        测试平台连接
        :param platform: 平台名称
        :return: 测试结果
        """
        try:
            # 使用简单的测试提示词
            test_prompt = "Hello, this is a connection test."
            result = self.chat_completion_sync(platform, test_prompt)
            
            if result.get('success'):
                return {
                    'success': True,
                    'message': f'{platform} 连接测试成功',
                    'details': {
                        'model': result.get('model'),
                        'tokens': result.get('usage', {}).get('total_tokens', 0)
                    }
                }
            else:
                return {
                    'success': False,
                    'message': f'{platform} 连接测试失败',
                    'error': result.get('error')
                }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'{platform} 连接测试异常',
                'error': str(e)
            }

# 全局AI服务实例
ai_service = AIService() 