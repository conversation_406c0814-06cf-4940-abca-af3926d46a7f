#!/usr/bin/env python3
"""
AI服务配置文件
包含各大模型平台的配置信息、模型参数和默认设置
"""
from typing import Dict, List, Any
import os
from pathlib import Path

# AI平台配置
AI_PLATFORMS = {
    'openai': {
        'name': 'OpenAI',
        'base_url': 'https://api.openai.com/v1',
        'models': {
            'gpt-4': {
                'max_tokens': 4096,
                'pricing': {'input': 0.03, 'output': 0.06}
            },
            'gpt-3.5-turbo': {
                'max_tokens': 4096,
                'pricing': {'input': 0.001, 'output': 0.002}
            }
        },
        'default_model': 'gpt-3.5-turbo'
    },
    'claude': {
        'name': 'Anthropic Claude',
        'base_url': 'https://api.anthropic.com',
        'models': {
            'claude-3-opus-20240229': {
                'max_tokens': 4096,
                'pricing': {'input': 0.015, 'output': 0.075}
            },
            'claude-3-sonnet-20240229': {
                'max_tokens': 4096,
                'pricing': {'input': 0.003, 'output': 0.015}
            },
            'claude-3-haiku-20240307': {
                'max_tokens': 4096,
                'pricing': {'input': 0.00025, 'output': 0.00125}
            }
        },
        'default_model': 'claude-3-sonnet-20240229'
    },
    'baidu': {
        'name': '百度文心一言',
        'base_url': 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat',
        'models': {
            'ernie-bot': {
                'max_tokens': 2048,
                'pricing': {'input': 0.008, 'output': 0.008}
            },
            'ernie-bot-turbo': {
                'max_tokens': 2048,
                'pricing': {'input': 0.003, 'output': 0.003}
            }
        },
        'default_model': 'ernie-bot'
    },
    'alibaba': {
        'name': '阿里通义千问',
        'base_url': 'https://dashscope.aliyuncs.com/api/v1',
        'models': {
            'qwen-max': {
                'max_tokens': 2048,
                'pricing': {'input': 0.02, 'output': 0.06}
            },
            'qwen-plus': {
                'max_tokens': 2048,
                'pricing': {'input': 0.004, 'output': 0.012}
            }
        },
        'default_model': 'qwen-plus'
    }
}

# 默认AI参数
DEFAULT_AI_PARAMS = {
    'temperature': 0.7,
    'max_tokens': 1000,
    'top_p': 1.0,
    'presence_penalty': 0,
    'frequency_penalty': 0,
    'timeout': 30  # 请求超时时间（秒）
}

# API密钥存储配置
API_KEY_CONFIG = {
    'encryption_enabled': True,
    'storage_method': 'local',  # local, env
    'key_file': 'api_keys.json'  # 加密存储的密钥文件
}

# 费用统计配置
COST_TRACKING = {
    'enabled': True,
    'currency': 'USD',
    'daily_limit': 10.0,  # 每日费用限制
    'warning_threshold': 0.8  # 警告阈值（80%）
}

# 请求配置
REQUEST_CONFIG = {
    'max_retries': 3,
    'retry_delay': 1,  # 重试延迟（秒）
    'rate_limit': {
        'requests_per_minute': 60,
        'tokens_per_minute': 90000
    }
}

# 结果缓存配置
CACHE_CONFIG = {
    'enabled': True,
    'ttl': 3600,  # 缓存有效期（秒）
    'max_size': 1000  # 最大缓存条目数
} 