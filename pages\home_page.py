#!/usr/bin/env python3
"""
首页组件
从main.py的ContentArea.create_home_page()方法中提取的首页实现
"""
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QScrollArea, 
                              QStackedWidget, QListWidget)
from PySide6.QtCore import Qt

from pages.base_page import BasePage


class HomePage(BasePage):
    """首页组件 - 提取自ContentArea.create_home_page()方法"""
    
    def __init__(self, content_area_parent):
        """
        初始化首页
        
        Args:
            content_area_parent: ContentArea实例，用于访问原有方法和属性
        """
        self.content_area = content_area_parent
        super().__init__(content_area_parent)
    
    def setup_ui(self) -> None:
        """设置UI - 完全复制自原始create_home_page方法"""
        layout = self.get_main_layout()
        layout.setContentsMargins(15, 8, 15, 15)  # 减少顶部间距从15px到8px
        layout.setSpacing(12)  # 减少整体间距从15px到12px
        
        # 导入SearchBox类
        from search_box import SearchBox
        
        # 搜索框和新建按钮区域 - 调整到内容区域顶部
        search_layout = QHBoxLayout()
        
        # 使用自定义搜索框组件
        self.content_area.search_input = SearchBox()
        # 连接搜索信号到处理方法
        self.content_area.search_input.searchTextChanged.connect(self.content_area.on_search_text_changed)
        search_layout.addWidget(self.content_area.search_input)
        
        # 创建方形SVG图标按钮替换文本按钮
        add_btn = self.content_area.create_add_button()
        search_layout.addWidget(add_btn)
        
        layout.addLayout(search_layout)
        
        # 工具栏
        toolbar_widget = self.content_area.create_toolbar()
        layout.addWidget(toolbar_widget)
        
        # 分类筛选栏容器（默认隐藏）
        self.content_area.category_filter_bar = QWidget()
        self.content_area.category_filter_bar.setFixedHeight(150)  # 从50px增加到150px（3倍）
        self.content_area.category_filter_bar.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 0px;
            }
        """)
        
        # 为分类筛选栏设置流式布局容器
        category_scroll = QScrollArea(self.content_area.category_filter_bar)
        category_scroll.setWidgetResizable(True)
        category_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        category_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        category_scroll.setStyleSheet("""
            QScrollArea {
                background-color: transparent;
                border: none;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """)
        
        # 创建分类筛选栏的主布局
        category_main_layout = QVBoxLayout(self.content_area.category_filter_bar)
        category_main_layout.setContentsMargins(0, 0, 0, 0)
        category_main_layout.addWidget(category_scroll)
        
        # 创建可滚动的内容容器
        from flow_layout import FlowLayout
        self.content_area.category_content_widget = QWidget()
        self.content_area.category_filter_layout = FlowLayout(self.content_area.category_content_widget)
        self.content_area.category_filter_layout.setContentsMargins(12, 8, 12, 8)
        self.content_area.category_filter_layout.setSpacing(8)
        
        category_scroll.setWidget(self.content_area.category_content_widget)
        
        # 添加分类筛选栏到布局
        layout.addWidget(self.content_area.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.content_area.tag_filter_area = QScrollArea()
        self.content_area.tag_filter_area.setFixedHeight(150)  # 从50px增加到150px（3倍）
        self.content_area.tag_filter_area.setWidgetResizable(True)
        self.content_area.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.content_area.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 改为按需显示垂直滚动条
        self.content_area.tag_filter_area.setStyleSheet("""
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
            
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #BAE6FD;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #93C5FD;
            }
        """)
        self.content_area.tag_filter_area.hide()
        
        # 标签筛选栏内容容器 - 使用流式布局
        tag_filter_widget = QWidget()
        self.content_area.tag_filter_layout = FlowLayout(tag_filter_widget)
        self.content_area.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.content_area.tag_filter_layout.setSpacing(6)
        self.content_area.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.content_area.tag_filter_area)
        
        # 初始状态下隐藏分类筛选栏
        self.content_area.category_filter_bar.hide()
        
        # 创建堆叠容器来切换列表视图和卡片视图
        self.content_area.view_stack = QStackedWidget()
        
        # 列表视图
        self.content_area.prompt_list = QListWidget()
        self.content_area.prompt_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #F9FAFB;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
        """)
        self.content_area.view_stack.addWidget(self.content_area.prompt_list)
        
        # 卡片视图
        self.content_area.card_view = self.content_area.create_card_view()
        self.content_area.view_stack.addWidget(self.content_area.card_view)
        
        # 根据保存的设置显示相应视图
        if self.content_area.current_view == "card":
            self.content_area.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.content_area.view_stack.setCurrentIndex(0)  # 显示列表视图
        
        layout.addWidget(self.content_area.view_stack)
    
    def load_data(self) -> None:
        """加载数据 - 在ContentArea中处理数据加载"""
        # 数据加载由ContentArea处理，这里无需实现
        pass 