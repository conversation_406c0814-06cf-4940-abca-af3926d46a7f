# 提示词导入导出功能说明

## 功能概述

在首页工具栏的工具箱菜单中新增了导入和导出功能，支持将提示词数据以多种格式进行导入和导出。

## 功能位置

- **位置**: 首页工具栏 → 工具箱按钮 → 下拉菜单
- **新增菜单项**:
  - 导出提示词
  - 从文件导入

## 支持的格式

### 1. JSON格式 (.json)
- **特点**: 完整保留所有提示词数据，包括标签、分类、收藏状态等
- **适用场景**: 数据备份、完整迁移
- **数据结构**: 包含所有字段的完整JSON对象数组

### 2. CSV格式 (.csv)
- **特点**: 表格形式，便于在Excel等软件中查看和编辑
- **适用场景**: 数据分析、批量编辑
- **包含字段**: 标题、内容、分类、标签、类型、收藏、置顶、创建时间

### 3. Markdown格式 (.md)
- **特点**: 可读性强的文档格式，便于阅读和分享
- **适用场景**: 文档分享、知识整理
- **格式**: 结构化的Markdown文档，包含所有提示词信息

## 使用方法

### 导出提示词

1. 点击首页工具栏的工具箱按钮
2. 选择"导出提示词"
3. 在弹出的对话框中选择导出格式（JSON/CSV/Markdown）
4. 点击"导出"按钮
5. 选择保存位置和文件名
6. 完成导出

### 从文件导入

1. 点击首页工具栏的工具箱按钮
2. 选择"从文件导入"
3. 在弹出的对话框中点击"选择文件"
4. 选择要导入的文件（支持.json、.csv、.md格式）
5. 系统会自动分析文件内容，显示：
   - 文件格式和名称
   - 发现的提示词数量
   - 前5个提示词的预览信息（标题、分类、标签）
   - 如果超过5个，会显示"还有X个..."的提示
6. 查看分析结果后，点击"确认导入"进行实际导入
7. 导入完成后会显示成功消息

## 注意事项

### 导出注意事项
- JSON格式会完整保留所有数据，但会移除内部ID以避免导入冲突
- CSV格式会将标签以逗号分隔的形式存储
- Markdown格式会生成结构化的文档，便于阅读

### 导入注意事项
- 导入前会先分析文件内容，显示提示词数量和预览信息
- 用户可以查看分析结果后再决定是否导入
- 导入时会自动创建新的提示词记录
- 如果原文件中有ID字段，导入时会忽略以避免冲突
- 导入过程中如果某个提示词出错，会跳过该条记录并继续处理其他记录
- 导入完成后会自动刷新提示词列表

### 文件格式要求
- **JSON文件**: 必须是有效的JSON格式，包含提示词对象数组
- **CSV文件**: 必须包含正确的表头，字段顺序要与导出格式一致
- **Markdown文件**: 必须按照特定的格式结构编写

## 错误处理

- 如果文件格式不正确，会显示错误提示
- 如果导入过程中出现错误，会在控制台输出详细信息
- 导入失败时不会影响现有的提示词数据

## 技术实现

- 使用PySide6的QFileDialog进行文件选择
- 支持UTF-8编码，确保中文内容正确显示
- 实现了完整的错误处理和用户反馈
- 与现有的提示词管理系统完全集成

## 文件分析功能

### 分析流程
1. 用户选择文件后，系统自动识别文件格式
2. 根据文件格式调用相应的分析函数
3. 解析文件内容，提取提示词信息
4. 显示分析结果，包括文件信息和提示词预览
5. 用户确认后执行实际导入

### 分析内容
- **文件信息**: 文件名、格式类型
- **提示词数量**: 发现的提示词总数
- **预览信息**: 前5个提示词的标题、分类、标签
- **扩展提示**: 如果超过5个，显示剩余数量

### 界面优化
- **对话框尺寸**: 500x600像素，确保内容完整显示
- **预览列表高度**: 最大200像素，可显示更多提示词信息
- **布局间距**: 主布局20像素，分析区域15像素，提供舒适的视觉体验

### 支持格式
- **JSON**: 解析JSON对象数组，支持完整的提示词数据结构
- **CSV**: 解析表格格式，支持标准字段映射
- **Markdown**: 解析结构化文档，支持自定义格式 