#!/usr/bin/env python3
"""
依赖注入容器模块
提供依赖注入功能，解决组件间的硬编码依赖问题
"""
from typing import Dict, Type, Any, Callable, Optional
from abc import ABC, abstractmethod
import inspect


class IDependencyContainer(ABC):
    """依赖注入容器接口"""
    
    @abstractmethod
    def register(self, interface: Type, implementation: Type, singleton: bool = False):
        """
        注册服务
        
        Args:
            interface: 接口类型
            implementation: 实现类型
            singleton: 是否为单例
        """
        pass
    
    @abstractmethod
    def register_instance(self, interface: Type, instance: Any):
        """
        注册服务实例
        
        Args:
            interface: 接口类型
            instance: 服务实例
        """
        pass
    
    @abstractmethod
    def register_factory(self, interface: Type, factory: Callable[[], Any], singleton: bool = False):
        """
        注册工厂方法
        
        Args:
            interface: 接口类型
            factory: 工厂方法
            singleton: 是否为单例
        """
        pass
    
    @abstractmethod
    def resolve(self, interface: Type) -> Any:
        """
        解析依赖
        
        Args:
            interface: 接口类型
            
        Returns:
            服务实例
        """
        pass
    
    @abstractmethod
    def is_registered(self, interface: Type) -> bool:
        """
        检查接口是否已注册
        
        Args:
            interface: 接口类型
            
        Returns:
            是否已注册
        """
        pass


class ServiceRegistration:
    """服务注册信息"""
    
    def __init__(self, implementation: Type = None, instance: Any = None, 
                 factory: Callable = None, singleton: bool = False):
        self.implementation = implementation
        self.instance = instance
        self.factory = factory
        self.singleton = singleton
        self._singleton_instance = None
    
    def get_instance(self, container: 'DependencyContainer') -> Any:
        """获取服务实例"""
        if self.instance is not None:
            return self.instance
        
        if self.singleton and self._singleton_instance is not None:
            return self._singleton_instance
        
        # 创建实例
        if self.factory is not None:
            instance = self.factory()
        elif self.implementation is not None:
            instance = container._create_instance(self.implementation)
        else:
            raise ValueError("无法创建实例：没有实现类或工厂方法")
        
        # 如果是单例，缓存实例
        if self.singleton:
            self._singleton_instance = instance
        
        return instance


class DependencyContainer(IDependencyContainer):
    """依赖注入容器实现"""
    
    def __init__(self):
        self._registrations: Dict[Type, ServiceRegistration] = {}
        self._resolving: set = set()  # 用于检测循环依赖
    
    def register(self, interface: Type, implementation: Type, singleton: bool = False):
        """注册服务类型"""
        if not inspect.isclass(implementation):
            raise ValueError(f"implementation必须是一个类: {implementation}")
        
        registration = ServiceRegistration(
            implementation=implementation,
            singleton=singleton
        )
        self._registrations[interface] = registration
    
    def register_instance(self, interface: Type, instance: Any):
        """注册服务实例"""
        registration = ServiceRegistration(instance=instance)
        self._registrations[interface] = registration
    
    def register_factory(self, interface: Type, factory: Callable[[], Any], singleton: bool = False):
        """注册工厂方法"""
        if not callable(factory):
            raise ValueError(f"factory必须是可调用对象: {factory}")
        
        registration = ServiceRegistration(
            factory=factory,
            singleton=singleton
        )
        self._registrations[interface] = registration
    
    def resolve(self, interface: Type) -> Any:
        """解析依赖"""
        if interface not in self._registrations:
            raise ValueError(f"未注册的接口: {interface}")
        
        # 检测循环依赖
        if interface in self._resolving:
            raise ValueError(f"检测到循环依赖: {interface}")
        
        try:
            self._resolving.add(interface)
            registration = self._registrations[interface]
            return registration.get_instance(self)
        finally:
            self._resolving.discard(interface)
    
    def is_registered(self, interface: Type) -> bool:
        """检查接口是否已注册"""
        return interface in self._registrations
    
    def _create_instance(self, implementation: Type) -> Any:
        """创建实例，支持构造函数依赖注入"""
        try:
            # 获取构造函数签名
            sig = inspect.signature(implementation.__init__)
            params = list(sig.parameters.values())[1:]  # 跳过self参数
            
            # 解析构造函数参数
            args = []
            kwargs = {}
            
            for param in params:
                if param.annotation != inspect.Parameter.empty:
                    # 如果参数有类型注解，尝试解析依赖
                    param_type = param.annotation
                    if self.is_registered(param_type):
                        dependency = self.resolve(param_type)
                        if param.default == inspect.Parameter.empty:
                            args.append(dependency)
                        else:
                            kwargs[param.name] = dependency
                    elif param.default == inspect.Parameter.empty:
                        # 没有默认值且未注册的依赖
                        raise ValueError(f"无法解析依赖: {param_type}")
                elif param.default == inspect.Parameter.empty:
                    # 没有类型注解且没有默认值
                    raise ValueError(f"参数 {param.name} 缺少类型注解或默认值")
            
            # 创建实例
            return implementation(*args, **kwargs)
        
        except Exception as e:
            # 如果依赖注入失败，尝试无参构造
            try:
                return implementation()
            except Exception:
                raise ValueError(f"无法创建实例 {implementation}: {e}")
    
    def clear(self):
        """清空所有注册"""
        self._registrations.clear()
        self._resolving.clear()
    
    def get_registered_interfaces(self) -> list:
        """获取所有已注册的接口"""
        return list(self._registrations.keys())


# 全局容器实例
_global_container: Optional[DependencyContainer] = None


def get_container() -> DependencyContainer:
    """获取全局依赖注入容器"""
    global _global_container
    if _global_container is None:
        _global_container = DependencyContainer()
    return _global_container


def set_container(container: DependencyContainer):
    """设置全局依赖注入容器"""
    global _global_container
    _global_container = container


def reset_container():
    """重置全局依赖注入容器"""
    global _global_container
    _global_container = None


# 装饰器支持
def inject(interface: Type):
    """
    依赖注入装饰器
    
    Args:
        interface: 要注入的接口类型
        
    Returns:
        装饰器函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            container = get_container()
            dependency = container.resolve(interface)
            return func(dependency, *args, **kwargs)
        return wrapper
    return decorator


def injectable(interface: Type, singleton: bool = False):
    """
    可注入服务装饰器
    
    Args:
        interface: 实现的接口类型
        singleton: 是否为单例
        
    Returns:
        装饰器函数
    """
    def decorator(cls):
        container = get_container()
        container.register(interface, cls, singleton)
        return cls
    return decorator 