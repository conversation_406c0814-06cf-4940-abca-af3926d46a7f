#!/usr/bin/env python3
"""
AI平台扩展配置
包含平台分类、UI配置和展示信息
"""

# 平台分类配置
PLATFORM_CATEGORIES = {
    '热门推荐': {
        'platforms': ['openai', 'claude', 'gemini'],
        'description': '最受欢迎的AI平台',
        'order': 1
    },
    '国内平台': {
        'platforms': ['baidu', 'alibaba', 'xunfei', 'zhipu'],
        'description': '国产大模型平台',
        'order': 2
    },
    '第三方服务': {
        'platforms': ['siliconflow', 'volcengine', 'deepseek'],
        'description': '第三方API服务商',
        'order': 3
    },
    '自定义': {
        'platforms': ['custom'],
        'description': '自定义API端点',
        'order': 4
    }
}

# 平台UI配置
PLATFORM_UI_CONFIG = {
    'openai': {
        'display_name': 'OpenAI',
        'description': 'GPT-4, GPT-3.5等模型',
        'icon': 'openai.svg',
        'color': '#10A37F',
        'website': 'https://openai.com',
        'pricing_url': 'https://openai.com/pricing'
    },
    'claude': {
        'display_name': 'Anthropic Claude',
        'description': 'Claude-3 Opus, Sonnet, Haiku',
        'icon': 'claude.svg',
        'color': '#D97706',
        'website': 'https://www.anthropic.com',
        'pricing_url': 'https://www.anthropic.com/pricing'
    },
    'gemini': {
        'display_name': 'Google Gemini',
        'description': 'Gemini Pro, Ultra模型',
        'icon': 'gemini.svg',
        'color': '#4285F4',
        'website': 'https://ai.google.dev',
        'pricing_url': 'https://ai.google.dev/pricing'
    },
    'baidu': {
        'display_name': '百度文心一言',
        'description': 'ERNIE-Bot系列模型',
        'icon': 'baidu.svg',
        'color': '#2932E1',
        'website': 'https://cloud.baidu.com/product/wenxinworkshop',
        'pricing_url': 'https://cloud.baidu.com/doc/WENXINWORKSHOP/s/hlrk4akp7'
    },
    'alibaba': {
        'display_name': '阿里通义千问',
        'description': 'Qwen系列模型',
        'icon': 'alibaba.svg',
        'color': '#FF6A00',
        'website': 'https://dashscope.aliyun.com',
        'pricing_url': 'https://help.aliyun.com/zh/dashscope/developer-reference/tongyi-thousand-questions-metering-and-billing'
    },
    'xunfei': {
        'display_name': '讯飞星火',
        'description': 'Spark系列大模型',
        'icon': 'xunfei.svg',
        'color': '#3F7EF3',
        'website': 'https://xinghuo.xfyun.cn',
        'pricing_url': 'https://xinghuo.xfyun.cn/pricing'
    },
    'zhipu': {
        'display_name': '智谱AI',
        'description': 'GLM系列模型',
        'icon': 'zhipu.svg',
        'color': '#6366F1',
        'website': 'https://open.bigmodel.cn',
        'pricing_url': 'https://open.bigmodel.cn/pricing'
    },
    'siliconflow': {
        'display_name': '硅基流动',
        'description': '提供多种开源模型API',
        'icon': 'siliconflow.svg',
        'color': '#3B82F6',
        'website': 'https://siliconflow.cn',
        'pricing_url': 'https://siliconflow.cn/pricing'
    },
    'volcengine': {
        'display_name': '火山引擎',
        'description': '字节跳动大模型服务',
        'icon': 'volcengine.svg',
        'color': '#F97316',
        'website': 'https://www.volcengine.com/product/doubao',
        'pricing_url': 'https://www.volcengine.com/docs/82379/1099475'
    },
    'deepseek': {
        'display_name': 'DeepSeek',
        'description': 'DeepSeek系列模型',
        'icon': 'deepseek.svg',
        'color': '#8B5CF6',
        'website': 'https://platform.deepseek.com',
        'pricing_url': 'https://platform.deepseek.com/pricing'
    },
    'custom': {
        'display_name': '自定义API',
        'description': '配置自定义API端点',
        'icon': 'custom.svg',
        'color': '#6B7280',
        'website': '',
        'pricing_url': ''
    }
} 