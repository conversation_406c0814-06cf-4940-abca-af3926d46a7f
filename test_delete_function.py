#!/usr/bin/env python3
"""
删除功能诊断测试
"""

import sqlite3
from model import PromptModel, Prompt

print("🔍 删除功能诊断测试")
print("=" * 50)

# 初始化模型
model = PromptModel()

# 1. 检查当前数据状态
print("📊 1. 检查当前数据状态:")
all_prompts = model.get_all_prompts(include_deleted=True)
active_prompts = model.get_all_prompts(include_deleted=False)
deleted_prompts = model.get_deleted_prompts()

print(f"   总提示词数量: {len(all_prompts)}")
print(f"   活跃提示词数量: {len(active_prompts)}")
print(f"   已删除提示词数量: {len(deleted_prompts)}")

if deleted_prompts:
    print("   回收站中的提示词:")
    for prompt in deleted_prompts[:3]:  # 只显示前3个
        print(f"     ID:{prompt.id} 标题:{prompt.title[:30]}... 删除时间:{prompt.deleted_at}")

# 2. 测试删除操作
if active_prompts:
    test_prompt = active_prompts[0]
    print(f"\n🗑️ 2. 测试删除操作:")
    print(f"   选择测试提示词: ID={test_prompt.id}, 标题={test_prompt.title[:50]}...")
    
    # 执行删除
    print("   执行删除操作...")
    model.delete_prompt(test_prompt.id)
    print("   ✅ delete_prompt() 调用完成")
    
    # 验证删除结果
    print("   验证删除结果...")
    
    # 检查提示词是否还在活跃列表中
    updated_active = model.get_all_prompts(include_deleted=False)
    is_removed_from_active = not any(p.id == test_prompt.id for p in updated_active)
    print(f"   是否从活跃列表移除: {is_removed_from_active}")
    
    # 检查提示词是否在回收站中
    updated_deleted = model.get_deleted_prompts()
    is_in_trash = any(p.id == test_prompt.id for p in updated_deleted)
    print(f"   是否在回收站中: {is_in_trash}")
    
    # 直接查询数据库验证
    with sqlite3.connect(model.storage.db_path) as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT is_deleted, deleted_at FROM prompts WHERE id=?", (test_prompt.id,))
        result = cursor.fetchone()
        if result:
            print(f"   数据库记录: is_deleted={result[0]}, deleted_at={result[1]}")
        else:
            print("   ❌ 数据库中找不到记录")
    
    if is_removed_from_active and is_in_trash:
        print("   ✅ 删除操作成功！")
        
        # 恢复测试数据
        print("   恢复测试数据...")
        model.restore_prompt(test_prompt.id)
        print("   🔄 测试数据已恢复")
    else:
        print("   ❌ 删除操作失败！")
        
        # 如果失败，尝试恢复
        if not is_removed_from_active:
            print("     问题：提示词仍在活跃列表中")
        if not is_in_trash:
            print("     问题：提示词未出现在回收站中")
else:
    print("   ❌ 没有活跃的提示词可供测试")

print("\n" + "=" * 50)
print("🏁 诊断完成") 