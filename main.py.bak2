#!/usr/bin/env python3
"""
Prompt Assistant - 重构版桌面应用界面
采用460x800固定尺寸，无标题栏设计
"""
import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QPushButton, QLabel, QFrame, 
                               QStackedWidget, QListWidget, QTextEdit, 
                               QLineEdit, QScrollArea, QMessageBox, QGraphicsDropShadowEffect,
                               QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QSize, QPoint
from PySide6.QtGui import QIcon, QPalette, QColor, QFont, QPixmap, QPainter, QPen
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QGraphicsDropShadowEffect
from tag_filter_styles import get_active_filter_button_style, get_filter_button_style
from model import PromptModel, Prompt
from pathlib import Path
from create_prompt_dialog import <PERSON>reatePromptDialog
from tag_filter_styles import get_active_filter_button_style, get_filter_button_style

class CustomTitleBar(QWidget):
    '''自定义标题栏组件 (400x50)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(400, 50)
        self.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        ''')
        self.init_ui()
        
        # 用于拖拽窗口
        self.drag_position = QPoint()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 0, 10, 0)
        layout.setSpacing(10)
        
        # 标题标签
        self.title_label = QLabel("Prompt收藏助手")
        self.title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        layout.addWidget(self.title_label)
        
        layout.addStretch()
        
        # 窗口控制按钮
        self.create_control_buttons(layout)
        
    def create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: #6B7280;
                font-size: 16px;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setStyleSheet(button_style)
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        close_style = button_style.replace("#E5E7EB", "#FEE2E2").replace("#D1D5DB", "#FECACA")
        close_style = close_style.replace("color: #6B7280;", "color: #DC2626;")
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet(close_style)
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def minimize_window(self):
        if self.parent_window:
            self.parent_window.showMinimized()
            
    def close_window(self):
        if self.parent_window:
            self.parent_window.close()
            
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)

class AddIconButton(QPushButton):
    """方形SVG新建按钮 - 三种状态"""
    
    def __init__(self, svg_content, tooltip, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.setFixedSize(36, 36)  # 方形尺寸
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(4)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(1)
        self.shadow_effect.setColor(QColor(59, 130, 246, 40))  # 蓝色阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：更深的蓝色阴影
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(29, 78, 216, 80))
        elif state == "hover":
            # 悬停状态：增强的蓝色阴影
            self.shadow_effect.setBlurRadius(5)
            self.shadow_effect.setYOffset(1)
            self.shadow_effect.setColor(QColor(37, 99, 235, 60))
        else:
            # 默认状态：轻微蓝色阴影
            self.shadow_effect.setBlurRadius(4)
            self.shadow_effect.setYOffset(1)
            self.shadow_effect.setColor(QColor(59, 130, 246, 40))
        
    def update_icon(self, state="default"):
        """更新图标状态"""
        # 三种状态的颜色和背景
        if state == "active":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#1D4ED8"  # 深蓝色背景
        elif state == "hover":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#2563EB"  # 中蓝色背景
        else:
            color = "#FFFFFF"  # 白色图标
            bg_color = "#3B82F6"  # 默认蓝色背景
            
        # 设置按钮样式
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: none;
                border-radius: 6px;
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(20, 20))
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.update_icon("hover")
        self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon("default")
        self.update_shadow("default")
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("active")
            self.update_shadow("active")
        super().mousePressEvent(event)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("hover")
            self.update_shadow("hover")
        super().mouseReleaseEvent(event)

class PromptCardWidget(QFrame):
    """提示词卡片组件 - 自适应宽度，高度自适应"""
    
    def __init__(self, prompt, parent=None):
        super().__init__(parent)
        self.prompt = prompt
        # 移除固定宽度，改为自适应宽度
        self.setMinimumHeight(120)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)  # 水平扩展，垂直最小
        
        # 设置基础框架样式
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        
        # 初始化鼠标悬停状态
        self.hovered = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI布局"""
        # 背景颜色设置
        self.setStyleSheet('''
            QFrame {
                background-color: white;
            }
        ''')
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 10, 12, 10)  # 保持左、上、右的内边距，减少底部内边距
        main_layout.setSpacing(8)
        
        # 顶部区域：分类 + 操作按钮
        top_widget = self.create_top_section()
        main_layout.addWidget(top_widget)
        
        # 标题区域
        title_widget = self.create_title_section()
        main_layout.addWidget(title_widget)
        
        # 内容区域
        content_widget = self.create_content_section()
        main_layout.addWidget(content_widget)
        
        # 底部标签区域
        tags_widget = self.create_tags_section()
        main_layout.addWidget(tags_widget)
        
        # 移除这一行，不再添加底部额外空间
        # main_layout.addStretch()
    
    def paintEvent(self, event):
        """重写绘制事件，手动绘制边框"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        
        # 设置画笔
        pen = QPen()
        if self.hovered:
            pen.setColor(QColor("#9ca3af"))  # 悬停时深灰色
            pen.setWidth(2)
        else:
            pen.setColor(QColor("#e0e0e0"))  # 默认浅灰色
            pen.setWidth(1)
        
        painter.setPen(pen)
        painter.setBrush(Qt.NoBrush)  # 不填充
        
        # 绘制圆角矩形
        rect = self.rect().adjusted(1, 1, -1, -1)  # 调整绘制区域，避免边缘被裁剪
        painter.drawRoundedRect(rect, 6, 6)
        
    def create_top_section(self):
        """创建顶部区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 左侧分类信息 - 删除"分类:"前缀，只显示分类名称
        category_label = QLabel(getattr(self.prompt, 'category', '通用'))
        category_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        ''')
        layout.addWidget(category_label)
        
        layout.addStretch()
        
        # 右侧操作按钮组
        buttons_widget = self.create_action_buttons()
        layout.addWidget(buttons_widget)
        
        return widget
        
    def create_action_buttons(self):
        """创建操作按钮组"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 定义按钮图标SVG
        button_icons = {
            'favorite': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'copy': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" stroke-width="2"/>
            </svg>''',
            'edit': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'history': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'delete': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
        button_tooltips = {
            'favorite': '收藏',
            'pin': '置顶', 
            'copy': '复制',
            'edit': '编辑',
            'history': '历史',
            'delete': '删除'
        }
        
        for key, svg in button_icons.items():
            btn = self.create_small_icon_button(svg, button_tooltips[key])
            layout.addWidget(btn)
            
        return widget
        
    def create_small_icon_button(self, svg_content, tooltip):
        """创建小图标按钮"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
        # 设置图标
        self.set_button_icon(btn, svg_content, "#6B7280")
        
        # 悬停效果
        def on_enter():
            self.set_button_icon(btn, svg_content, "#3B82F6")
        def on_leave():
            self.set_button_icon(btn, svg_content, "#6B7280")
            
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        
        return btn
        
    def set_button_icon(self, button, svg_content, color):
        """设置按钮图标"""
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(12, 12)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(12, 12))
        
    def create_title_section(self):
        """创建标题区域"""
        title_label = QLabel(self.prompt.title)
        title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)  # 最多显示2行标题
        return title_label
        
    def create_content_section(self):
        """创建内容区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 检查是否有媒体文件（这里简化处理，假设根据内容长度判断）
        has_media = hasattr(self.prompt, 'has_media') and self.prompt.has_media
        
        if has_media:
            # 状态B：带媒体布局
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(8)
            
            # 左侧缩略图
            thumbnail = QLabel()
            thumbnail.setFixedSize(40, 40)
            thumbnail.setStyleSheet('''
                QLabel {
                    background-color: #F3F4F6;
                    border: 1px solid #E5E7EB;
                    border-radius: 4px;
                }
            ''')
            thumbnail.setAlignment(Qt.AlignCenter)
            thumbnail.setText("📷")  # 占位符
            layout.addWidget(thumbnail)
            
            # 右侧内容预览
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        else:
            # 状态A：纯文本布局
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        return widget
        
    def create_content_preview(self):
        """创建内容预览标签"""
        # 截取内容前100个字符作为预览
        preview_text = self.prompt.content[:100] + "..." if len(self.prompt.content) > 100 else self.prompt.content
        
        content_label = QLabel(preview_text)
        content_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                line-height: 1.4;
                background: transparent;
                border: none;
            }
        ''')
        content_label.setWordWrap(True)
        content_label.setMaximumHeight(60)  # 最多显示3行内容
        content_label.setAlignment(Qt.AlignTop)
        
        return content_label
        
    def create_tags_section(self):
        """创建标签区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 使用FlowLayout代替HBoxLayout以实现标签自动换行
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 获取标签 - 确保标签正确显示
        tags = []
        
        if hasattr(self.prompt, 'tags'):
            if self.prompt.tags is not None:
                # 如果是字符串，按逗号分割
                if isinstance(self.prompt.tags, str):
                    tags = [tag.strip() for tag in self.prompt.tags.split(',') if tag.strip()]
                else:
                    # 确保是列表类型
                    tags = list(self.prompt.tags)
                    
        # 仅当tags完全不存在或为None时才使用默认标签
        if not tags and not hasattr(self.prompt, 'tags'):
            tags = ['AI助手', '提示词', '创意']
        
        # 最多显示5个标签
        tag_count = 0
        for tag in tags:
            if tag_count >= 5:
                break
                
            tag_label = self.create_tag_label(tag)
            layout.addWidget(tag_label)
            tag_count += 1
            
        # 如果有更多标签，显示+N标签
        if len(tags) > 5:
            more_label = self.create_more_tags_label(len(tags) - 5)
            layout.addWidget(more_label)
            
        layout.addStretch()
        return widget
        
    def create_tag_label(self, tag_text):
        """创建单个标签"""
        label = QLabel(tag_text)
        label.setStyleSheet('''
            QLabel {
                background-color: #EBF4FF;
                color: #1E40AF;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        ''')
        return label
        
    def create_more_tags_label(self, count):
        """创建显示更多标签的标签"""
        label = QLabel(f"+{count}")
        label.setStyleSheet('''
            QLabel {
                background-color: #F3F4F6;
                color: #6B7280;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        ''')
        return label
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.hovered = True
        self.update()  # 触发重绘
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.hovered = False
        self.update()  # 触发重绘
        super().leaveEvent(event)

class FilterToggleButton(QPushButton):
    """筛选切换按钮 - 通用筛选按钮类"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class ViewToggleButton(QPushButton):
    """视图切换按钮 - 28x28px尺寸"""
    
    def __init__(self, svg_content, tooltip, view_type, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.view_type = view_type
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class SvgIconButton(QPushButton):
    """自定义SVG图标按钮"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        self.setup_shadow()
        
        # 设置按钮样式
        self.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
    def update_icon(self):
        """更新图标状态"""
        # 根据状态选择颜色 - 避免白色系，确保在深色背景下清晰可见
        if self.is_active:
            color = "#3B82F6"  # 激活状态：蓝色
        else:
            color = "#6B7280"  # 默认状态：中灰色
            
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(24, 24))
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(8)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：蓝色阴影，更明显
            self.shadow_effect.setBlurRadius(12)
            self.shadow_effect.setYOffset(3)
            self.shadow_effect.setColor(QColor(59, 130, 246, 100))  # 蓝色阴影
        elif state == "hover":
            # 悬停状态：稍微增强的阴影
            self.shadow_effect.setBlurRadius(10)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 80))  # 深一点的阴影
        else:
            # 默认状态：轻微阴影
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(6)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 40))  # 默认轻微阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：蓝色阴影，更明显
            self.shadow_effect.setBlurRadius(10)
            self.shadow_effect.setYOffset(3)
            self.shadow_effect.setColor(QColor(59, 130, 246, 80))  # 蓝色阴影
        elif state == "hover":
            # 悬停状态：稍微增强的阴影
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 深一点的阴影
        else:
            # 默认状态：轻微阴影
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 40))  # 默认阴影
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_icon()
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态：亮灰色，避免白色系
            svg_data = self.svg_content.replace("currentColor", "#9CA3AF")
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            self.setIcon(QIcon(pixmap))
            self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon()
        if self.is_active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        super().leaveEvent(event)

class NavigationBar(QWidget):
    '''左侧导航栏组件 (60x800)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(60, 800)
        self.current_page = "home"
        self.nav_buttons = {}
        self.init_ui()
        
    def get_svg_icons(self):
        """获取SVG图标定义"""
        return {
            "home": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "trash": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "settings": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "help": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: #1F2937;
            }
        ''')
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(0)
        
        # Logo区域
        logo_label = QLabel("PA")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet('''
            QLabel {
                color: #3B82F6;
                font-size: 18px;
                font-weight: bold;
                background-color: #374151;
                border-radius: 8px;
                padding: 8px;
                margin: 0 10px 30px 10px;
            }
        ''')
        layout.addWidget(logo_label)
        
        # 主要导航按钮区域（居中显示）
        main_nav_layout = QVBoxLayout()
        main_nav_layout.setSpacing(15)
        main_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 主要导航项（首页和回收站）
        main_nav_items = [
            ("home", "首页"),
            ("trash", "回收站")
        ]
        
        svg_icons = self.get_svg_icons()
        
        for key, tooltip in main_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            main_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(main_nav_layout)
        layout.addStretch()
        
        # 底部功能按钮区域
        bottom_nav_layout = QVBoxLayout()
        bottom_nav_layout.setSpacing(15)
        bottom_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 底部导航项（设置和帮助）
        bottom_nav_items = [
            ("settings", "设置"),
            ("help", "帮助")
        ]
        
        for key, tooltip in bottom_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            bottom_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(bottom_nav_layout)
        
        # 设置默认选中状态
        self.set_active_button("home")
        
    def create_svg_button(self, svg_content, tooltip, key):
        """创建SVG图标按钮"""
        btn = SvgIconButton(svg_content, tooltip, key, self)
        btn.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
        ''')
        btn.clicked.connect(lambda: self.on_nav_clicked(key))
        return btn
            
    def on_nav_clicked(self, key):
        """导航按钮点击事件"""
        self.set_active_button(key)
        if self.parent_window:
            self.parent_window.switch_page(key)
            
    def set_active_button(self, key):
        """设置活动按钮"""
        self.current_page = key
        for btn_key, btn in self.nav_buttons.items():
            is_active = btn_key == key
            btn.set_active(is_active)

class StatusBar(QWidget):
    '''底部状态栏组件 (400x32)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 32)  # 调整高度从50px到32px，更符合移动端设计规范
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: #F3F4F6;
                border-top: 1px solid #E5E7EB;
            }
        ''')
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)
        layout.setAlignment(Qt.AlignVCenter)  # 设置整个布局垂直居中
        
        # 状态信息标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        ''')
        self.status_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 右侧信息
        self.info_label = QLabel("")
        self.info_label.setStyleSheet('''
            QLabel {
                color: #9CA3AF;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        ''')
        self.info_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.info_label)
        
    def set_status(self, message):
        """设置状态信息"""
        self.status_label.setText(message)
        
    def set_info(self, info):
        """设置右侧信息"""
        self.info_label.setText(info)

class ContentArea(QWidget):
    '''内容显示区域 (400x718)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_view = "card"  # 默认为卡片视图
        self.all_prompts = []  # 存储从数据库加载的、未经筛选的完整提示词列表
        self.active_filters = {'favorite': False, 'category': '全部', 'tags': set()}  # 跟踪当前激活的筛选器，tags改为set支持多选
        self.category_buttons = {}  # 存储分类按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用
        
        # 激活状态的实心星形图标
        self.favorite_active_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        self.tag_buttons = {}  # 存储标签按钮的引用
        self.setFixedSize(400, 718)  # 调整高度从700px到718px，补偿状态栏减少的18px
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: white;
            }
        ''')
        
        # 使用堆叠布局来切换不同页面
        self.stacked_widget = QStackedWidget()
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stacked_widget)
        
        # 创建各个页面
        self.create_pages()
        
    def create_pages(self):
        """创建各个页面"""
        # 首页
        self.home_page = self.create_home_page()
        self.stacked_widget.addWidget(self.home_page)
        
        # 回收站页面
        self.trash_page = self.create_trash_page()
        self.stacked_widget.addWidget(self.trash_page)
        
        # 设置页面
        self.settings_page = self.create_settings_page()
        self.stacked_widget.addWidget(self.settings_page)
        
        # 帮助页面
        self.help_page = self.create_help_page()
        self.stacked_widget.addWidget(self.help_page)
        
    def create_home_page(self):
        """创建首页"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 8, 15, 15)  # 减少顶部间距从15px到8px
        layout.setSpacing(12)  # 减少整体间距从15px到12px
        
        # 搜索框和新建按钮区域 - 调整到内容区域顶部
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索提示词...")
        self.search_input.setStyleSheet('''
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        ''')
        search_layout.addWidget(self.search_input)
        
        # 创建方形SVG图标按钮替换文本按钮
        add_btn = self.create_add_button()
        search_layout.addWidget(add_btn)
        
        layout.addLayout(search_layout)
        
        # 工具栏
        toolbar_widget = self.create_toolbar()
        layout.addWidget(toolbar_widget)
        
        # 分类筛选栏容器（默认隐藏）
        self.category_filter_bar = QWidget()
        self.category_filter_bar.setFixedHeight(50)
        self.category_filter_bar.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 0px;
            }
        ''')
        
        # 为分类筛选栏设置水平布局
        self.category_filter_layout = QHBoxLayout(self.category_filter_bar)
        self.category_filter_layout.setContentsMargins(12, 8, 12, 8)
        self.category_filter_layout.setSpacing(8)
        
        # 添加分类筛选栏到布局
        layout.addWidget(self.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.tag_filter_area = QScrollArea()
        self.tag_filter_area.setFixedHeight(50)
        self.tag_filter_area.setWidgetResizable(True)
        self.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tag_filter_area.setStyleSheet('''
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
        ''')
        self.tag_filter_area.hide()
        
        # 标签筛选栏内容容器
        tag_filter_widget = QWidget()
        self.tag_filter_layout = QHBoxLayout(tag_filter_widget)
        self.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.tag_filter_layout.setSpacing(6)
        self.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.tag_filter_area)
        
        # 初始状态下隐藏分类筛选栏
        self.category_filter_bar.hide()
        
        # 创建堆叠容器来切换列表视图和卡片视图
        self.view_stack = QStackedWidget()
        
        # 列表视图
        self.prompt_list = QListWidget()
        self.prompt_list.setStyleSheet('''
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #F9FAFB;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
        ''')
        self.view_stack.addWidget(self.prompt_list)
        
        # 卡片视图
        self.card_view = self.create_card_view()
        self.view_stack.addWidget(self.card_view)
        
        # 默认显示卡片视图
        self.view_stack.setCurrentIndex(1)
        
        layout.addWidget(self.view_stack)
        
        return page
        
    def create_trash_page(self):
        """创建回收站页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("回收站")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }
        ''')
        layout.addWidget(title)
        
        # 空状态提示
        empty_label = QLabel("回收站为空")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet('''
            QLabel {
                color: #9CA3AF;
                font-size: 14px;
                padding: 40px;
            }
        ''')
        layout.addWidget(empty_label)
        layout.addStretch()
        
        return page
        
    def create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        title = QLabel("设置")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
            }
        ''')
        layout.addWidget(title)
        
        # 设置项
        settings_items = [
            ("主题设置", "选择应用主题"),
            ("存储设置", "配置数据存储方式"),
            ("导入导出", "管理数据备份"),
            ("关于应用", "查看版本信息")
        ]
        
        for title_text, desc_text in settings_items:
            item_widget = self.create_setting_item(title_text, desc_text)
            layout.addWidget(item_widget)
            
        layout.addStretch()
        
        return page
        
    def create_setting_item(self, title, description):
        """创建设置项"""
        widget = QFrame()
        widget.setStyleSheet('''
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 12px;
            }
            QFrame:hover {
                background-color: #F3F4F6;
            }
        ''')
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        
        title_label = QLabel(title)
        title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        ''')
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return widget
        
    def create_help_page(self):
        """创建帮助页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("帮助")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }
        ''')
        layout.addWidget(title)
        
        help_text = QLabel("""
        
        """)
        help_text.setStyleSheet('''
            QLabel {
                color: #374151;
                font-size: 13px;
                line-height: 1.5;
                background: transparent;
                border: none;
            }
        ''')
        help_text.setWordWrap(True)
        layout.addWidget(help_text)
        layout.addStretch()
        
        return page
        
    def switch_to_page(self, page_key):
        """切换到指定页面"""
        page_map = {
            "home": 0,
            "trash": 1,
            "settings": 2,
            "help": 3
        }
        
        if page_key in page_map:
            self.stacked_widget.setCurrentIndex(page_map[page_key])
            
    def create_toolbar(self):
        """创建工具栏 - 无边框设计"""
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(40)
        toolbar_widget.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border-radius: 6px;
                border: none;
            }
        ''')
        
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(12, 0, 12, 0)
        toolbar_layout.setSpacing(8)
        
        # 左侧工具栏标识
        toolbar_label = QLabel("工具栏")
        toolbar_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        ''')
        toolbar_layout.addWidget(toolbar_label)
        
        # 添加筛选按钮组
        filter_buttons_layout = QHBoxLayout()
        filter_buttons_layout.setSpacing(4)
        
        # 准备 SVG 图标内容
        filter_icons = self.get_filter_icons()
        
        # 创建三个筛选按钮
        self.favorite_filter_btn = FilterToggleButton(
            filter_icons['favorite'], "收藏筛选", "favorite", self
        )
        self.favorite_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("favorite"))
        filter_buttons_layout.addWidget(self.favorite_filter_btn)
        
        self.category_filter_btn = FilterToggleButton(
            filter_icons['category'], "分类筛选", "category", self
        )
        self.category_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("category"))
        filter_buttons_layout.addWidget(self.category_filter_btn)
        
        self.tag_filter_btn = FilterToggleButton(
            filter_icons['tag'], "标签筛选", "tag", self
        )
        self.tag_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("tag"))
        filter_buttons_layout.addWidget(self.tag_filter_btn)
        
        toolbar_layout.addLayout(filter_buttons_layout)
        toolbar_layout.addStretch()
        
        # 右侧单个视图切换按钮
        self.view_toggle_btn = self.create_single_view_toggle_button()
        toolbar_layout.addWidget(self.view_toggle_btn)
        
        return toolbar_widget
    
    def create_single_view_toggle_button(self):
        """创建单个视图切换按钮"""
        # 卡片视图图标（默认显示）
        card_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 列表视图图标
        list_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 创建按钮，存储两个图标
        btn = QPushButton()
        btn.setFixedSize(32, 28)
        btn.card_svg = card_svg
        btn.list_svg = list_svg
        btn.current_view = "card"  # 默认卡片视图
        
        # 设置初始样式和图标
        self.update_toggle_button_style(btn)
        
        # 连接点击事件
        btn.clicked.connect(self.toggle_view)
        
        return btn
    
    def toggle_view(self):
        """切换视图（循环切换）"""
        # 切换视图类型
        if self.current_view == "card":
            self.current_view = "list"
        else:
            self.current_view = "card"
        
        # 更新按钮显示
        self.update_toggle_button_style(self.view_toggle_btn)
        
        # 切换显示的视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if self.current_view == 'card' else '列表'}视图")
    
    def create_card_view(self):
        """创建卡片视图容器 - 一行一个卡片，自适应宽度"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)     # 启用垂直滚动条
        scroll_area.setStyleSheet('''
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
            }
            QScrollBar:vertical {
                background-color: #F3F4F6;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #9CA3AF;
            }
        ''')
        
        # 创建卡片容器
        self.card_container = QWidget()
        self.card_layout = QVBoxLayout(self.card_container)
        self.card_layout.setContentsMargins(15, 15, 15, 15)  # 外部容器边距
        self.card_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.card_layout.setAlignment(Qt.AlignTop)
        
        # 创建垂直布局来放置卡片（一行一个）
        self.cards_list_widget = QWidget()
        self.cards_list_layout = QVBoxLayout(self.cards_list_widget)
        self.cards_list_layout.setContentsMargins(0, 0, 0, 0)
        self.cards_list_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.cards_list_layout.setAlignment(Qt.AlignTop)
        
        self.card_layout.addWidget(self.cards_list_widget)
        self.card_layout.addStretch()
        
        scroll_area.setWidget(self.card_container)
        return scroll_area

    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        self.update_view_buttons()
        
        # 切换显示的视图
        if view_type == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if view_type == 'card' else '列表'}视图")
    
    def update_toggle_button_style(self, btn):
        """更新切换按钮样式和图标"""
        # 根据当前视图选择图标和提示文本
        if self.current_view == "card":
            svg_content = btn.card_svg
            tooltip = "当前：卡片视图 (点击切换到列表视图)"
            bg_color = "#3B82F6"  # 蓝色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#3B82F6"
        else:
            svg_content = btn.list_svg
            tooltip = "当前：列表视图 (点击切换到卡片视图)"
            bg_color = "#10B981"  # 绿色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#10B981"
        
        # 设置按钮样式
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 6px;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.current_view == "card" else "#059669"};
                border-color: {"#2563EB" if self.current_view == "card" else "#059669"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.current_view == "card" else "#047857"};
            }}
        """)
        
        # 设置工具提示
        btn.setToolTip(tooltip)
        
        # 更新图标
        self.set_button_svg_icon(btn, svg_content, icon_color)
    
    def set_button_svg_icon(self, button, svg_content, color):
        """设置按钮SVG图标"""
        from PySide6.QtSvg import QSvgRenderer
        from PySide6.QtGui import QPixmap, QPainter, QIcon
        
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(16, 16))

    def update_card_view(self, prompts):
        """更新卡片视图显示 - 一行一个卡片"""
        # 清除现有卡片
        if hasattr(self, 'cards_list_layout'):
            for i in reversed(range(self.cards_list_layout.count())):
                child = self.cards_list_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
        
        # 创建卡片，每行一个
        for prompt in prompts:
            # 创建卡片外围容器 - 用于实现边框效果
            container = QFrame()
            container.setFrameShape(QFrame.Box)
            container.setFrameShadow(QFrame.Plain)
            container.setLineWidth(1)
            # 使用中性色边框
            container.setStyleSheet('''
                QFrame {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    padding: 0px;
                    background-color: white;
                }
                QFrame:hover {
                    border: 1px solid #9ca3af;
                }
            ''')
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
            container_layout.setSpacing(0)
            
            # 创建卡片内容
            card = PromptCardWidget(prompt, self)
            # 卡片自身不显示边框
            card.setStyleSheet("QFrame { border: none; background-color: white; }")
            card.setFrameShape(QFrame.NoFrame)
            container_layout.addWidget(card)
            
            # 添加到卡片列表布局
            self.cards_list_layout.addWidget(container)

    def update_view_buttons(self):
        """更新视图按钮状态"""
        self.card_view_btn.set_active(self.current_view == "card")
        self.list_view_btn.set_active(self.current_view == "list")

    def create_add_button(self):
        """创建方形SVG新建按钮"""
        # 定义新建按钮的SVG图标
        add_svg = '''<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 12H19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        btn = AddIconButton(add_svg, "新建提示词", self)
        btn.clicked.connect(self.add_prompt)
        return btn
        
    def add_prompt(self):
        """添加新提示词"""
        if self.parent_window:
            self.parent_window.status_bar.set_status("打开新建提示词对话框...")
            
            # 创建并显示新建提示词对话框
            dialog = CreatePromptDialog(self.parent_window)
            result = dialog.exec()
            
            if result == 1:  # QDialog.Accepted 的值是 1
                self.parent_window.status_bar.set_status("提示词创建成功")
                # 刷新提示词列表
                self.refresh_prompt_list()
            else:
                self.parent_window.status_bar.set_status("取消创建提示词")
                
    def get_filter_icons(self):
        """获取筛选按钮的 SVG 图标"""
        return {
            'favorite': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'tag': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0136 20.9135 12.7709 21.0141C12.5282 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4818 21.1148 11.2391 21.0141C10.9964 20.9135 10.7757 20.766 10.59 20.58L2 12V2H12L20.59 10.59C20.9625 10.9647 21.1716 11.4716 21.1716 12C21.1716 12.5284 20.9625 13.0353 20.59 13.41V13.41Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 7H7.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
    
    def apply_filters_and_update_views(self):
        """应用筛选并更新视图 - 所有筛选逻辑的中心"""
        # 从完整列表创建副本
        filtered_prompts = self.all_prompts[:]
        
        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选（多选支持）
        if self.active_filters.get('tags') and len(self.active_filters['tags']) > 0:
            selected_tags = self.active_filters['tags']
            filtered_prompts = [prompt for prompt in filtered_prompts
                              if hasattr(prompt, 'tags') and prompt.tags and 
                              selected_tags.issubset(set(prompt.tags))]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选（多选支持）
        if self.active_filters.get('tags') and len(self.active_filters['tags']) > 0:
            selected_tags = self.active_filters['tags']
            filtered_prompts = [prompt for prompt in filtered_prompts
                              if hasattr(prompt, 'tags') and prompt.tags and 
                              selected_tags.issubset(set(prompt.tags))]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if self.active_filters.get('favorite', False):
                self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
            else:
                self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")




    def on_category_button_clicked(self, category_name):
        """分类按钮点击处理器"""
        # 更新分类筛选状态
        self.active_filters['category'] = category_name
        
        # 更新按钮状态
        self.update_category_button_states()
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def update_category_button_states(self):
        """更新分类按钮的激活状态"""
        current_category = self.active_filters.get('category', '全部')
        
        for category_name, button in self.category_buttons.items():
            if category_name == current_category:
                # 激活状态样式
                button.setStyleSheet('''
                    QPushButton {
                        background-color: #3B82F6;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-size: 12px;
                        margin: 2px;
                    }
                    QPushButton:hover {
                        background-color: #2563EB;
                    }
                ''')
            else:
                # 默认状态样式
                button.setStyleSheet('''
                    QPushButton {
                        background-color: #F3F4F6;
                        color: #374151;
                        border: 1px solid #D1D5DB;
                        border-radius: 4px;
                        padding: 6px 12px;
                        font-size: 12px;
                        margin: 2px;
                    }
                    QPushButton:hover {
                        background-color: #E5E7EB;
                        border-color: #9CA3AF;
                    }
                ''')

    def populate_tag_filters(self):
        """动态填充标签筛选按钮"""
        
        # 清除现有按钮和引用
        self.tag_buttons.clear()
        for i in reversed(range(self.tag_filter_layout.count())):
            child = self.tag_filter_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 从所有提示词中提取唯一标签
        all_tags = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'tags') and prompt.tags:
                for tag in prompt.tags:
                    if tag and tag.strip():
                        all_tags.add(tag.strip())
        
        # 转换为排序列表
        unique_tags = sorted(list(all_tags))
        
        # 添加"全部"按钮
        all_button = QPushButton("全部")
        all_button.clicked.connect(lambda: self.on_tag_button_clicked("全部"))
        self.tag_buttons["全部"] = all_button
        self.tag_filter_layout.addWidget(all_button)
        
        # 添加各标签按钮
        for tag in unique_tags:
            button = QPushButton(tag)
            button.clicked.connect(lambda checked, t=tag: self.on_tag_button_clicked(t))
            self.tag_buttons[tag] = button
            self.tag_filter_layout.addWidget(button)
        
        # 添加弹性空间
        self.tag_filter_layout.addStretch()
        
        # 更新按钮状态
        self.update_tag_button_states()

    def on_tag_button_clicked(self, tag_name):
        """标签按钮点击处理器（支持多选）"""
        if tag_name == '全部':
            # 点击"全部"按钮，清空所有选中的标签
            self.active_filters['tags'].clear()
        else:
            # 处理普通标签按钮的多选逻辑
            if tag_name in self.active_filters['tags']:
                # 如果标签已选中，则取消选择
                self.active_filters['tags'].remove(tag_name)
            else:
                # 如果标签未选中，则添加到选中集合
                self.active_filters['tags'].add(tag_name)
        
        # 更新按钮状态
        self.update_tag_button_states()
        
        # 更新按钮状态
        self.update_tag_button_states()
        
        # 应用筛选并更新视图
        self.apply_filters_and_update_views()

    def get_active_filter_button_style(self):
        """获取激活状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #0EA5E9;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0284C7;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''

    def get_filter_button_style(self):
        """获取默认状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #F0F9FF;
                color: #0C4A6E;
                border: 1px solid #BAE6FD;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #E0F2FE;
                border-color: #7DD3FC;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''


    def get_active_filter_button_style(self):
        """获取激活状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #0EA5E9;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0284C7;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''

    def get_filter_button_style(self):
        """获取默认状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #F0F9FF;
                color: #0C4A6E;
                border: 1px solid #BAE6FD;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #E0F2FE;
                border-color: #7DD3FC;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''


    def get_active_filter_button_style(self):
        """获取激活状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #0EA5E9;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #0284C7;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''
        
    def get_filter_button_style(self):
        """获取默认状态的筛选按钮样式"""
        return '''
            QPushButton {
                background-color: #F0F9FF;
                color: #0C4A6E;
                border: 1px solid #BAE6FD;
                border-radius: 4px;
                padding: 6px 12px;
                font-size: 12px;
                margin: 2px;
            }
            QPushButton:hover {
                background-color: #E0F2FE;
                border-color: #7DD3FC;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        '''
        
        def update_tag_button_states(self):
        """更新标签按钮的激活状态（支持多选）"""
        selected_tags = self.active_filters.get('tags', set())
        
        # 更新所有标签按钮的状态
        for tag_name, button in self.tag_buttons.items():
            if tag_name == '全部':
                # "全部"按钮：当没有其他标签选中时激活
                if len(selected_tags) == 0:
                    button.setStyleSheet(get_active_filter_button_style())
                else:
                    button.setStyleSheet(get_filter_button_style())
            else:
                # 普通标签按钮：根据是否在选中集合中设置状态
                if tag_name in selected_tags:
                    button.setStyleSheet(get_active_filter_button_style())
                else:
                    button.setStyleSheet(get_filter_button_style())
def get_active_filter_button_style(self):
    """Get active filter button style"""
    return '''
        QPushButton {
            background-color: #0EA5E9;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        QPushButton:hover {
            background-color: #0284C7;
        }
        QToolTip {
            background-color: #374151;
            color: white;
            border: 1px solid #4B5563;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }
    '''



def get_filter_button_style(self):
    """Get 
        """刷新提示词列表"""
        if self.parent_window:
            self.parent_window.load_data()
        if self.parent_window:
            self.parent_window.load_data()

class PromptAssistantRedesigned(QMainWindow):
    """重构版主窗口"""
    
    def __init__(self):
        super().__init__()
        self.model = PromptModel("sqlite")
        self.init_ui()
        self.load_data()
        
    def init_ui(self):
        # 设置窗口属性
        self.setWindowTitle("Prompt收藏助手")
        self.setFixedSize(460, 800)
        self.setWindowFlags(Qt.FramelessWindowHint)  # 无标题栏
        
        # 设置窗口样式 - 移除圆角，改为直角边框
        self.setStyleSheet('''
            QMainWindow {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 0px;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 (水平布局)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.nav_bar = NavigationBar(self)
        main_layout.addWidget(self.nav_bar)
        
        # 右侧主区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 自定义标题栏
        self.title_bar = CustomTitleBar(self)
        right_layout.addWidget(self.title_bar)
        
        # 内容区域
        self.content_area = ContentArea(self)
        right_layout.addWidget(self.content_area)
        
        # 状态栏
        self.status_bar = StatusBar(self)
        right_layout.addWidget(self.status_bar)
        
        main_layout.addWidget(right_widget)
        
        # 居中显示窗口
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def switch_page(self, page_key):
        """切换页面"""
        self.content_area.switch_to_page(page_key)
        
        # 更新标题栏标题
        titles = {
            "home": "我的提示词",
            "trash": "回收站",
            "settings": "设置",
            "help": "帮助"
        }
        
        if page_key in titles:
            self.title_bar.set_title(titles[page_key])
            self.status_bar.set_status(f"已切换到{titles[page_key]}")
            
    def load_data(self):
        """加载数据"""
        try:
            prompts = self.model.get_all_prompts()
            # 将数据传递给 ContentArea 并应用筛选
            self.content_area.all_prompts = prompts
            # 填充分类筛选按钮
            self.content_area.populate_category_filters()
            self.content_area.populate_tag_filters()
        except Exception as e:
            print(f"加载标签筛选时出错: {e}")
            self.content_area.apply_filters_and_update_views()
            self.status_bar.set_status("数据加载完成")
        except Exception as e:
            self.status_bar.set_status(f"加载失败: {str(e)}")
            
    def update_prompt_list(self, prompts):
        """更新提示词列表显示"""
        if hasattr(self.content_area, 'prompt_list'):
            # 更新列表视图
            self.content_area.prompt_list.clear()
            for prompt in prompts:
                item_text = f"{prompt.title}\n{prompt.content[:50]}..." if len(prompt.content) > 50 else f"{prompt.title}\n{prompt.content}"
                self.content_area.prompt_list.addItem(item_text)
            
            # 更新卡片视图
            try:
                if hasattr(self.content_area, 'update_card_view') and hasattr(self.content_area, 'cards_list_layout'):
                    self.content_area.update_card_view(prompts)
            except Exception as e:
                import traceback
                print(f"更新卡片视图时出错: {e}")
                traceback.print_exc()
            
    def refresh_prompt_list(self):
        """刷新提示词列表"""
        self.load_data()
        self.status_bar.set_status("提示词列表已刷新")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = PromptAssistantRedesigned()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QPushButton, QLabel, QFrame, 
                               QStackedWidget, QListWidget, QTextEdit, 
                               QLineEdit, QScrollArea, QMessageBox, QGraphicsDropShadowEffect,
                               QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QSize, QPoint
from PySide6.QtGui import QIcon, QPalette, QColor, QFont, QPixmap, QPainter, QPen
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QGraphicsDropShadowEffect
from model import PromptModel, Prompt
from pathlib import Path
from create_prompt_dialog import CreatePromptDialog
from tag_filter_styles import get_active_filter_button_style, get_filter_button_style

class CustomTitleBar(QWidget):
    '''自定义标题栏组件 (400x50)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(400, 50)
        self.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        ''')
        self.init_ui()
        
        # 用于拖拽窗口
        self.drag_position = QPoint()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 0, 10, 0)
        layout.setSpacing(10)
        
        # 标题标签
        self.title_label = QLabel("Prompt收藏助手")
        self.title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        layout.addWidget(self.title_label)
        
        layout.addStretch()
        
        # 窗口控制按钮
        self.create_control_buttons(layout)
        
    def create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: #6B7280;
                font-size: 16px;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setStyleSheet(button_style)
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        close_style = button_style.replace("#E5E7EB", "#FEE2E2").replace("#D1D5DB", "#FECACA")
        close_style = close_style.replace("color: #6B7280;", "color: #DC2626;")
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet(close_style)
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def minimize_window(self):
        if self.parent_window:
            self.parent_window.showMinimized()
            
    def close_window(self):
        if self.parent_window:
            self.parent_window.close()
            
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)

class AddIconButton(QPushButton):
    """方形SVG新建按钮 - 三种状态"""
    
    def __init__(self, svg_content, tooltip, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.setFixedSize(36, 36)  # 方形尺寸
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(4)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(1)
        self.shadow_effect.setColor(QColor(59, 130, 246, 40))  # 蓝色阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：更深的蓝色阴影
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(29, 78, 216, 80))
        elif state == "hover":
            # 悬停状态：增强的蓝色阴影
            self.shadow_effect.setBlurRadius(5)
            self.shadow_effect.setYOffset(1)
            self.shadow_effect.setColor(QColor(37, 99, 235, 60))
        else:
            # 默认状态：轻微蓝色阴影
            self.shadow_effect.setBlurRadius(4)
            self.shadow_effect.setYOffset(1)
            self.shadow_effect.setColor(QColor(59, 130, 246, 40))
        
    def update_icon(self, state="default"):
        """更新图标状态"""
        # 三种状态的颜色和背景
        if state == "active":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#1D4ED8"  # 深蓝色背景
        elif state == "hover":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#2563EB"  # 中蓝色背景
        else:
            color = "#FFFFFF"  # 白色图标
            bg_color = "#3B82F6"  # 默认蓝色背景
            
        # 设置按钮样式
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: none;
                border-radius: 6px;
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(20, 20))
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.update_icon("hover")
        self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon("default")
        self.update_shadow("default")
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("active")
            self.update_shadow("active")
        super().mousePressEvent(event)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("hover")
            self.update_shadow("hover")
        super().mouseReleaseEvent(event)

class PromptCardWidget(QFrame):
    """提示词卡片组件 - 自适应宽度，高度自适应"""
    
    def __init__(self, prompt, parent=None):
        super().__init__(parent)
        self.prompt = prompt
        # 移除固定宽度，改为自适应宽度
        self.setMinimumHeight(120)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)  # 水平扩展，垂直最小
        
        # 设置基础框架样式
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        
        # 初始化鼠标悬停状态
        self.hovered = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI布局"""
        # 背景颜色设置
        self.setStyleSheet('''
            QFrame {
                background-color: white;
            }
        ''')
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 10, 12, 10)  # 保持左、上、右的内边距，减少底部内边距
        main_layout.setSpacing(8)
        
        # 顶部区域：分类 + 操作按钮
        top_widget = self.create_top_section()
        main_layout.addWidget(top_widget)
        
        # 标题区域
        title_widget = self.create_title_section()
        main_layout.addWidget(title_widget)
        
        # 内容区域
        content_widget = self.create_content_section()
        main_layout.addWidget(content_widget)
        
        # 底部标签区域
        tags_widget = self.create_tags_section()
        main_layout.addWidget(tags_widget)
        
        # 移除这一行，不再添加底部额外空间
        # main_layout.addStretch()
    
    def paintEvent(self, event):
        """重写绘制事件，手动绘制边框"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        
        # 设置画笔
        pen = QPen()
        if self.hovered:
            pen.setColor(QColor("#9ca3af"))  # 悬停时深灰色
            pen.setWidth(2)
        else:
            pen.setColor(QColor("#e0e0e0"))  # 默认浅灰色
            pen.setWidth(1)
        
        painter.setPen(pen)
        painter.setBrush(Qt.NoBrush)  # 不填充
        
        # 绘制圆角矩形
        rect = self.rect().adjusted(1, 1, -1, -1)  # 调整绘制区域，避免边缘被裁剪
        painter.drawRoundedRect(rect, 6, 6)
        
    def create_top_section(self):
        """创建顶部区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 左侧分类信息 - 删除"分类:"前缀，只显示分类名称
        category_label = QLabel(getattr(self.prompt, 'category', '通用'))
        category_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        ''')
        layout.addWidget(category_label)
        
        layout.addStretch()
        
        # 右侧操作按钮组
        buttons_widget = self.create_action_buttons()
        layout.addWidget(buttons_widget)
        
        return widget
        
    def create_action_buttons(self):
        """创建操作按钮组"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 定义按钮图标SVG
        button_icons = {
            'favorite': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'copy': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" stroke-width="2"/>
            </svg>''',
            'edit': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'history': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'delete': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
        button_tooltips = {
            'favorite': '收藏',
            'pin': '置顶', 
            'copy': '复制',
            'edit': '编辑',
            'history': '历史',
            'delete': '删除'
        }
        
        for key, svg in button_icons.items():
            btn = self.create_small_icon_button(svg, button_tooltips[key])
            layout.addWidget(btn)
            
        return widget
        
    def create_small_icon_button(self, svg_content, tooltip):
        """创建小图标按钮"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
        # 设置图标
        self.set_button_icon(btn, svg_content, "#6B7280")
        
        # 悬停效果
        def on_enter():
            self.set_button_icon(btn, svg_content, "#3B82F6")
        def on_leave():
            self.set_button_icon(btn, svg_content, "#6B7280")
            
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        
        return btn
        
    def set_button_icon(self, button, svg_content, color):
        """设置按钮图标"""
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(12, 12)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(12, 12))
        
    def create_title_section(self):
        """创建标题区域"""
        title_label = QLabel(self.prompt.title)
        title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)  # 最多显示2行标题
        return title_label
        
    def create_content_section(self):
        """创建内容区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 检查是否有媒体文件（这里简化处理，假设根据内容长度判断）
        has_media = hasattr(self.prompt, 'has_media') and self.prompt.has_media
        
        if has_media:
            # 状态B：带媒体布局
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(8)
            
            # 左侧缩略图
            thumbnail = QLabel()
            thumbnail.setFixedSize(40, 40)
            thumbnail.setStyleSheet('''
                QLabel {
                    background-color: #F3F4F6;
                    border: 1px solid #E5E7EB;
                    border-radius: 4px;
                }
            ''')
            thumbnail.setAlignment(Qt.AlignCenter)
            thumbnail.setText("📷")  # 占位符
            layout.addWidget(thumbnail)
            
            # 右侧内容预览
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        else:
            # 状态A：纯文本布局
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        return widget
        
    def create_content_preview(self):
        """创建内容预览标签"""
        # 截取内容前100个字符作为预览
        preview_text = self.prompt.content[:100] + "..." if len(self.prompt.content) > 100 else self.prompt.content
        
        content_label = QLabel(preview_text)
        content_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                line-height: 1.4;
                background: transparent;
                border: none;
            }
        ''')
        content_label.setWordWrap(True)
        content_label.setMaximumHeight(60)  # 最多显示3行内容
        content_label.setAlignment(Qt.AlignTop)
        
        return content_label
        
    def create_tags_section(self):
        """创建标签区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 使用FlowLayout代替HBoxLayout以实现标签自动换行
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 获取标签 - 确保标签正确显示
        tags = []
        
        if hasattr(self.prompt, 'tags'):
            if self.prompt.tags is not None:
                # 如果是字符串，按逗号分割
                if isinstance(self.prompt.tags, str):
                    tags = [tag.strip() for tag in self.prompt.tags.split(',') if tag.strip()]
                else:
                    # 确保是列表类型
                    tags = list(self.prompt.tags)
                    
        # 仅当tags完全不存在或为None时才使用默认标签
        if not tags and not hasattr(self.prompt, 'tags'):
            tags = ['AI助手', '提示词', '创意']
        
        # 最多显示5个标签
        tag_count = 0
        for tag in tags:
            if tag_count >= 5:
                break
                
            tag_label = self.create_tag_label(tag)
            layout.addWidget(tag_label)
            tag_count += 1
            
        # 如果有更多标签，显示+N标签
        if len(tags) > 5:
            more_label = self.create_more_tags_label(len(tags) - 5)
            layout.addWidget(more_label)
            
        layout.addStretch()
        return widget
        
    def create_tag_label(self, tag_text):
        """创建单个标签"""
        label = QLabel(tag_text)
        label.setStyleSheet('''
            QLabel {
                background-color: #EBF4FF;
                color: #1E40AF;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        ''')
        return label
        
    def create_more_tags_label(self, count):
        """创建显示更多标签的标签"""
        label = QLabel(f"+{count}")
        label.setStyleSheet('''
            QLabel {
                background-color: #F3F4F6;
                color: #6B7280;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        ''')
        return label
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.hovered = True
        self.update()  # 触发重绘
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.hovered = False
        self.update()  # 触发重绘
        super().leaveEvent(event)

class FilterToggleButton(QPushButton):
    """筛选切换按钮 - 通用筛选按钮类"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class ViewToggleButton(QPushButton):
    """视图切换按钮 - 28x28px尺寸"""
    
    def __init__(self, svg_content, tooltip, view_type, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.view_type = view_type
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class SvgIconButton(QPushButton):
    """自定义SVG图标按钮"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        self.setup_shadow()
        
        # 设置按钮样式
        self.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
    def update_icon(self):
        """更新图标状态"""
        # 根据状态选择颜色 - 避免白色系，确保在深色背景下清晰可见
        if self.is_active:
            color = "#3B82F6"  # 激活状态：蓝色
        else:
            color = "#6B7280"  # 默认状态：中灰色
            
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(24, 24))
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(8)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：蓝色阴影，更明显
            self.shadow_effect.setBlurRadius(12)
            self.shadow_effect.setYOffset(3)
            self.shadow_effect.setColor(QColor(59, 130, 246, 100))  # 蓝色阴影
        elif state == "hover":
            # 悬停状态：稍微增强的阴影
            self.shadow_effect.setBlurRadius(10)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 80))  # 深一点的阴影
        else:
            # 默认状态：轻微阴影
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(6)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 40))  # 默认轻微阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：蓝色阴影，更明显
            self.shadow_effect.setBlurRadius(10)
            self.shadow_effect.setYOffset(3)
            self.shadow_effect.setColor(QColor(59, 130, 246, 80))  # 蓝色阴影
        elif state == "hover":
            # 悬停状态：稍微增强的阴影
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 深一点的阴影
        else:
            # 默认状态：轻微阴影
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 40))  # 默认阴影
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_icon()
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态：亮灰色，避免白色系
            svg_data = self.svg_content.replace("currentColor", "#9CA3AF")
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            self.setIcon(QIcon(pixmap))
            self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon()
        if self.is_active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        super().leaveEvent(event)

class NavigationBar(QWidget):
    '''左侧导航栏组件 (60x800)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(60, 800)
        self.current_page = "home"
        self.nav_buttons = {}
        self.init_ui()
        
    def get_svg_icons(self):
        """获取SVG图标定义"""
        return {
            "home": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "trash": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "settings": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "help": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: #1F2937;
            }
        ''')
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(0)
        
        # Logo区域
        logo_label = QLabel("PA")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet('''
            QLabel {
                color: #3B82F6;
                font-size: 18px;
                font-weight: bold;
                background-color: #374151;
                border-radius: 8px;
                padding: 8px;
                margin: 0 10px 30px 10px;
            }
        ''')
        layout.addWidget(logo_label)
        
        # 主要导航按钮区域（居中显示）
        main_nav_layout = QVBoxLayout()
        main_nav_layout.setSpacing(15)
        main_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 主要导航项（首页和回收站）
        main_nav_items = [
            ("home", "首页"),
            ("trash", "回收站")
        ]
        
        svg_icons = self.get_svg_icons()
        
        for key, tooltip in main_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            main_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(main_nav_layout)
        layout.addStretch()
        
        # 底部功能按钮区域
        bottom_nav_layout = QVBoxLayout()
        bottom_nav_layout.setSpacing(15)
        bottom_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 底部导航项（设置和帮助）
        bottom_nav_items = [
            ("settings", "设置"),
            ("help", "帮助")
        ]
        
        for key, tooltip in bottom_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            bottom_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(bottom_nav_layout)
        
        # 设置默认选中状态
        self.set_active_button("home")
        
    def create_svg_button(self, svg_content, tooltip, key):
        """创建SVG图标按钮"""
        btn = SvgIconButton(svg_content, tooltip, key, self)
        btn.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
        ''')
        btn.clicked.connect(lambda: self.on_nav_clicked(key))
        return btn
            
    def on_nav_clicked(self, key):
        """导航按钮点击事件"""
        self.set_active_button(key)
        if self.parent_window:
            self.parent_window.switch_page(key)
            
    def set_active_button(self, key):
        """设置活动按钮"""
        self.current_page = key
        for btn_key, btn in self.nav_buttons.items():
            is_active = btn_key == key
            btn.set_active(is_active)

class StatusBar(QWidget):
    '''底部状态栏组件 (400x32)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 32)  # 调整高度从50px到32px，更符合移动端设计规范
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: #F3F4F6;
                border-top: 1px solid #E5E7EB;
            }
        ''')
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)
        layout.setAlignment(Qt.AlignVCenter)  # 设置整个布局垂直居中
        
        # 状态信息标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        ''')
        self.status_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 右侧信息
        self.info_label = QLabel("")
        self.info_label.setStyleSheet('''
            QLabel {
                color: #9CA3AF;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        ''')
        self.info_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.info_label)
        
    def set_status(self, message):
        """设置状态信息"""
        self.status_label.setText(message)
        
    def set_info(self, info):
        """设置右侧信息"""
        self.info_label.setText(info)

class ContentArea(QWidget):
    '''内容显示区域 (400x718)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_view = "card"  # 默认为卡片视图
        self.all_prompts = []  # 存储从数据库加载的、未经筛选的完整提示词列表
        self.active_filters = {'favorite': False, 'category': '全部', 'tags': set()}  # 跟踪当前激活的筛选器，tags改为set支持多选
        self.category_buttons = {}  # 存储分类按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用
        self.setFixedSize(400, 718)  # 调整高度从700px到718px，补偿状态栏减少的18px
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: white;
            }
        ''')
        
        # 使用堆叠布局来切换不同页面
        self.stacked_widget = QStackedWidget()
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stacked_widget)
        
        # 创建各个页面
        self.create_pages()
        
    def create_pages(self):
        """创建各个页面"""
        # 首页
        self.home_page = self.create_home_page()
        self.stacked_widget.addWidget(self.home_page)
        
        # 回收站页面
        self.trash_page = self.create_trash_page()
        self.stacked_widget.addWidget(self.trash_page)
        
        # 设置页面
        self.settings_page = self.create_settings_page()
        self.stacked_widget.addWidget(self.settings_page)
        
        # 帮助页面
        self.help_page = self.create_help_page()
        self.stacked_widget.addWidget(self.help_page)
        
    def create_home_page(self):
        """创建首页"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 8, 15, 15)  # 减少顶部间距从15px到8px
        layout.setSpacing(12)  # 减少整体间距从15px到12px
        
        # 搜索框和新建按钮区域 - 调整到内容区域顶部
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索提示词...")
        self.search_input.setStyleSheet('''
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        ''')
        search_layout.addWidget(self.search_input)
        
        # 创建方形SVG图标按钮替换文本按钮
        add_btn = self.create_add_button()
        search_layout.addWidget(add_btn)
        
        layout.addLayout(search_layout)
        
        # 工具栏
        toolbar_widget = self.create_toolbar()
        layout.addWidget(toolbar_widget)
        
        # 分类筛选栏容器（默认隐藏）
        self.category_filter_bar = QWidget()
        self.category_filter_bar.setFixedHeight(50)
        self.category_filter_bar.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 0px;
            }
        ''')
        
        # 为分类筛选栏设置水平布局
        self.category_filter_layout = QHBoxLayout(self.category_filter_bar)
        self.category_filter_layout.setContentsMargins(12, 8, 12, 8)
        self.category_filter_layout.setSpacing(8)
        
        # 添加分类筛选栏到布局
        layout.addWidget(self.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.tag_filter_area = QScrollArea()
        self.tag_filter_area.setFixedHeight(50)
        self.tag_filter_area.setWidgetResizable(True)
        self.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tag_filter_area.setStyleSheet('''
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
        ''')
        self.tag_filter_area.hide()
        
        # 标签筛选栏内容容器
        tag_filter_widget = QWidget()
        self.tag_filter_layout = QHBoxLayout(tag_filter_widget)
        self.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.tag_filter_layout.setSpacing(6)
        self.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.tag_filter_area)
        
        # 初始状态下隐藏分类筛选栏
        self.category_filter_bar.hide()
        
        # 创建堆叠容器来切换列表视图和卡片视图
        self.view_stack = QStackedWidget()
        
        # 列表视图
        self.prompt_list = QListWidget()
        self.prompt_list.setStyleSheet('''
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #F9FAFB;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
        ''')
        self.view_stack.addWidget(self.prompt_list)
        
        # 卡片视图
        self.card_view = self.create_card_view()
        self.view_stack.addWidget(self.card_view)
        
        # 默认显示卡片视图
        self.view_stack.setCurrentIndex(1)
        
        layout.addWidget(self.view_stack)
        
        return page
        
    def create_trash_page(self):
        """创建回收站页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("回收站")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }
        ''')
        layout.addWidget(title)
        
        # 空状态提示
        empty_label = QLabel("回收站为空")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet('''
            QLabel {
                color: #9CA3AF;
                font-size: 14px;
                padding: 40px;
            }
        ''')
        layout.addWidget(empty_label)
        layout.addStretch()
        
        return page
        
    def create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        title = QLabel("设置")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
            }
        ''')
        layout.addWidget(title)
        
        # 设置项
        settings_items = [
            ("主题设置", "选择应用主题"),
            ("存储设置", "配置数据存储方式"),
            ("导入导出", "管理数据备份"),
            ("关于应用", "查看版本信息")
        ]
        
        for title_text, desc_text in settings_items:
            item_widget = self.create_setting_item(title_text, desc_text)
            layout.addWidget(item_widget)
            
        layout.addStretch()
        
        return page
        
    def create_setting_item(self, title, description):
        """创建设置项"""
        widget = QFrame()
        widget.setStyleSheet('''
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 12px;
            }
            QFrame:hover {
                background-color: #F3F4F6;
            }
        ''')
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        
        title_label = QLabel(title)
        title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        ''')
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return widget
        
    def create_help_page(self):
        """创建帮助页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("帮助")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }
        ''')
        layout.addWidget(title)
        
        help_text = QLabel("""
        
        """)
        help_text.setStyleSheet('''
            QLabel {
                color: #374151;
                font-size: 13px;
                line-height: 1.5;
                background: transparent;
                border: none;
            }
        ''')
        help_text.setWordWrap(True)
        layout.addWidget(help_text)
        layout.addStretch()
        
        return page
        
    def switch_to_page(self, page_key):
        """切换到指定页面"""
        page_map = {
            "home": 0,
            "trash": 1,
            "settings": 2,
            "help": 3
        }
        
        if page_key in page_map:
            self.stacked_widget.setCurrentIndex(page_map[page_key])
            
    def create_toolbar(self):
        """创建工具栏 - 无边框设计"""
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(40)
        toolbar_widget.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border-radius: 6px;
                border: none;
            }
        ''')
        
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(12, 0, 12, 0)
        toolbar_layout.setSpacing(8)
        
        # 左侧工具栏标识
        toolbar_label = QLabel("工具栏")
        toolbar_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        ''')
        toolbar_layout.addWidget(toolbar_label)
        
        # 添加筛选按钮组
        filter_buttons_layout = QHBoxLayout()
        filter_buttons_layout.setSpacing(4)
        
        # 准备 SVG 图标内容
        filter_icons = self.get_filter_icons()
        
        # 创建三个筛选按钮
        self.favorite_filter_btn = FilterToggleButton(
            filter_icons['favorite'], "收藏筛选", "favorite", self
        )
        self.favorite_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("favorite"))
        filter_buttons_layout.addWidget(self.favorite_filter_btn)
        
        self.category_filter_btn = FilterToggleButton(
            filter_icons['category'], "分类筛选", "category", self
        )
        self.category_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("category"))
        filter_buttons_layout.addWidget(self.category_filter_btn)
        
        self.tag_filter_btn = FilterToggleButton(
            filter_icons['tag'], "标签筛选", "tag", self
        )
        self.tag_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("tag"))
        filter_buttons_layout.addWidget(self.tag_filter_btn)
        
        toolbar_layout.addLayout(filter_buttons_layout)
        toolbar_layout.addStretch()
        
        # 右侧单个视图切换按钮
        self.view_toggle_btn = self.create_single_view_toggle_button()
        toolbar_layout.addWidget(self.view_toggle_btn)
        
        return toolbar_widget
    
    def create_single_view_toggle_button(self):
        """创建单个视图切换按钮"""
        # 卡片视图图标（默认显示）
        card_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 列表视图图标
        list_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 创建按钮，存储两个图标
        btn = QPushButton()
        btn.setFixedSize(32, 28)
        btn.card_svg = card_svg
        btn.list_svg = list_svg
        btn.current_view = "card"  # 默认卡片视图
        
        # 设置初始样式和图标
        self.update_toggle_button_style(btn)
        
        # 连接点击事件
        btn.clicked.connect(self.toggle_view)
        
        return btn
    
    def toggle_view(self):
        """切换视图（循环切换）"""
        # 切换视图类型
        if self.current_view == "card":
            self.current_view = "list"
        else:
            self.current_view = "card"
        
        # 更新按钮显示
        self.update_toggle_button_style(self.view_toggle_btn)
        
        # 切换显示的视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if self.current_view == 'card' else '列表'}视图")
    
    def create_card_view(self):
        """创建卡片视图容器 - 一行一个卡片，自适应宽度"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)     # 启用垂直滚动条
        scroll_area.setStyleSheet('''
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
            }
            QScrollBar:vertical {
                background-color: #F3F4F6;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #9CA3AF;
            }
        ''')
        
        # 创建卡片容器
        self.card_container = QWidget()
        self.card_layout = QVBoxLayout(self.card_container)
        self.card_layout.setContentsMargins(15, 15, 15, 15)  # 外部容器边距
        self.card_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.card_layout.setAlignment(Qt.AlignTop)
        
        # 创建垂直布局来放置卡片（一行一个）
        self.cards_list_widget = QWidget()
        self.cards_list_layout = QVBoxLayout(self.cards_list_widget)
        self.cards_list_layout.setContentsMargins(0, 0, 0, 0)
        self.cards_list_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.cards_list_layout.setAlignment(Qt.AlignTop)
        
        self.card_layout.addWidget(self.cards_list_widget)
        self.card_layout.addStretch()
        
        scroll_area.setWidget(self.card_container)
        return scroll_area

    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        self.update_view_buttons()
        
        # 切换显示的视图
        if view_type == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if view_type == 'card' else '列表'}视图")
    
    def update_toggle_button_style(self, btn):
        """更新切换按钮样式和图标"""
        # 根据当前视图选择图标和提示文本
        if self.current_view == "card":
            svg_content = btn.card_svg
            tooltip = "当前：卡片视图 (点击切换到列表视图)"
            bg_color = "#3B82F6"  # 蓝色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#3B82F6"
        else:
            svg_content = btn.list_svg
            tooltip = "当前：列表视图 (点击切换到卡片视图)"
            bg_color = "#10B981"  # 绿色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#10B981"
        
        # 设置按钮样式
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 6px;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.current_view == "card" else "#059669"};
                border-color: {"#2563EB" if self.current_view == "card" else "#059669"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.current_view == "card" else "#047857"};
            }}
        """)
        
        # 设置工具提示
        btn.setToolTip(tooltip)
        
        # 更新图标
        self.set_button_svg_icon(btn, svg_content, icon_color)
    
    def set_button_svg_icon(self, button, svg_content, color):
        """设置按钮SVG图标"""
        from PySide6.QtSvg import QSvgRenderer
        from PySide6.QtGui import QPixmap, QPainter, QIcon
        
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(16, 16))

    def update_card_view(self, prompts):
        """更新卡片视图显示 - 一行一个卡片"""
        # 清除现有卡片
        if hasattr(self, 'cards_list_layout'):
            for i in reversed(range(self.cards_list_layout.count())):
                child = self.cards_list_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
        
        # 创建卡片，每行一个
        for prompt in prompts:
            # 创建卡片外围容器 - 用于实现边框效果
            container = QFrame()
            container.setFrameShape(QFrame.Box)
            container.setFrameShadow(QFrame.Plain)
            container.setLineWidth(1)
            # 使用中性色边框
            container.setStyleSheet('''
                QFrame {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    padding: 0px;
                    background-color: white;
                }
                QFrame:hover {
                    border: 1px solid #9ca3af;
                }
            ''')
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
            container_layout.setSpacing(0)
            
            # 创建卡片内容
            card = PromptCardWidget(prompt, self)
            # 卡片自身不显示边框
            card.setStyleSheet("QFrame { border: none; background-color: white; }")
            card.setFrameShape(QFrame.NoFrame)
            container_layout.addWidget(card)
            
            # 添加到卡片列表布局
            self.cards_list_layout.addWidget(container)

    def update_view_buttons(self):
        """更新视图按钮状态"""
        self.card_view_btn.set_active(self.current_view == "card")
        self.list_view_btn.set_active(self.current_view == "list")

    def create_add_button(self):
        """创建方形SVG新建按钮"""
        # 定义新建按钮的SVG图标
        add_svg = '''<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 12H19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        btn = AddIconButton(add_svg, "新建提示词", self)
        btn.clicked.connect(self.add_prompt)
        return btn
        
    def add_prompt(self):
        """添加新提示词"""
        if self.parent_window:
            self.parent_window.status_bar.set_status("打开新建提示词对话框...")
            
            # 创建并显示新建提示词对话框
            dialog = CreatePromptDialog(self.parent_window)
            result = dialog.exec()
            
            if result == 1:  # QDialog.Accepted 的值是 1
                self.parent_window.status_bar.set_status("提示词创建成功")
                # 刷新提示词列表
                self.refresh_prompt_list()
            else:
                self.parent_window.status_bar.set_status("取消创建提示词")
                
    def get_filter_icons(self):
        """获取筛选按钮的 SVG 图标"""
        return {
            'favorite': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'tag': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0136 20.9135 12.7709 21.0141C12.5282 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4818 21.1148 11.2391 21.0141C10.9964 20.9135 10.7757 20.766 10.59 20.58L2 12V2H12L20.59 10.59C20.9625 10.9647 21.1716 11.4716 21.1716 12C21.1716 12.5284 20.9625 13.0353 20.59 13.41V13.41Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 7H7.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
    
    def apply_filters_and_update_views(self):
        """应用筛选并更新视图 - 所有筛选逻辑的中心"""
        # 从完整列表创建副本
        filtered_prompts = self.all_prompts[:]
        
        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if self.active_filters.get('favorite', False):
                self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
            else:
                self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")

    def on_filter_button_clicked(self, filter_key):
        """筛选按钮点击处理方法"""
        print(f"筛选按钮被点击: {filter_key}")
        
        # 切换按钮激活状态和筛选器状态
        if filter_key == "favorite":
            current_state = self.favorite_filter_btn.is_active
            new_state = not current_state
            self.favorite_filter_btn.set_active(new_state)
            self.active_filters['favorite'] = new_state
            
            # 应用筛选并更新视图
            self.apply_filters_and_update_views()
            
        elif filter_key == "category":
            # 切换分类筛选栏的显示/隐藏
            current_state = self.category_filter_btn.is_active
            new_state = not current_state
            self.category_filter_btn.set_active(new_state)
            
            # 切换分类筛选栏的可见性
            self.category_filter_bar.setVisible(new_state)
            
        elif filter_key == "tag":
            current_state = self.tag_filter_btn.is_active
            self.tag_filter_btn.set_active(not current_state)
        
        # 更新状态栏显示
        if self.parent_window:
            filter_names = {
                "favorite": "收藏",
                "category": "分类", 
                "tag": "标签"
            }
            if filter_key == "favorite":
                status = "激活" if self.active_filters['favorite'] else "取消"
            elif filter_key == "category":
                status = "显示" if self.category_filter_btn.is_active else "隐藏"
            else:
                status = "激活" if getattr(getattr(self, f"{filter_key}_filter_btn"), "is_active") else "取消"
            self.parent_window.status_bar.set_status(f"{status}{filter_names[filter_key]}筛选")

    def populate_category_filters(self):
        """动态填充分类筛选按钮"""
        # 清除现有按钮
        for i in reversed(range(self.category_filter_layout.count())):
            child = self.category_filter_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 从所有提示词中提取唯一的分类名称
        categories = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'category') and prompt.category and prompt.category.strip():
                categories.add(prompt.category.strip())
        
        # 排序分类列表
        sorted_categories = sorted(list(categories))
        
        # 创建"全部"按钮
        all_btn = self.create_category_button("全部", is_all_button=True)
        all_btn.clicked.connect(lambda: self.on_category_filter_clicked("全部"))
        self.category_filter_layout.addWidget(all_btn)
        
        # 为每个分类创建按钮
        for category in sorted_categories:
            btn = self.create_category_button(category)
            btn.clicked.connect(lambda checked, cat=category: self.on_category_filter_clicked(cat))
            self.category_filter_layout.addWidget(btn)
        
        # 添加弹性空间
        self.category_filter_layout.addStretch()
        
    def create_category_button(self, category_name, is_all_button=False):
        """创建分类按钮"""
        btn = QPushButton(category_name)
        btn.setFixedHeight(32)
        btn.category_name = category_name
        btn.is_active = False
        
        # 设置按钮样式
        if is_all_button:
            # "全部"按钮的特殊样式
            btn.setStyleSheet('''
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: 1px solid #3B82F6;
                    border-radius: 6px;
                padding: 6px 12px;
                font-size: 12px;
                    font-weight: 500;
            }
            QPushButton:hover {
                    background-color: #2563EB;
                    border-color: #2563EB;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            ''')
            btn.is_active = True  # "全部"按钮默认激活
        else:
            # 普通分类按钮样式
            btn.setStyleSheet('''
                QPushButton {
                    background-color: white;
                    color: #374151;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 400;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                    border-color: #9CA3AF;
                }
                QPushButton:pressed {
                    background-color: #E5E7EB;
                }
            ''')
        
        return btn
    
    def on_category_filter_clicked(self, category_name):
        """分类筛选按钮点击处理"""
        print(f"分类筛选被点击: {category_name}")
        
        # 更新按钮状态
        for i in range(self.category_filter_layout.count()):
            item = self.category_filter_layout.itemAt(i)
            if item and item.widget():
                btn = item.widget()
                if hasattr(btn, 'category_name'):
                    is_selected = btn.category_name == category_name
                    btn.is_active = is_selected
                    
                    if is_selected:
                        if category_name == "全部":
                            # "全部"按钮激活样式
                            btn.setStyleSheet('''
                                QPushButton {
                                    background-color: #3B82F6;
                                    color: white;
                                    border: 1px solid #3B82F6;
                                    border-radius: 6px;
                                    padding: 6px 12px;
                                    font-size: 12px;
                                    font-weight: 500;
                                }
                                QPushButton:hover {
                                    background-color: #2563EB;
                                    border-color: #2563EB;
                                }
                            ''')
                        else:
                            # 普通分类按钮激活样式
                            btn.setStyleSheet('''
                                QPushButton {
                                    background-color: #EBF4FF;
                                    color: #1E40AF;
                                    border: 1px solid #3B82F6;
                                    border-radius: 6px;
                                    padding: 6px 12px;
                                    font-size: 12px;
                                    font-weight: 500;
                                }
                                QPushButton:hover {
                                    background-color: #DBEAFE;
                                    border-color: #2563EB;
                                }
                            ''')
                    else:
                        # 未激活状态样式
                        btn.setStyleSheet('''
                            QPushButton {
                                background-color: white;
                                color: #374151;
                                border: 1px solid #D1D5DB;
                                border-radius: 6px;
                                padding: 6px 12px;
                                font-size: 12px;
                                font-weight: 400;
                            }
                            QPushButton:hover {
                                background-color: #F3F4F6;
                                border-color: #9CA3AF;
                            }
                        ''')
        
        # 应用分类筛选
        self.apply_category_filter(category_name)
        
        # 更新状态栏
        if self.parent_window:
            if category_name == "全部":
                self.parent_window.status_bar.set_status("显示所有分类")
            else:
                self.parent_window.status_bar.set_status(f"筛选分类: {category_name}")
    
    def apply_category_filter(self, category_name):
        """应用分类筛选"""
        if category_name == "全部":
            # 显示所有提示词（但仍要考虑其他筛选器）
            filtered_prompts = self.all_prompts[:]
        else:
            # 按分类筛选
            filtered_prompts = [prompt for prompt in self.all_prompts 
                              if hasattr(prompt, 'category') and prompt.category == category_name]
        
        # 如果收藏筛选也激活，需要进一步筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if category_name == "全部":
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
            else:
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"{category_name} (收藏): {filtered_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"{category_name}: {filtered_count} 个提示词")

    def refresh_prompt_list(self):

def get_active_filter_button_style(self):
    """Get active filter button style"""
    return '''
        QPushButton {
            background-color: #0EA5E9;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        QPushButton:hover {
            background-color: #0284C7;
        }
        QToolTip {
            background-color: #374151;
            color: white;
            border: 1px solid #4B5563;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }
    '''



def get_filter_button_style(self):
    """Get 
        """刷新提示词列表"""
        if self.parent_window:
            self.parent_window.load_data()

class PromptAssistantRedesigned(QMainWindow):
    """重构版主窗口"""
    
    def __init__(self):
        super().__init__()
        self.model = PromptModel("sqlite")
        self.init_ui()
        self.load_data()
        
    def init_ui(self):
        # 设置窗口属性
        self.setWindowTitle("Prompt收藏助手")
        self.setFixedSize(460, 800)
        self.setWindowFlags(Qt.FramelessWindowHint)  # 无标题栏
        
        # 设置窗口样式 - 移除圆角，改为直角边框
        self.setStyleSheet('''
            QMainWindow {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 0px;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 (水平布局)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.nav_bar = NavigationBar(self)
        main_layout.addWidget(self.nav_bar)
        
        # 右侧主区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 自定义标题栏
        self.title_bar = CustomTitleBar(self)
        right_layout.addWidget(self.title_bar)
        
        # 内容区域
        self.content_area = ContentArea(self)
        right_layout.addWidget(self.content_area)
        
        # 状态栏
        self.status_bar = StatusBar(self)
        right_layout.addWidget(self.status_bar)
        
        main_layout.addWidget(right_widget)
        
        # 居中显示窗口
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def switch_page(self, page_key):
        """切换页面"""
        self.content_area.switch_to_page(page_key)
        
        # 更新标题栏标题
        titles = {
            "home": "我的提示词",
            "trash": "回收站",
            "settings": "设置",
            "help": "帮助"
        }
        
        if page_key in titles:
            self.title_bar.set_title(titles[page_key])
            self.status_bar.set_status(f"已切换到{titles[page_key]}")
            
    def load_data(self):
        """加载数据"""
        try:
            prompts = self.model.get_all_prompts()
            # 将数据传递给 ContentArea 并应用筛选
            self.content_area.all_prompts = prompts
            # 填充分类筛选按钮
            self.content_area.populate_category_filters()
            self.content_area.populate_tag_filters()
            self.content_area.apply_filters_and_update_views()
            self.status_bar.set_status("数据加载完成")
        except Exception as e:
            self.status_bar.set_status(f"加载失败: {str(e)}")
            
    def update_prompt_list(self, prompts):
        """更新提示词列表显示"""
        if hasattr(self.content_area, 'prompt_list'):
            # 更新列表视图
            self.content_area.prompt_list.clear()
            for prompt in prompts:
                item_text = f"{prompt.title}\n{prompt.content[:50]}..." if len(prompt.content) > 50 else f"{prompt.title}\n{prompt.content}"
                self.content_area.prompt_list.addItem(item_text)
            
            # 更新卡片视图
            try:
                if hasattr(self.content_area, 'update_card_view') and hasattr(self.content_area, 'cards_list_layout'):
                    self.content_area.update_card_view(prompts)
            except Exception as e:
                import traceback
                print(f"更新卡片视图时出错: {e}")
                traceback.print_exc()
            



#!/usr/bin/env python3
"""
Prompt Assistant - 重构版桌面应用界面
采用460x800固定尺寸，无标题栏设计
"""
import sys
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QHBoxLayout, 
                               QVBoxLayout, QPushButton, QLabel, QFrame, 
                               QStackedWidget, QListWidget, QTextEdit, 
                               QLineEdit, QScrollArea, QMessageBox, QGraphicsDropShadowEffect,
                               QDialog, QSizePolicy)
from PySide6.QtCore import Qt, QSize, QPoint
from PySide6.QtGui import QIcon, QPalette, QColor, QFont, QPixmap, QPainter, QPen
from PySide6.QtSvg import QSvgRenderer
from PySide6.QtSvgWidgets import QSvgWidget
from PySide6.QtWidgets import QGraphicsDropShadowEffect
from model import PromptModel, Prompt
from pathlib import Path
from create_prompt_dialog import CreatePromptDialog
from tag_filter_styles import get_active_filter_button_style, get_filter_button_style

class CustomTitleBar(QWidget):
    '''自定义标题栏组件 (400x50)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(400, 50)
        self.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        ''')
        self.init_ui()
        
        # 用于拖拽窗口
        self.drag_position = QPoint()
        
    def init_ui(self):
        layout = QHBoxLayout(self)
        layout.setContentsMargins(15, 0, 10, 0)
        layout.setSpacing(10)
        
        # 标题标签
        self.title_label = QLabel("Prompt收藏助手")
        self.title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        layout.addWidget(self.title_label)
        
        layout.addStretch()
        
        # 窗口控制按钮
        self.create_control_buttons(layout)
        
    def create_control_buttons(self, layout):
        """创建窗口控制按钮"""
        button_style = """
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 4px;
                color: #6B7280;
                font-size: 16px;
                font-weight: bold;
                width: 30px;
                height: 30px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
            QPushButton:pressed {
                background-color: #D1D5DB;
            }
        """
        
        # 最小化按钮
        self.minimize_btn = QPushButton("−")
        self.minimize_btn.setStyleSheet(button_style)
        self.minimize_btn.clicked.connect(self.minimize_window)
        layout.addWidget(self.minimize_btn)
        
        # 关闭按钮
        close_style = button_style.replace("#E5E7EB", "#FEE2E2").replace("#D1D5DB", "#FECACA")
        close_style = close_style.replace("color: #6B7280;", "color: #DC2626;")
        self.close_btn = QPushButton("×")
        self.close_btn.setStyleSheet(close_style)
        self.close_btn.clicked.connect(self.close_window)
        layout.addWidget(self.close_btn)
        
    def minimize_window(self):
        if self.parent_window:
            self.parent_window.showMinimized()
            
    def close_window(self):
        if self.parent_window:
            self.parent_window.close()
            
    def mousePressEvent(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.parent_window.frameGeometry().topLeft()
            
    def mouseMoveEvent(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.parent_window.move(event.globalPosition().toPoint() - self.drag_position)
            
    def set_title(self, title):
        """设置标题"""
        self.title_label.setText(title)

class AddIconButton(QPushButton):
    """方形SVG新建按钮 - 三种状态"""
    
    def __init__(self, svg_content, tooltip, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.setFixedSize(36, 36)  # 方形尺寸
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(4)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(1)
        self.shadow_effect.setColor(QColor(59, 130, 246, 40))  # 蓝色阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：更深的蓝色阴影
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(29, 78, 216, 80))
        elif state == "hover":
            # 悬停状态：增强的蓝色阴影
            self.shadow_effect.setBlurRadius(5)
            self.shadow_effect.setYOffset(1)
            self.shadow_effect.setColor(QColor(37, 99, 235, 60))
        else:
            # 默认状态：轻微蓝色阴影
            self.shadow_effect.setBlurRadius(4)
            self.shadow_effect.setYOffset(1)
            self.shadow_effect.setColor(QColor(59, 130, 246, 40))
        
    def update_icon(self, state="default"):
        """更新图标状态"""
        # 三种状态的颜色和背景
        if state == "active":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#1D4ED8"  # 深蓝色背景
        elif state == "hover":
            color = "#FFFFFF"  # 白色图标
            bg_color = "#2563EB"  # 中蓝色背景
        else:
            color = "#FFFFFF"  # 白色图标
            bg_color = "#3B82F6"  # 默认蓝色背景
            
        # 设置按钮样式
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: none;
                border-radius: 6px;
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(20, 20)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(20, 20))
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.update_icon("hover")
        self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon("default")
        self.update_shadow("default")
        super().leaveEvent(event)
        
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("active")
            self.update_shadow("active")
        super().mousePressEvent(event)
        
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.update_icon("hover")
            self.update_shadow("hover")
        super().mouseReleaseEvent(event)

class PromptCardWidget(QFrame):
    """提示词卡片组件 - 自适应宽度，高度自适应"""
    
    def __init__(self, prompt, parent=None):
        super().__init__(parent)
        self.prompt = prompt
        # 移除固定宽度，改为自适应宽度
        self.setMinimumHeight(120)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)  # 水平扩展，垂直最小
        
        # 设置基础框架样式
        self.setFrameShape(QFrame.Box)
        self.setFrameShadow(QFrame.Plain)
        self.setLineWidth(1)
        
        # 初始化鼠标悬停状态
        self.hovered = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置UI布局"""
        # 背景颜色设置
        self.setStyleSheet('''
            QFrame {
                background-color: white;
            }
        ''')
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(12, 10, 12, 10)  # 保持左、上、右的内边距，减少底部内边距
        main_layout.setSpacing(8)
        
        # 顶部区域：分类 + 操作按钮
        top_widget = self.create_top_section()
        main_layout.addWidget(top_widget)
        
        # 标题区域
        title_widget = self.create_title_section()
        main_layout.addWidget(title_widget)
        
        # 内容区域
        content_widget = self.create_content_section()
        main_layout.addWidget(content_widget)
        
        # 底部标签区域
        tags_widget = self.create_tags_section()
        main_layout.addWidget(tags_widget)
        
        # 移除这一行，不再添加底部额外空间
        # main_layout.addStretch()
    
    def paintEvent(self, event):
        """重写绘制事件，手动绘制边框"""
        super().paintEvent(event)
        
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        
        # 设置画笔
        pen = QPen()
        if self.hovered:
            pen.setColor(QColor("#9ca3af"))  # 悬停时深灰色
            pen.setWidth(2)
        else:
            pen.setColor(QColor("#e0e0e0"))  # 默认浅灰色
            pen.setWidth(1)
        
        painter.setPen(pen)
        painter.setBrush(Qt.NoBrush)  # 不填充
        
        # 绘制圆角矩形
        rect = self.rect().adjusted(1, 1, -1, -1)  # 调整绘制区域，避免边缘被裁剪
        painter.drawRoundedRect(rect, 6, 6)
        
    def create_top_section(self):
        """创建顶部区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 左侧分类信息 - 删除"分类:"前缀，只显示分类名称
        category_label = QLabel(getattr(self.prompt, 'category', '通用'))
        category_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        ''')
        layout.addWidget(category_label)
        
        layout.addStretch()
        
        # 右侧操作按钮组
        buttons_widget = self.create_action_buttons()
        layout.addWidget(buttons_widget)
        
        return widget
        
    def create_action_buttons(self):
        """创建操作按钮组"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 定义按钮图标SVG
        button_icons = {
            'favorite': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'copy': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" stroke-width="2"/>
            </svg>''',
            'edit': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'history': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="12,6 12,12 16,14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'delete': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <polyline points="3,6 5,6 21,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
        button_tooltips = {
            'favorite': '收藏',
            'pin': '置顶', 
            'copy': '复制',
            'edit': '编辑',
            'history': '历史',
            'delete': '删除'
        }
        
        for key, svg in button_icons.items():
            btn = self.create_small_icon_button(svg, button_tooltips[key])
            layout.addWidget(btn)
            
        return widget
        
    def create_small_icon_button(self, svg_content, tooltip):
        """创建小图标按钮"""
        btn = QPushButton()
        btn.setFixedSize(16, 16)
        btn.setToolTip(tooltip)
        btn.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 2px;
                padding: 2px;
            }
            QPushButton:hover {
                background-color: rgba(243, 244, 246, 0.8);
            }
            QPushButton:pressed {
                background-color: rgba(229, 231, 235, 0.8);
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
        # 设置图标
        self.set_button_icon(btn, svg_content, "#6B7280")
        
        # 悬停效果
        def on_enter():
            self.set_button_icon(btn, svg_content, "#3B82F6")
        def on_leave():
            self.set_button_icon(btn, svg_content, "#6B7280")
            
        btn.enterEvent = lambda e: on_enter()
        btn.leaveEvent = lambda e: on_leave()
        
        return btn
        
    def set_button_icon(self, button, svg_content, color):
        """设置按钮图标"""
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(12, 12)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(12, 12))
        
    def create_title_section(self):
        """创建标题区域"""
        title_label = QLabel(self.prompt.title)
        title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)  # 最多显示2行标题
        return title_label
        
    def create_content_section(self):
        """创建内容区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 检查是否有媒体文件（这里简化处理，假设根据内容长度判断）
        has_media = hasattr(self.prompt, 'has_media') and self.prompt.has_media
        
        if has_media:
            # 状态B：带媒体布局
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(8)
            
            # 左侧缩略图
            thumbnail = QLabel()
            thumbnail.setFixedSize(40, 40)
            thumbnail.setStyleSheet('''
                QLabel {
                    background-color: #F3F4F6;
                    border: 1px solid #E5E7EB;
                    border-radius: 4px;
                }
            ''')
            thumbnail.setAlignment(Qt.AlignCenter)
            thumbnail.setText("📷")  # 占位符
            layout.addWidget(thumbnail)
            
            # 右侧内容预览
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        else:
            # 状态A：纯文本布局
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = self.create_content_preview()
            layout.addWidget(content_label)
            
        return widget
        
    def create_content_preview(self):
        """创建内容预览标签"""
        # 截取内容前100个字符作为预览
        preview_text = self.prompt.content[:100] + "..." if len(self.prompt.content) > 100 else self.prompt.content
        
        content_label = QLabel(preview_text)
        content_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                line-height: 1.4;
                background: transparent;
                border: none;
            }
        ''')
        content_label.setWordWrap(True)
        content_label.setMaximumHeight(60)  # 最多显示3行内容
        content_label.setAlignment(Qt.AlignTop)
        
        return content_label
        
    def create_tags_section(self):
        """创建标签区域"""
        widget = QWidget()
        widget.setStyleSheet("background-color: transparent;")
        
        # 使用FlowLayout代替HBoxLayout以实现标签自动换行
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 获取标签 - 确保标签正确显示
        tags = []
        
        if hasattr(self.prompt, 'tags'):
            if self.prompt.tags is not None:
                # 如果是字符串，按逗号分割
                if isinstance(self.prompt.tags, str):
                    tags = [tag.strip() for tag in self.prompt.tags.split(',') if tag.strip()]
                else:
                    # 确保是列表类型
                    tags = list(self.prompt.tags)
                    
        # 仅当tags完全不存在或为None时才使用默认标签
        if not tags and not hasattr(self.prompt, 'tags'):
            tags = ['AI助手', '提示词', '创意']
        
        # 最多显示5个标签
        tag_count = 0
        for tag in tags:
            if tag_count >= 5:
                break
                
            tag_label = self.create_tag_label(tag)
            layout.addWidget(tag_label)
            tag_count += 1
            
        # 如果有更多标签，显示+N标签
        if len(tags) > 5:
            more_label = self.create_more_tags_label(len(tags) - 5)
            layout.addWidget(more_label)
            
        layout.addStretch()
        return widget
        
    def create_tag_label(self, tag_text):
        """创建单个标签"""
        label = QLabel(tag_text)
        label.setStyleSheet('''
            QLabel {
                background-color: #EBF4FF;
                color: #1E40AF;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        ''')
        return label
        
    def create_more_tags_label(self, count):
        """创建显示更多标签的标签"""
        label = QLabel(f"+{count}")
        label.setStyleSheet('''
            QLabel {
                background-color: #F3F4F6;
                color: #6B7280;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
            }
        ''')
        return label
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.hovered = True
        self.update()  # 触发重绘
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.hovered = False
        self.update()  # 触发重绘
        super().leaveEvent(event)

class FilterToggleButton(QPushButton):
    """筛选切换按钮 - 通用筛选按钮类"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class ViewToggleButton(QPushButton):
    """视图切换按钮 - 28x28px尺寸"""
    
    def __init__(self, svg_content, tooltip, view_type, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.view_type = view_type
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)

class SvgIconButton(QPushButton):
    """自定义SVG图标按钮"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(40, 40)
        self.setToolTip(tooltip)
        self.setup_shadow()
        self.update_icon()
        self.setup_shadow()
        
        # 设置按钮样式
        self.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
    def update_icon(self):
        """更新图标状态"""
        # 根据状态选择颜色 - 避免白色系，确保在深色背景下清晰可见
        if self.is_active:
            color = "#3B82F6"  # 激活状态：蓝色
        else:
            color = "#6B7280"  # 默认状态：中灰色
            
        # 替换SVG中的颜色
        svg_data = self.svg_content.replace("currentColor", color)
        
        # 创建QPixmap
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        # 设置图标
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(24, 24))
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(8)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：蓝色阴影，更明显
            self.shadow_effect.setBlurRadius(12)
            self.shadow_effect.setYOffset(3)
            self.shadow_effect.setColor(QColor(59, 130, 246, 100))  # 蓝色阴影
        elif state == "hover":
            # 悬停状态：稍微增强的阴影
            self.shadow_effect.setBlurRadius(10)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 80))  # 深一点的阴影
        else:
            # 默认状态：轻微阴影
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 默认阴影
        
    def setup_shadow(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(6)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 40))  # 默认轻微阴影
        self.setGraphicsEffect(self.shadow_effect)
        
    def update_shadow(self, state="default"):
        """更新阴影状态"""
        if state == "active":
            # 激活状态：蓝色阴影，更明显
            self.shadow_effect.setBlurRadius(10)
            self.shadow_effect.setYOffset(3)
            self.shadow_effect.setColor(QColor(59, 130, 246, 80))  # 蓝色阴影
        elif state == "hover":
            # 悬停状态：稍微增强的阴影
            self.shadow_effect.setBlurRadius(8)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 60))  # 深一点的阴影
        else:
            # 默认状态：轻微阴影
            self.shadow_effect.setBlurRadius(6)
            self.shadow_effect.setYOffset(2)
            self.shadow_effect.setColor(QColor(0, 0, 0, 40))  # 默认阴影
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_icon()
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        if active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态：亮灰色，避免白色系
            svg_data = self.svg_content.replace("currentColor", "#9CA3AF")
            renderer = QSvgRenderer()
            renderer.load(svg_data.encode())
            
            pixmap = QPixmap(24, 24)
            pixmap.fill(Qt.transparent)
            
            painter = QPainter(pixmap)
            renderer.render(painter)
            painter.end()
            
            self.setIcon(QIcon(pixmap))
            self.update_shadow("hover")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_icon()
        if self.is_active:
            self.update_shadow("active")
        else:
            self.update_shadow("default")
        super().leaveEvent(event)

class NavigationBar(QWidget):
    '''左侧导航栏组件 (60x800)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.setFixedSize(60, 800)
        self.current_page = "home"
        self.nav_buttons = {}
        self.init_ui()
        
    def get_svg_icons(self):
        """获取SVG图标定义"""
        return {
            "home": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M9 22V12H15V22" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "trash": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "settings": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.2573 9.77251 19.9887C9.5799 19.7201 9.31074 19.5176 9 19.41C8.69838 19.2769 8.36381 19.2372 8.03941 19.296C7.71502 19.3548 7.41568 19.5095 7.18 19.74L7.12 19.8C6.93425 19.986 6.71368 20.1335 6.47088 20.2341C6.22808 20.3348 5.96783 20.3866 5.705 20.3866C5.44217 20.3866 5.18192 20.3348 4.93912 20.2341C4.69632 20.1335 4.47575 19.986 4.29 19.8C4.10405 19.6143 3.95653 19.3937 3.85588 19.1509C3.75523 18.9081 3.70343 18.6478 3.70343 18.385C3.70343 18.1222 3.75523 17.8619 3.85588 17.6191C3.95653 17.3763 4.10405 17.1557 4.29 16.97L4.35 16.91C4.58054 16.6743 4.73519 16.375 4.794 16.0506C4.85282 15.7262 4.81312 15.3916 4.68 15.09C4.55324 14.7942 4.34276 14.542 4.07447 14.3643C3.80618 14.1866 3.49179 14.0913 3.17 14.09H3C2.46957 14.09 1.96086 13.8793 1.58579 13.5042C1.21071 13.1291 1 12.6204 1 12.09C1 11.5596 1.21071 11.0509 1.58579 10.6758C1.96086 10.3007 2.46957 10.09 3 10.09H3.09C3.42099 10.0823 3.742 9.97512 4.01062 9.78251C4.27925 9.5899 4.48167 9.32074 4.59 9.01C4.72312 8.70838 4.76282 8.37381 4.704 8.04941C4.64519 7.72502 4.49054 7.42568 4.26 7.19L4.2 7.13C4.01405 6.94425 3.86653 6.72368 3.76588 6.48088C3.66523 6.23808 3.61343 5.97783 3.61343 5.715C3.61343 5.45217 3.66523 5.19192 3.76588 4.94912C3.86653 4.70632 4.01405 4.48575 4.2 4.3C4.38575 4.11405 4.60632 3.96653 4.84912 3.86588C5.09192 3.76523 5.35217 3.71343 5.615 3.71343C5.87783 3.71343 6.13808 3.76523 6.38088 3.86588C6.62368 3.96653 6.84425 4.11405 7.03 4.3L7.09 4.36C7.32568 4.59054 7.62502 4.74519 7.94941 4.804C8.27381 4.86282 8.60838 4.82312 8.91 4.69H9C9.29577 4.56324 9.54802 4.35276 9.72569 4.08447C9.90337 3.81618 9.99872 3.50179 10 3.18V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            "help": '''<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.09 9C9.3251 8.33167 9.78915 7.76811 10.4 7.40913C11.0108 7.05016 11.7289 6.91894 12.4272 7.03871C13.1255 7.15849 13.7588 7.52152 14.2151 8.06353C14.6713 8.60553 14.9211 9.29152 14.92 10C14.92 12 11.92 13 11.92 13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M12 17H12.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: #1F2937;
            }
        ''')
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 20, 0, 20)
        layout.setSpacing(0)
        
        # Logo区域
        logo_label = QLabel("PA")
        logo_label.setAlignment(Qt.AlignCenter)
        logo_label.setStyleSheet('''
            QLabel {
                color: #3B82F6;
                font-size: 18px;
                font-weight: bold;
                background-color: #374151;
                border-radius: 8px;
                padding: 8px;
                margin: 0 10px 30px 10px;
            }
        ''')
        layout.addWidget(logo_label)
        
        # 主要导航按钮区域（居中显示）
        main_nav_layout = QVBoxLayout()
        main_nav_layout.setSpacing(15)
        main_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 主要导航项（首页和回收站）
        main_nav_items = [
            ("home", "首页"),
            ("trash", "回收站")
        ]
        
        svg_icons = self.get_svg_icons()
        
        for key, tooltip in main_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            main_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(main_nav_layout)
        layout.addStretch()
        
        # 底部功能按钮区域
        bottom_nav_layout = QVBoxLayout()
        bottom_nav_layout.setSpacing(15)
        bottom_nav_layout.setAlignment(Qt.AlignCenter)
        
        # 底部导航项（设置和帮助）
        bottom_nav_items = [
            ("settings", "设置"),
            ("help", "帮助")
        ]
        
        for key, tooltip in bottom_nav_items:
            btn = self.create_svg_button(svg_icons[key], tooltip, key)
            self.nav_buttons[key] = btn
            bottom_nav_layout.addWidget(btn, 0, Qt.AlignCenter)
            
        layout.addLayout(bottom_nav_layout)
        
        # 设置默认选中状态
        self.set_active_button("home")
        
    def create_svg_button(self, svg_content, tooltip, key):
        """创建SVG图标按钮"""
        btn = SvgIconButton(svg_content, tooltip, key, self)
        btn.setStyleSheet('''
            QPushButton {
                background-color: transparent;
                border: none;
                border-radius: 8px;
                padding: 8px;
            }
            QPushButton:hover {
                background-color: transparent;
            }
            QPushButton:pressed {
                background-color: transparent;
            }
        ''')
        btn.clicked.connect(lambda: self.on_nav_clicked(key))
        return btn
            
    def on_nav_clicked(self, key):
        """导航按钮点击事件"""
        self.set_active_button(key)
        if self.parent_window:
            self.parent_window.switch_page(key)
            
    def set_active_button(self, key):
        """设置活动按钮"""
        self.current_page = key
        for btn_key, btn in self.nav_buttons.items():
            is_active = btn_key == key
            btn.set_active(is_active)

class StatusBar(QWidget):
    '''底部状态栏组件 (400x32)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 32)  # 调整高度从50px到32px，更符合移动端设计规范
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: #F3F4F6;
                border-top: 1px solid #E5E7EB;
            }
        ''')
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)
        layout.setAlignment(Qt.AlignVCenter)  # 设置整个布局垂直居中
        
        # 状态信息标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        ''')
        self.status_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 右侧信息
        self.info_label = QLabel("")
        self.info_label.setStyleSheet('''
            QLabel {
                color: #9CA3AF;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        ''')
        self.info_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.info_label)
        
    def set_status(self, message):
        """设置状态信息"""
        self.status_label.setText(message)
        
    def set_info(self, info):
        """设置右侧信息"""
        self.info_label.setText(info)

class ContentArea(QWidget):
    '''内容显示区域 (400x718)'''
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.current_view = "card"  # 默认为卡片视图
        self.all_prompts = []  # 存储从数据库加载的、未经筛选的完整提示词列表
        self.active_filters = {'favorite': False, 'category': '全部', 'tags': set()}  # 跟踪当前激活的筛选器，tags改为set支持多选
        self.category_buttons = {}  # 存储分类按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用
        self.tag_buttons = {}  # 存储标签按钮的引用
        self.setFixedSize(400, 718)  # 调整高度从700px到718px，补偿状态栏减少的18px
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet('''
            QWidget {
                background-color: white;
            }
        ''')
        
        # 使用堆叠布局来切换不同页面
        self.stacked_widget = QStackedWidget()
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.stacked_widget)
        
        # 创建各个页面
        self.create_pages()
        
    def create_pages(self):
        """创建各个页面"""
        # 首页
        self.home_page = self.create_home_page()
        self.stacked_widget.addWidget(self.home_page)
        
        # 回收站页面
        self.trash_page = self.create_trash_page()
        self.stacked_widget.addWidget(self.trash_page)
        
        # 设置页面
        self.settings_page = self.create_settings_page()
        self.stacked_widget.addWidget(self.settings_page)
        
        # 帮助页面
        self.help_page = self.create_help_page()
        self.stacked_widget.addWidget(self.help_page)
        
    def create_home_page(self):
        """创建首页"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 8, 15, 15)  # 减少顶部间距从15px到8px
        layout.setSpacing(12)  # 减少整体间距从15px到12px
        
        # 搜索框和新建按钮区域 - 调整到内容区域顶部
        search_layout = QHBoxLayout()
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索提示词...")
        self.search_input.setStyleSheet('''
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                font-size: 14px;
                background-color: white;
            }
            QLineEdit:focus {
                border-color: #3B82F6;
                outline: none;
            }
        ''')
        search_layout.addWidget(self.search_input)
        
        # 创建方形SVG图标按钮替换文本按钮
        add_btn = self.create_add_button()
        search_layout.addWidget(add_btn)
        
        layout.addLayout(search_layout)
        
        # 工具栏
        toolbar_widget = self.create_toolbar()
        layout.addWidget(toolbar_widget)
        
        # 分类筛选栏容器（默认隐藏）
        self.category_filter_bar = QWidget()
        self.category_filter_bar.setFixedHeight(50)
        self.category_filter_bar.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                margin: 0px;
            }
        ''')
        
        # 为分类筛选栏设置水平布局
        self.category_filter_layout = QHBoxLayout(self.category_filter_bar)
        self.category_filter_layout.setContentsMargins(12, 8, 12, 8)
        self.category_filter_layout.setSpacing(8)
        
        # 添加分类筛选栏到布局
        layout.addWidget(self.category_filter_bar)
        
        # 标签筛选栏（默认隐藏）
        self.tag_filter_area = QScrollArea()
        self.tag_filter_area.setFixedHeight(50)
        self.tag_filter_area.setWidgetResizable(True)
        self.tag_filter_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.tag_filter_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.tag_filter_area.setStyleSheet('''
            QScrollArea {
                background-color: #F0F9FF;
                border: 1px solid #BAE6FD;
                border-radius: 6px;
                margin: 2px 0px;
            }
        ''')
        self.tag_filter_area.hide()
        
        # 标签筛选栏内容容器
        tag_filter_widget = QWidget()
        self.tag_filter_layout = QHBoxLayout(tag_filter_widget)
        self.tag_filter_layout.setContentsMargins(8, 8, 8, 8)
        self.tag_filter_layout.setSpacing(6)
        self.tag_filter_area.setWidget(tag_filter_widget)
        
        layout.addWidget(self.tag_filter_area)
        
        # 初始状态下隐藏分类筛选栏
        self.category_filter_bar.hide()
        
        # 创建堆叠容器来切换列表视图和卡片视图
        self.view_stack = QStackedWidget()
        
        # 列表视图
        self.prompt_list = QListWidget()
        self.prompt_list.setStyleSheet('''
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #F9FAFB;
            }
            QListWidget::item {
                padding: 12px;
                border-bottom: 1px solid #F3F4F6;
            }
            QListWidget::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QListWidget::item:hover {
                background-color: #F3F4F6;
            }
        ''')
        self.view_stack.addWidget(self.prompt_list)
        
        # 卡片视图
        self.card_view = self.create_card_view()
        self.view_stack.addWidget(self.card_view)
        
        # 默认显示卡片视图
        self.view_stack.setCurrentIndex(1)
        
        layout.addWidget(self.view_stack)
        
        return page
        
    def create_trash_page(self):
        """创建回收站页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("回收站")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }
        ''')
        layout.addWidget(title)
        
        # 空状态提示
        empty_label = QLabel("回收站为空")
        empty_label.setAlignment(Qt.AlignCenter)
        empty_label.setStyleSheet('''
            QLabel {
                color: #9CA3AF;
                font-size: 14px;
                padding: 40px;
            }
        ''')
        layout.addWidget(empty_label)
        layout.addStretch()
        
        return page
        
    def create_settings_page(self):
        """创建设置页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(20)
        
        title = QLabel("设置")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
            }
        ''')
        layout.addWidget(title)
        
        # 设置项
        settings_items = [
            ("主题设置", "选择应用主题"),
            ("存储设置", "配置数据存储方式"),
            ("导入导出", "管理数据备份"),
            ("关于应用", "查看版本信息")
        ]
        
        for title_text, desc_text in settings_items:
            item_widget = self.create_setting_item(title_text, desc_text)
            layout.addWidget(item_widget)
            
        layout.addStretch()
        
        return page
        
    def create_setting_item(self, title, description):
        """创建设置项"""
        widget = QFrame()
        widget.setStyleSheet('''
            QFrame {
                background-color: #F9FAFB;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                padding: 12px;
            }
            QFrame:hover {
                background-color: #F3F4F6;
            }
        ''')
        
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(12, 12, 12, 12)
        
        title_label = QLabel(title)
        title_label.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        ''')
        
        desc_label = QLabel(description)
        desc_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                background: transparent;
                border: none;
            }
        ''')
        
        layout.addWidget(title_label)
        layout.addWidget(desc_label)
        
        return widget
        
    def create_help_page(self):
        """创建帮助页面"""
        page = QWidget()
        layout = QVBoxLayout(page)
        layout.setContentsMargins(15, 15, 15, 15)
        
        title = QLabel("帮助")
        title.setStyleSheet('''
            QLabel {
                color: #1F2937;
                font-size: 18px;
                font-weight: 600;
                margin-bottom: 20px;
            }
        ''')
        layout.addWidget(title)
        
        help_text = QLabel("""
        
        """)
        help_text.setStyleSheet('''
            QLabel {
                color: #374151;
                font-size: 13px;
                line-height: 1.5;
                background: transparent;
                border: none;
            }
        ''')
        help_text.setWordWrap(True)
        layout.addWidget(help_text)
        layout.addStretch()
        
        return page
        
    def switch_to_page(self, page_key):
        """切换到指定页面"""
        page_map = {
            "home": 0,
            "trash": 1,
            "settings": 2,
            "help": 3
        }
        
        if page_key in page_map:
            self.stacked_widget.setCurrentIndex(page_map[page_key])
            
    def create_toolbar(self):
        """创建工具栏 - 无边框设计"""
        toolbar_widget = QWidget()
        toolbar_widget.setFixedHeight(40)
        toolbar_widget.setStyleSheet('''
            QWidget {
                background-color: #F9FAFB;
                border-radius: 6px;
                border: none;
            }
        ''')
        
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(12, 0, 12, 0)
        toolbar_layout.setSpacing(8)
        
        # 左侧工具栏标识
        toolbar_label = QLabel("工具栏")
        toolbar_label.setStyleSheet('''
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-weight: 400;
                background: transparent;
                border: none;
            }
        ''')
        toolbar_layout.addWidget(toolbar_label)
        
        # 添加筛选按钮组
        filter_buttons_layout = QHBoxLayout()
        filter_buttons_layout.setSpacing(4)
        
        # 准备 SVG 图标内容
        filter_icons = self.get_filter_icons()
        
        # 创建三个筛选按钮
        self.favorite_filter_btn = FilterToggleButton(
            filter_icons['favorite'], "收藏筛选", "favorite", self
        )
        self.favorite_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("favorite"))
        filter_buttons_layout.addWidget(self.favorite_filter_btn)
        
        self.category_filter_btn = FilterToggleButton(
            filter_icons['category'], "分类筛选", "category", self
        )
        self.category_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("category"))
        filter_buttons_layout.addWidget(self.category_filter_btn)
        
        self.tag_filter_btn = FilterToggleButton(
            filter_icons['tag'], "标签筛选", "tag", self
        )
        self.tag_filter_btn.clicked.connect(lambda: self.on_filter_button_clicked("tag"))
        filter_buttons_layout.addWidget(self.tag_filter_btn)
        
        toolbar_layout.addLayout(filter_buttons_layout)
        toolbar_layout.addStretch()
        
        # 右侧单个视图切换按钮
        self.view_toggle_btn = self.create_single_view_toggle_button()
        toolbar_layout.addWidget(self.view_toggle_btn)
        
        return toolbar_widget
    
    def create_single_view_toggle_button(self):
        """创建单个视图切换按钮"""
        # 卡片视图图标（默认显示）
        card_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="3" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="3" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="3" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <rect x="14" y="14" width="7" height="7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 列表视图图标
        list_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <line x1="8" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="12" x2="21" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="8" y1="18" x2="21" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="6" x2="3.01" y2="6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="12" x2="3.01" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <line x1="3" y1="18" x2="3.01" y2="18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        # 创建按钮，存储两个图标
        btn = QPushButton()
        btn.setFixedSize(32, 28)
        btn.card_svg = card_svg
        btn.list_svg = list_svg
        btn.current_view = "card"  # 默认卡片视图
        
        # 设置初始样式和图标
        self.update_toggle_button_style(btn)
        
        # 连接点击事件
        btn.clicked.connect(self.toggle_view)
        
        return btn
    
    def toggle_view(self):
        """切换视图（循环切换）"""
        # 切换视图类型
        if self.current_view == "card":
            self.current_view = "list"
        else:
            self.current_view = "card"
        
        # 更新按钮显示
        self.update_toggle_button_style(self.view_toggle_btn)
        
        # 切换显示的视图
        if self.current_view == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if self.current_view == 'card' else '列表'}视图")
    
    def create_card_view(self):
        """创建卡片视图容器 - 一行一个卡片，自适应宽度"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 禁用水平滚动条
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)     # 启用垂直滚动条
        scroll_area.setStyleSheet('''
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                background-color: white;
            }
            QScrollBar:vertical {
                background-color: #F3F4F6;
                width: 8px;
                border-radius: 4px;
            }
            QScrollBar::handle:vertical {
                background-color: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #9CA3AF;
            }
        ''')
        
        # 创建卡片容器
        self.card_container = QWidget()
        self.card_layout = QVBoxLayout(self.card_container)
        self.card_layout.setContentsMargins(15, 15, 15, 15)  # 外部容器边距
        self.card_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.card_layout.setAlignment(Qt.AlignTop)
        
        # 创建垂直布局来放置卡片（一行一个）
        self.cards_list_widget = QWidget()
        self.cards_list_layout = QVBoxLayout(self.cards_list_widget)
        self.cards_list_layout.setContentsMargins(0, 0, 0, 0)
        self.cards_list_layout.setSpacing(16)  # 设置卡片之间的垂直间距为16像素
        self.cards_list_layout.setAlignment(Qt.AlignTop)
        
        self.card_layout.addWidget(self.cards_list_widget)
        self.card_layout.addStretch()
        
        scroll_area.setWidget(self.card_container)
        return scroll_area

    def switch_view(self, view_type):
        """切换视图"""
        self.current_view = view_type
        self.update_view_buttons()
        
        # 切换显示的视图
        if view_type == "card":
            self.view_stack.setCurrentIndex(1)  # 显示卡片视图
        else:
            self.view_stack.setCurrentIndex(0)  # 显示列表视图
            
        if self.parent_window:
            self.parent_window.status_bar.set_status(f"已切换到{'卡片' if view_type == 'card' else '列表'}视图")
    
    def update_toggle_button_style(self, btn):
        """更新切换按钮样式和图标"""
        # 根据当前视图选择图标和提示文本
        if self.current_view == "card":
            svg_content = btn.card_svg
            tooltip = "当前：卡片视图 (点击切换到列表视图)"
            bg_color = "#3B82F6"  # 蓝色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#3B82F6"
        else:
            svg_content = btn.list_svg
            tooltip = "当前：列表视图 (点击切换到卡片视图)"
            bg_color = "#10B981"  # 绿色背景表示激活状态
            icon_color = "#FFFFFF"  # 白色图标
            border_color = "#10B981"
        
        # 设置按钮样式
        btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 6px;
                padding: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.current_view == "card" else "#059669"};
                border-color: {"#2563EB" if self.current_view == "card" else "#059669"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.current_view == "card" else "#047857"};
            }}
        """)
        
        # 设置工具提示
        btn.setToolTip(tooltip)
        
        # 更新图标
        self.set_button_svg_icon(btn, svg_content, icon_color)
    
    def set_button_svg_icon(self, button, svg_content, color):
        """设置按钮SVG图标"""
        from PySide6.QtSvg import QSvgRenderer
        from PySide6.QtGui import QPixmap, QPainter, QIcon
        
        svg_data = svg_content.replace("currentColor", color)
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        button.setIcon(QIcon(pixmap))
        button.setIconSize(QSize(16, 16))

    def update_card_view(self, prompts):
        """更新卡片视图显示 - 一行一个卡片"""
        # 清除现有卡片
        if hasattr(self, 'cards_list_layout'):
            for i in reversed(range(self.cards_list_layout.count())):
                child = self.cards_list_layout.itemAt(i).widget()
                if child:
                    child.setParent(None)
        
        # 创建卡片，每行一个
        for prompt in prompts:
            # 创建卡片外围容器 - 用于实现边框效果
            container = QFrame()
            container.setFrameShape(QFrame.Box)
            container.setFrameShadow(QFrame.Plain)
            container.setLineWidth(1)
            # 使用中性色边框
            container.setStyleSheet('''
                QFrame {
                    border: 1px solid #e0e0e0;
                    border-radius: 6px;
                    padding: 0px;
                    background-color: white;
                }
                QFrame:hover {
                    border: 1px solid #9ca3af;
                }
            ''')
            container_layout = QVBoxLayout(container)
            container_layout.setContentsMargins(0, 0, 0, 0)  # 设置所有边距为0
            container_layout.setSpacing(0)
            
            # 创建卡片内容
            card = PromptCardWidget(prompt, self)
            # 卡片自身不显示边框
            card.setStyleSheet("QFrame { border: none; background-color: white; }")
            card.setFrameShape(QFrame.NoFrame)
            container_layout.addWidget(card)
            
            # 添加到卡片列表布局
            self.cards_list_layout.addWidget(container)

    def update_view_buttons(self):
        """更新视图按钮状态"""
        self.card_view_btn.set_active(self.current_view == "card")
        self.list_view_btn.set_active(self.current_view == "list")

    def create_add_button(self):
        """创建方形SVG新建按钮"""
        # 定义新建按钮的SVG图标
        add_svg = '''<svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 5V19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M5 12H19" stroke="currentColor" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>'''
        
        btn = AddIconButton(add_svg, "新建提示词", self)
        btn.clicked.connect(self.add_prompt)
        return btn
        
    def add_prompt(self):
        """添加新提示词"""
        if self.parent_window:
            self.parent_window.status_bar.set_status("打开新建提示词对话框...")
            
            # 创建并显示新建提示词对话框
            dialog = CreatePromptDialog(self.parent_window)
            result = dialog.exec()
            
            if result == 1:  # QDialog.Accepted 的值是 1
                self.parent_window.status_bar.set_status("提示词创建成功")
                # 刷新提示词列表
                self.refresh_prompt_list()
            else:
                self.parent_window.status_bar.set_status("取消创建提示词")
                
    def get_filter_icons(self):
        """获取筛选按钮的 SVG 图标"""
        return {
            'favorite': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.5783 8.50903 2.9987 7.05 2.9987C5.59096 2.9987 4.19169 3.5783 3.16 4.61C2.1283 5.6417 1.5487 7.04097 1.5487 8.5C1.5487 9.95903 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39467C21.7563 5.72723 21.351 5.1208 20.84 4.61V4.61Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'category': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22 19C22 19.5304 21.7893 20.0391 21.4142 20.4142C21.0391 20.7893 20.5304 21 20 21H4C3.46957 21 2.96086 20.7893 2.58579 20.4142C2.21071 20.0391 2 19.5304 2 19V5C2 4.46957 2.21071 3.96086 2.58579 3.58579C2.96086 3.21071 3.46957 3 4 3H9L11 6H20C20.5304 6 21.0391 6.21071 21.4142 6.58579C21.7893 6.96086 22 7.46957 22 8V19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'tag': '''<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.59 13.41L13.42 20.58C13.2343 20.766 13.0136 20.9135 12.7709 21.0141C12.5282 21.1148 12.2678 21.1666 12.005 21.1666C11.7422 21.1666 11.4818 21.1148 11.2391 21.0141C10.9964 20.9135 10.7757 20.766 10.59 20.58L2 12V2H12L20.59 10.59C20.9625 10.9647 21.1716 11.4716 21.1716 12C21.1716 12.5284 20.9625 13.0353 20.59 13.41V13.41Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M7 7H7.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        }
    
    def apply_filters_and_update_views(self):
        """应用筛选并更新视图 - 所有筛选逻辑的中心"""
        # 从完整列表创建副本
        filtered_prompts = self.all_prompts[:]
        
        # 检查收藏筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 检查分类筛选
        if self.active_filters.get('category', '全部') != '全部':
            selected_category = self.active_filters['category']
            filtered_prompts = [prompt for prompt in filtered_prompts if prompt.category == selected_category]
        
        # 检查标签筛选
        if self.active_filters.get('tag', '全部') != '全部':
            selected_tag = self.active_filters['tag']
            filtered_prompts = [prompt for prompt in filtered_prompts 
                              if hasattr(prompt, 'tags') and prompt.tags and selected_tag in prompt.tags]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if self.active_filters.get('favorite', False):
                self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
            else:
                self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")

    def on_filter_button_clicked(self, filter_key):
        """筛选按钮点击处理方法"""
        print(f"筛选按钮被点击: {filter_key}")
        
        # 切换按钮激活状态和筛选器状态
        if filter_key == "favorite":
            current_state = self.favorite_filter_btn.is_active
            new_state = not current_state
            self.favorite_filter_btn.set_active(new_state)
            self.active_filters['favorite'] = new_state
            
            # 应用筛选并更新视图
            self.apply_filters_and_update_views()
            
        elif filter_key == "category":
            # 切换分类筛选栏的显示/隐藏
            current_state = self.category_filter_btn.is_active
            new_state = not current_state
            self.category_filter_btn.set_active(new_state)
            
            # 切换分类筛选栏的可见性
            self.category_filter_bar.setVisible(new_state)
            
        elif filter_key == "tag":
            current_state = self.tag_filter_btn.is_active
            self.tag_filter_btn.set_active(not current_state)
        
        # 更新状态栏显示
        if self.parent_window:
            filter_names = {
                "favorite": "收藏",
                "category": "分类", 
                "tag": "标签"
            }
            if filter_key == "favorite":
                status = "激活" if self.active_filters['favorite'] else "取消"
            elif filter_key == "category":
                status = "显示" if self.category_filter_btn.is_active else "隐藏"
            else:
                status = "激活" if getattr(getattr(self, f"{filter_key}_filter_btn"), "is_active") else "取消"
            self.parent_window.status_bar.set_status(f"{status}{filter_names[filter_key]}筛选")

    def populate_category_filters(self):
        """动态填充分类筛选按钮"""
        # 清除现有按钮
        for i in reversed(range(self.category_filter_layout.count())):
            child = self.category_filter_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        # 从所有提示词中提取唯一的分类名称
        categories = set()
        for prompt in self.all_prompts:
            if hasattr(prompt, 'category') and prompt.category and prompt.category.strip():
                categories.add(prompt.category.strip())
        
        # 排序分类列表
        sorted_categories = sorted(list(categories))
        
        # 创建"全部"按钮
        all_btn = self.create_category_button("全部", is_all_button=True)
        all_btn.clicked.connect(lambda: self.on_category_filter_clicked("全部"))
        self.category_filter_layout.addWidget(all_btn)
        
        # 为每个分类创建按钮
        for category in sorted_categories:
            btn = self.create_category_button(category)
            btn.clicked.connect(lambda checked, cat=category: self.on_category_filter_clicked(cat))
            self.category_filter_layout.addWidget(btn)
        
        # 添加弹性空间
        self.category_filter_layout.addStretch()
        
    def create_category_button(self, category_name, is_all_button=False):
        """创建分类按钮"""
        btn = QPushButton(category_name)
        btn.setFixedHeight(32)
        btn.category_name = category_name
        btn.is_active = False
        
        # 设置按钮样式
        if is_all_button:
            # "全部"按钮的特殊样式
            btn.setStyleSheet('''
                QPushButton {
                    background-color: #3B82F6;
                    color: white;
                    border: 1px solid #3B82F6;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #2563EB;
                    border-color: #2563EB;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            ''')
            btn.is_active = True  # "全部"按钮默认激活
        else:
            # 普通分类按钮样式
            btn.setStyleSheet('''
                QPushButton {
                    background-color: white;
                    color: #374151;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 6px 12px;
                    font-size: 12px;
                    font-weight: 400;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                    border-color: #9CA3AF;
                }
                QPushButton:pressed {
                    background-color: #E5E7EB;
                }
            ''')
        
        return btn
    
    def on_category_filter_clicked(self, category_name):
        """分类筛选按钮点击处理"""
        print(f"分类筛选被点击: {category_name}")
        
        # 更新按钮状态
        for i in range(self.category_filter_layout.count()):
            item = self.category_filter_layout.itemAt(i)
            if item and item.widget():
                btn = item.widget()
                if hasattr(btn, 'category_name'):
                    is_selected = btn.category_name == category_name
                    btn.is_active = is_selected
                    
                    if is_selected:
                        if category_name == "全部":
                            # "全部"按钮激活样式
                            btn.setStyleSheet('''
                                QPushButton {
                                    background-color: #3B82F6;
                                    color: white;
                                    border: 1px solid #3B82F6;
                                    border-radius: 6px;
                                    padding: 6px 12px;
                                    font-size: 12px;
                                    font-weight: 500;
                                }
                                QPushButton:hover {
                                    background-color: #2563EB;
                                    border-color: #2563EB;
                                }
                            ''')
                        else:
                            # 普通分类按钮激活样式
                            btn.setStyleSheet('''
                                QPushButton {
                                    background-color: #EBF4FF;
                                    color: #1E40AF;
                                    border: 1px solid #3B82F6;
                                    border-radius: 6px;
                                    padding: 6px 12px;
                                    font-size: 12px;
                                    font-weight: 500;
                                }
                                QPushButton:hover {
                                    background-color: #DBEAFE;
                                    border-color: #2563EB;
                                }
                            ''')
                    else:
                        # 未激活状态样式
                        btn.setStyleSheet('''
                            QPushButton {
                                background-color: white;
                                color: #374151;
                                border: 1px solid #D1D5DB;
                                border-radius: 6px;
                                padding: 6px 12px;
                                font-size: 12px;
                                font-weight: 400;
                            }
                            QPushButton:hover {
                                background-color: #F3F4F6;
                                border-color: #9CA3AF;
                            }
                        ''')
        
        # 应用分类筛选
        self.apply_category_filter(category_name)
        
        # 更新状态栏
        if self.parent_window:
            if category_name == "全部":
                self.parent_window.status_bar.set_status("显示所有分类")
            else:
                self.parent_window.status_bar.set_status(f"筛选分类: {category_name}")
    
    def apply_category_filter(self, category_name):
        """应用分类筛选"""
        if category_name == "全部":
            # 显示所有提示词（但仍要考虑其他筛选器）
            filtered_prompts = self.all_prompts[:]
        else:
            # 按分类筛选
            filtered_prompts = [prompt for prompt in self.all_prompts 
                              if hasattr(prompt, 'category') and prompt.category == category_name]
        
        # 如果收藏筛选也激活，需要进一步筛选
        if self.active_filters.get('favorite', False):
            filtered_prompts = [prompt for prompt in filtered_prompts if getattr(prompt, 'is_favorite', 0) == 1]
        
        # 更新UI显示
        if self.parent_window:
            self.parent_window.update_prompt_list(filtered_prompts)
            
            # 更新状态栏信息
            total_count = len(self.all_prompts)
            filtered_count = len(filtered_prompts)
            if category_name == "全部":
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"收藏: {filtered_count}/{total_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"共 {total_count} 个提示词")
            else:
                if self.active_filters.get('favorite', False):
                    self.parent_window.status_bar.set_info(f"{category_name} (收藏): {filtered_count} 个提示词")
                else:
                    self.parent_window.status_bar.set_info(f"{category_name}: {filtered_count} 个提示词")

    def refresh_prompt_list(self):

def get_active_filter_button_style(self):
    """Get active filter button style"""
    return '''
        QPushButton {
            background-color: #0EA5E9;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        QPushButton:hover {
            background-color: #0284C7;
        }
        QToolTip {
            background-color: #374151;
            color: white;
            border: 1px solid #4B5563;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }
    '''



def get_filter_button_style(self):
    """Get 
        """刷新提示词列表"""
        if self.parent_window:
            self.parent_window.load_data()

class PromptAssistantRedesigned(QMainWindow):
    """重构版主窗口"""
    
    def __init__(self):
        super().__init__()
        self.model = PromptModel("sqlite")
        self.init_ui()
        self.load_data()
        
    def init_ui(self):
        # 设置窗口属性
        self.setWindowTitle("Prompt收藏助手")
        self.setFixedSize(460, 800)
        self.setWindowFlags(Qt.FramelessWindowHint)  # 无标题栏
        
        # 设置窗口样式 - 移除圆角，改为直角边框
        self.setStyleSheet('''
            QMainWindow {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 0px;
            }
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        ''')
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 (水平布局)
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 左侧导航栏
        self.nav_bar = NavigationBar(self)
        main_layout.addWidget(self.nav_bar)
        
        # 右侧主区域
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        
        # 自定义标题栏
        self.title_bar = CustomTitleBar(self)
        right_layout.addWidget(self.title_bar)
        
        # 内容区域
        self.content_area = ContentArea(self)
        right_layout.addWidget(self.content_area)
        
        # 状态栏
        self.status_bar = StatusBar(self)
        right_layout.addWidget(self.status_bar)
        
        main_layout.addWidget(right_widget)
        
        # 居中显示窗口
        self.center_window()
        
    def center_window(self):
        """窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        x = (screen.width() - self.width()) // 2
        y = (screen.height() - self.height()) // 2
        self.move(x, y)
        
    def switch_page(self, page_key):
        """切换页面"""
        self.content_area.switch_to_page(page_key)
        
        # 更新标题栏标题
        titles = {
            "home": "我的提示词",
            "trash": "回收站",
            "settings": "设置",
            "help": "帮助"
        }
        
        if page_key in titles:
            self.title_bar.set_title(titles[page_key])
            self.status_bar.set_status(f"已切换到{titles[page_key]}")
            
    def load_data(self):
        """加载数据"""
        try:
            prompts = self.model.get_all_prompts()
            # 将数据传递给 ContentArea 并应用筛选
            self.content_area.all_prompts = prompts
            # 填充分类筛选按钮
            self.content_area.populate_category_filters()
            self.content_area.populate_tag_filters()
            self.content_area.apply_filters_and_update_views()
            self.status_bar.set_status("数据加载完成")
        except Exception as e:
            self.status_bar.set_status(f"加载失败: {str(e)}")
            
    def update_prompt_list(self, prompts):
        """更新提示词列表显示"""
        if hasattr(self.content_area, 'prompt_list'):
            # 更新列表视图
            self.content_area.prompt_list.clear()
            for prompt in prompts:
                item_text = f"{prompt.title}\n{prompt.content[:50]}..." if len(prompt.content) > 50 else f"{prompt.title}\n{prompt.content}"
                self.content_area.prompt_list.addItem(item_text)
            
            # 更新卡片视图
            try:
                if hasattr(self.content_area, 'update_card_view') and hasattr(self.content_area, 'cards_list_layout'):
                    self.content_area.update_card_view(prompts)
            except Exception as e:
                import traceback
                print(f"更新卡片视图时出错: {e}")
                traceback.print_exc()
            
    def refresh_prompt_list(self):
        """刷新提示词列表"""
        self.load_data()
        self.status_bar.set_status("提示词列表已刷新")

def main():
    app = QApplication(sys.argv)
    
    # 设置应用样式
    app.setStyle('Fusion')
    
    window = PromptAssistantRedesigned()
    window.show()
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()