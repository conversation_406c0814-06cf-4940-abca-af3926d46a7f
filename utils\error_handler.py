#!/usr/bin/env python3
"""
错误处理模块
提供统一的异常处理机制和自定义异常类
"""
import logging
import sys
import traceback
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import QMessageBox

# 导入配置
try:
    from config.app_config import LOG_LEVEL, LOG_FILE
except ImportError:
    # 默认配置
    LOG_LEVEL = "INFO"
    LOG_FILE = Path(__file__).parent.parent / "app.log"

# 配置日志
logging.basicConfig(
    filename=LOG_FILE,
    level=getattr(logging, LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger('PromptAssistant')


class AppError(Exception):
    """应用程序基础异常类"""
    def __init__(self, message, details=None):
        self.message = message
        self.details = details
        super().__init__(self.message)
        
    def __str__(self):
        if self.details:
            return f"{self.message} - {self.details}"
        return self.message


class DataError(AppError):
    """数据错误异常"""
    pass


class DatabaseError(AppError):
    """数据库错误异常"""
    pass


class UIError(AppError):
    """UI错误异常"""
    pass


class ConfigError(AppError):
    """配置错误异常"""
    pass


class NetworkError(AppError):
    """网络错误异常"""
    pass


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理函数，可以设置为sys.excepthook"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 正常退出，不处理键盘中断
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
        
    error_msg = ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
    logger.critical(f"未捕获的异常: {error_msg}")
    
    # 在这里可以添加其他处理，如发送错误报告等
    
    # 显示对用户友好的错误对话框
    from PySide6.QtWidgets import QApplication
    if QApplication.instance():
        show_error_dialog("应用程序错误", 
                         f"发生了意外错误: {str(exc_value)}", 
                         error_msg)


def show_error_dialog(title, message, details=None):
    """显示错误对话框"""
    msg_box = QMessageBox()
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    
    if details:
        msg_box.setDetailedText(details)
        
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.exec()


def log_error(error, level=logging.ERROR):
    """记录错误到日志"""
    if isinstance(error, Exception):
        logger.log(level, str(error), exc_info=error)
    else:
        logger.log(level, str(error))


def setup_exception_handling():
    """设置全局异常处理"""
    sys.excepthook = handle_exception
    
    # 为未处理的Qt异常添加处理
    # 注意：这需要在QApplication创建后调用
    def qt_message_handler(mode, context, message):
        if mode == QtCore.QtMsgType.QtCriticalMsg or mode == QtCore.QtMsgType.QtFatalMsg:
            logger.critical(f"Qt错误: {message}")
        elif mode == QtCore.QtMsgType.QtWarningMsg:
            logger.warning(f"Qt警告: {message}")
            
    # 由于导入问题，这部分需要在应用程序启动时设置
    # QtCore.qInstallMessageHandler(qt_message_handler)


# 辅助函数
def safe_call(func, *args, error_return=None, **kwargs):
    """安全调用函数，捕获所有异常"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        log_error(e)
        return error_return 