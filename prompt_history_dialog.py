#!/usr/bin/env python3
"""
提示词历史版本对话框
显示提示词的历史版本，支持查看、恢复和使用历史版本
"""
import sys
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                               QPushButton, QListWidget, QListWidgetItem, 
                               QTextEdit, QSplitter, QWidget, QFrame,
                               QMessageBox, QApplication, QScrollArea)
from PySide6.QtCore import Qt, QSize, QPoint
from PySide6.QtGui import QFont, QColor
from datetime import datetime
import json

# 导入FollowingDialog类
from create_prompt_dialog import FollowingDialog
# 不在这里导入ThumbnailCarousel，避免循环导入
# 将在需要的方法中导入

class PromptHistoryDialog(FollowingDialog):
    """提示词历史版本对话框"""
    
    def __init__(self, prompt_id, parent=None):
        super().__init__(parent)
        self.prompt_id = prompt_id
        self.parent_window = parent
        self.model = None
        if hasattr(parent, 'model'):
            self.model = parent.model
        
        # 初始化ColorService
        self.color_service = None
        if hasattr(parent, 'color_service'):
            self.color_service = parent.color_service
        else:
            try:
                from services.color_service import ColorService
                self.color_service = ColorService()
            except ImportError:
                self.color_service = None

        if self.model:
            self.prompt = self.model.get_prompt(self.prompt_id)
        else:
            self.prompt = None
            
        self.selected_history = None
        
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.Dialog)
        self.setFixedSize(700, 650)  # 增加高度以容纳媒体预览
        self.setWindowTitle("提示词历史版本")
        self.init_ui()
        self.load_history_versions()
        # 不需要调用center_dialog，因为FollowingDialog的showEvent会处理位置
        
    def init_ui(self):
        """初始化UI"""
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """)
        
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 标题栏
        self.create_title_bar(main_layout)
        
        # 内容区域
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(20, 20, 20, 20)
        content_layout.setSpacing(15)
        
        # 提示信息
        prompt_info = QLabel(f"'{self.prompt.title}' 的历史版本")
        prompt_info.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 5px;
            }
        """)
        content_layout.addWidget(prompt_info)
        
        # 分割区域
        splitter = QSplitter(Qt.Horizontal)
        splitter.setHandleWidth(1)
        splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #E5E7EB;
            }
        """)
        
        # 左侧版本列表
        self.create_version_list(splitter)
        
        # 右侧预览区
        self.create_preview_area(splitter)
        
        # 设置初始分割比例
        splitter.setSizes([200, 450])
        content_layout.addWidget(splitter)
        
        # 功能按钮区域
        self.create_buttons_area(content_layout)
        
        main_layout.addWidget(content_widget)
        
    def create_title_bar(self, layout):
        """创建标题栏"""
        title_bar = QWidget()
        title_bar.setFixedHeight(50)
        title_bar.setStyleSheet("""
            QWidget {
                background-color: #F9FAFB;
                border-bottom: 1px solid #E5E7EB;
            }
        """)
        
        title_layout = QHBoxLayout(title_bar)
        title_layout.setContentsMargins(20, 0, 15, 0)
        
        # 标题
        title_label = QLabel("历史版本")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        title_layout.addWidget(title_label)
        title_layout.addStretch()
        
        # 关闭按钮
        close_btn = QPushButton("×")
        close_btn.setFixedSize(30, 30)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #6B7280;
                border: none;
                border-radius: 15px;
                font-size: 18px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #FEE2E2;
                color: #DC2626;
            }
        """)
        close_btn.clicked.connect(self.close)
        title_layout.addWidget(close_btn)
        
        layout.addWidget(title_bar)
        
        # 用于拖拽
        self.drag_position = QPoint()
        title_bar.mousePressEvent = self.title_bar_mouse_press
        title_bar.mouseMoveEvent = self.title_bar_mouse_move
        
    def title_bar_mouse_press(self, event):
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            
    def title_bar_mouse_move(self, event):
        if event.buttons() == Qt.LeftButton and self.drag_position:
            self.move(event.globalPosition().toPoint() - self.drag_position)
    
    def create_version_list(self, parent):
        """创建版本列表"""
        list_widget = QWidget()
        list_layout = QVBoxLayout(list_widget)
        list_layout.setContentsMargins(0, 0, 0, 0)
        list_layout.setSpacing(10)
        
        # 列表标题
        list_title = QLabel("版本列表")
        list_title.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 13px;
                font-weight: 600;
            }
        """)
        list_layout.addWidget(list_title)
        
        # 版本列表
        self.version_list = QListWidget()
        self.version_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: white;
                padding: 5px;
                font-size: 12px;
            }
            QListWidget::item {
                border-bottom: 1px solid #F3F4F6;
                padding: 8px;
                border-radius: 4px;
                margin: 2px 0;
            }
            QListWidget::item:hover {
                background-color: #F9FAFB;
            }
            QListWidget::item:selected {
                background-color: #EBF5FF;
                color: #1E40AF;
                border: none;
            }
        """)
        self.version_list.currentItemChanged.connect(self.on_version_selected)
        list_layout.addWidget(self.version_list)
        
        parent.addWidget(list_widget)
        
    def create_preview_area(self, parent):
        """创建预览区域"""
        # 导入ThumbnailCarousel，避免循环导入
        from main import ThumbnailCarousel
        
        preview_widget = QWidget()
        preview_layout = QVBoxLayout(preview_widget)
        preview_layout.setContentsMargins(0, 0, 0, 0)
        preview_layout.setSpacing(10)
        
        # 预览标题
        preview_title = QLabel("预览")
        preview_title.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 13px;
                font-weight: 600;
            }
        """)
        preview_layout.addWidget(preview_title)
        
        # 版本信息
        self.version_info = QLabel("选择一个版本查看详细信息")
        self.version_info.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                font-style: italic;
                padding: 5px 0;
            }
        """)
        preview_layout.addWidget(self.version_info)
        
        # 内容预览（调整高度和样式）
        self.content_preview = QTextEdit()
        self.content_preview.setReadOnly(True)
        self.content_preview.setMaximumHeight(180)  # 减少高度为卡片区域让出空间
        self.content_preview.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: #F9FAFB;
                padding: 12px;
                font-size: 13px;
                line-height: 1.5;
                color: #374151;
            }
        """)
        preview_layout.addWidget(self.content_preview)
        
        # 新的卡片式信息预览区域（替换原来的details_preview）
        self.info_cards_container = self._create_info_card_container()
        preview_layout.addWidget(self.info_cards_container)
        
        # 媒体预览区域（优化尺寸）
        media_section = QWidget()
        media_layout = QVBoxLayout(media_section)
        media_layout.setContentsMargins(0, 0, 0, 0)
        media_layout.setSpacing(5)
        
        # 媒体预览标题
        media_title = QLabel("🖼️ 媒体文件")
        media_title.setStyleSheet("""
            QLabel {
                color: #374151;
                font-size: 13px;
                font-weight: 600;
                margin-top: 5px;
            }
        """)
        media_layout.addWidget(media_title)
        
        # 媒体预览组件（从150x150调整为200x150）
        self.media_preview = ThumbnailCarousel()
        self.media_preview.setFixedSize(200, 150)
        self.media_preview.thumbnail_container.setFixedSize(200, 150)
        self.media_preview.thumbnail_label.setFixedSize(198, 148)
        
        # 更新控制按钮位置
        self.media_preview.left_button.move(10, 65)
        self.media_preview.right_button.move(170, 65)  # 调整右按钮位置
        self.media_preview.indicator_label.move(90, 120)  # 调整指示器位置
        
        # 连接缩略图点击信号
        self.media_preview.thumbnailClicked.connect(self.on_thumbnail_clicked)
        
        self.media_preview.setStyleSheet("""
            QWidget {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: #F9FAFB;
            }
        """)
        media_layout.addWidget(self.media_preview)
        
        preview_layout.addWidget(media_section)
        
        parent.addWidget(preview_widget)
        
    def create_buttons_area(self, layout):
        """创建按钮区域"""
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setSpacing(10)
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setFixedHeight(36)
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #F3F4F6;
                color: #374151;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                min-width: 80px;
            }
            QPushButton:hover {
                background-color: #E5E7EB;
            }
        """)
        cancel_btn.clicked.connect(self.close)
        button_layout.addWidget(cancel_btn)
        
        # 使用按钮
        self.use_btn = QPushButton("使用此版本")
        self.use_btn.setFixedHeight(36)
        self.use_btn.setEnabled(False)
        self.use_btn.setStyleSheet("""
            QPushButton {
                background-color: #8B5CF6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #7C3AED;
            }
            QPushButton:disabled {
                background-color: #C4B5FD;
                color: #EDE9FE;
            }
        """)
        self.use_btn.clicked.connect(self.use_history_version)
        button_layout.addWidget(self.use_btn)
        
        # 恢复按钮
        self.restore_btn = QPushButton("恢复到此版本")
        self.restore_btn.setFixedHeight(36)
        self.restore_btn.setEnabled(False)
        self.restore_btn.setStyleSheet("""
            QPushButton {
                background-color: #10B981;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 13px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #059669;
            }
            QPushButton:disabled {
                background-color: #A7F3D0;
                color: #D1FAE5;
            }
        """)
        self.restore_btn.clicked.connect(self.restore_history_version)
        button_layout.addWidget(self.restore_btn)
        
        layout.addLayout(button_layout)
    
    def load_history_versions(self):
        """加载历史版本列表"""
        if not self.model or not self.prompt:
            return
        
        try:
            # 获取历史版本列表
            history_versions = self.model.get_prompt_history(self.prompt.id)
            
            # 如果没有历史版本
            if not history_versions:
                item = QListWidgetItem("没有历史版本记录")
                item.setFlags(item.flags() & ~Qt.ItemIsEnabled)  # 禁用项
                self.version_list.addItem(item)
                return
            
            # 添加到列表
            for history in history_versions:
                # 解析日期时间
                try:
                    dt = datetime.fromisoformat(history.created_at)
                    formatted_date = dt.strftime("%Y-%m-%d %H:%M:%S")
                except ValueError:
                    formatted_date = history.created_at
                    
                item = QListWidgetItem(f"版本 {history.version} - {formatted_date}")
                item.setData(Qt.UserRole, history.id)  # 存储历史版本ID
                self.version_list.addItem(item)
        
        except Exception as e:
            print(f"加载历史版本时出错: {e}")
            item = QListWidgetItem(f"加载历史版本出错: {str(e)}")
            item.setFlags(item.flags() & ~Qt.ItemIsEnabled)  # 禁用项
            self.version_list.addItem(item)
    
    def on_version_selected(self, current, previous):
        """当选择历史版本时"""
        if not current:
            self.selected_history = None
            self.update_preview(None)
            self.restore_btn.setEnabled(False)
            self.use_btn.setEnabled(False)
            return
            
        # 获取选中版本的ID
        history_id = current.data(Qt.UserRole)
        if not history_id:
            self.selected_history = None
            self.update_preview(None)
            return
            
        try:
            # 获取历史版本
            history = self.model.get_history_version(history_id)
            if not history:
                self.selected_history = None
                self.update_preview(None)
                return
                
            self.selected_history = history
            self.update_preview(history)
            self.restore_btn.setEnabled(True)
            self.use_btn.setEnabled(True)
            
        except Exception as e:
            print(f"加载历史版本详情时出错: {e}")
            self.selected_history = None
            self.update_preview(None)
            self.restore_btn.setEnabled(False)
            self.use_btn.setEnabled(False)
    
    def update_preview(self, history):
        """更新预览区域"""
        if not history:
            self.version_info.setText("选择一个版本查看详细信息")
            self.content_preview.setText("")
            # 清空卡片信息
            self._update_info_cards(None)
            self.media_preview.clear() # 清空媒体预览
            return
            
        # 版本信息
        try:
            dt = datetime.fromisoformat(history.created_at)
            formatted_date = dt.strftime("%Y年%m月%d日 %H:%M:%S")
        except ValueError:
            formatted_date = history.created_at
            
        self.version_info.setText(f"版本 {history.version} - {formatted_date}")
        
        # 内容预览
        self.content_preview.setText(history.content)
        
        # 更新信息卡片
        self._update_info_cards(history)

        # 媒体预览
        self.media_preview.clear() # 清空之前的媒体
        if history.media_files and isinstance(history.media_files, list) and history.media_files:
            for file_path in history.media_files:
                print(f"[历史版本] 添加媒体文件到预览: {file_path}")
                self.media_preview.add_item(file_path)
        else:
            self.media_preview.add_item("无媒体文件")

    def _update_info_cards(self, history):
        """更新信息卡片的数据"""
        if not history:
            # 空状态
            self.title_info_label.setText("无标题")
            self.category_info_label.setText("无分类")
            self.version_time_label.setText("")
            
            # 清空标签容器
            layout = self.tags_container.layout()
            if layout:
                while layout.count():
                    child = layout.takeAt(0)
                    if child.widget():
                        child.widget().deleteLater()
            else:
                layout = QHBoxLayout(self.tags_container)
                layout.setContentsMargins(0, 0, 0, 0)
            
            self.type_info_label.setText("无类型")
            self.content_stats_label.setText("无内容")
            self.media_files_label.setText("无媒体文件")
            self.auxiliary_fields_label.setText("无辅助字段")
            return

        # 基本信息卡片数据填充
        self.title_info_label.setText(f"标题: {history.title or '无标题'}")
        
        # 分类信息（使用ColorService着色）
        category_text = f"📁 分类: {history.category or '无分类'}"
        self.category_info_label.setText(category_text)
        
        # 版本时间信息
        try:
            dt = datetime.fromisoformat(history.created_at)
            formatted_date = dt.strftime("%Y-%m-%d %H:%M:%S")
        except ValueError:
            formatted_date = history.created_at
        self.version_time_label.setText(f"⏰ 创建时间: {formatted_date}")

        # 标签和属性卡片数据填充
        tags = history.tags if isinstance(history.tags, list) else []
        
        # 更新标签容器
        layout = self.tags_container.layout()
        if layout:
            # 清空现有标签
            while layout.count():
                child = layout.takeAt(0)
                if child.widget():
                    child.widget().deleteLater()
        else:
            layout = QHBoxLayout(self.tags_container)
            layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加新的标签组件
        tags_widget = self._create_styled_tag_labels(tags)
        layout.addWidget(tags_widget)

        # 确定提示词类型
        prompt_type = "文本"
        if hasattr(history, 'type') and history.type:
            prompt_type = history.type
        elif history.media_files and isinstance(history.media_files, list) and history.media_files:
            # 根据第一个媒体文件的类型判断
            from pathlib import Path
            file_path = Path(history.media_files[0])
            if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                prompt_type = "图片"
            elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                prompt_type = "视频"
        
        type_icon = "📄" if prompt_type == "文本" else ("🖼️" if prompt_type == "图片" else "🎥")
        self.type_info_label.setText(f"{type_icon} 类型: {prompt_type}")
        
        # 内容统计
        self.content_stats_label.setText(self._get_content_stats(history.content))

        # 扩展信息卡片数据填充
        # 媒体文件信息
        if history.media_files and isinstance(history.media_files, list) and history.media_files:
            media_count = len(history.media_files)
            media_icon = "🖼️" if prompt_type == "图片" else ("🎥" if prompt_type == "视频" else "📎")
            self.media_files_label.setText(f"{media_icon} 媒体文件: {media_count} 个文件")
        else:
            self.media_files_label.setText("📎 媒体文件: 无")
        
        # 辅助选择字段信息
        if hasattr(history, 'auxiliary_fields') and history.auxiliary_fields and len(history.auxiliary_fields) > 0:
            fields_count = len(history.auxiliary_fields)
            self.auxiliary_fields_label.setText(f"⚙️ 辅助字段: {fields_count} 个字段")
        else:
            self.auxiliary_fields_label.setText("⚙️ 辅助字段: 无")
    
    def restore_history_version(self):
        """恢复到历史版本"""
        if not self.selected_history:
            return
            
        # 确认对话框
        reply = QMessageBox.question(self, "确认恢复", 
                                    f"确定要恢复到版本 {self.selected_history.version} 吗？当前版本将被替换。",
                                    QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            try:
                # 恢复到历史版本
                success = self.model.restore_prompt_from_history(self.selected_history.id)
                
                if success:
                    QMessageBox.information(self, "成功", f"已成功恢复到版本 {self.selected_history.version}")
                    self.accept()  # 关闭对话框
                else:
                    QMessageBox.warning(self, "错误", "恢复失败，请重试")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"恢复过程中出错: {str(e)}")
    
    def use_history_version(self):
        """使用历史版本（复制到剪贴板）"""
        if not self.selected_history:
            return
            
        # 复制内容到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(self.selected_history.content)
        
        QMessageBox.information(self, "成功", "已复制到剪贴板，可以粘贴到需要的地方使用") 

    def on_thumbnail_clicked(self, index, media_files):
        """处理缩略图点击事件"""
        if not media_files or index >= len(media_files):
            return
            
        # 导入ImageViewerDialog，避免循环导入
        from main import ImageViewerDialog
        
        # 打开图片查看器对话框
        viewer = ImageViewerDialog(media_files, index, self)
        viewer.show()

    # 现代化卡片式预览系统的私有方法
    
    def _create_info_card_container(self):
        """创建卡片容器"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(120)
        scroll_area.setMaximumHeight(200)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: #F9FAFB;
            }
            QScrollBar:vertical {
                background: #F3F4F6;
                width: 6px;
                border-radius: 3px;
            }
            QScrollBar::handle:vertical {
                background: #9CA3AF;
                border-radius: 3px;
                min-height: 20px;
            }
        """)

        # 创建内容容器
        container_widget = QWidget()
        container_layout = QVBoxLayout(container_widget)
        container_layout.setContentsMargins(12, 12, 12, 12)
        container_layout.setSpacing(8)

        # 创建三个信息卡片
        self.basic_info_card = self._create_basic_info_card()
        self.tags_attributes_card = self._create_tags_attributes_card()
        self.media_extension_card = self._create_media_extension_card()

        container_layout.addWidget(self.basic_info_card)
        container_layout.addWidget(self.tags_attributes_card)
        container_layout.addWidget(self.media_extension_card)
        container_layout.addStretch()

        scroll_area.setWidget(container_widget)
        return scroll_area

    def _create_basic_info_card(self):
        """创建基本信息卡片"""
        card = QFrame()
        card.setStyleSheet(self._get_card_style())
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(6)

        # 卡片标题
        title_label = QLabel("📄 基本信息")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 4px;
            }
        """)
        layout.addWidget(title_label)

        # 信息内容区域
        self.title_info_label = QLabel()
        self.category_info_label = QLabel()
        self.version_time_label = QLabel()

        info_style = """
            QLabel {
                color: #4B5563;
                font-size: 12px;
                margin: 2px 0;
                padding: 2px 0;
            }
        """
        self.title_info_label.setStyleSheet(info_style)
        self.category_info_label.setStyleSheet(info_style)
        self.version_time_label.setStyleSheet(info_style)

        layout.addWidget(self.title_info_label)
        layout.addWidget(self.category_info_label)
        layout.addWidget(self.version_time_label)

        return card

    def _create_tags_attributes_card(self):
        """创建标签和属性卡片"""
        card = QFrame()
        card.setStyleSheet(self._get_card_style())
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(6)

        # 卡片标题
        title_label = QLabel("🏷️ 标签与属性")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 4px;
            }
        """)
        layout.addWidget(title_label)

        # 属性内容区域
        self.tags_container = QWidget()
        self.type_info_label = QLabel()
        self.content_stats_label = QLabel()

        info_style = """
            QLabel {
                color: #4B5563;
                font-size: 12px;
                margin: 2px 0;
                padding: 2px 0;
            }
        """
        self.type_info_label.setStyleSheet(info_style)
        self.content_stats_label.setStyleSheet(info_style)

        layout.addWidget(self.tags_container)
        layout.addWidget(self.type_info_label)
        layout.addWidget(self.content_stats_label)

        return card

    def _create_media_extension_card(self):
        """创建媒体和扩展信息卡片"""
        card = QFrame()
        card.setStyleSheet(self._get_card_style())
        
        layout = QVBoxLayout(card)
        layout.setContentsMargins(12, 12, 12, 12)
        layout.setSpacing(6)

        # 卡片标题
        title_label = QLabel("⚙️ 扩展信息")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 13px;
                font-weight: 600;
                margin-bottom: 4px;
            }
        """)
        layout.addWidget(title_label)

        # 扩展信息内容区域
        self.media_files_label = QLabel()
        self.auxiliary_fields_label = QLabel()

        info_style = """
            QLabel {
                color: #4B5563;
                font-size: 12px;
                margin: 2px 0;
                padding: 2px 0;
            }
        """
        self.media_files_label.setStyleSheet(info_style)
        self.auxiliary_fields_label.setStyleSheet(info_style)

        layout.addWidget(self.media_files_label)
        layout.addWidget(self.auxiliary_fields_label)

        return card

    def _create_styled_category_label(self, category_name):
        """创建带颜色的分类标签"""
        if not category_name:
            return QLabel("无分类")

        label = QLabel(category_name)
        
        # 获取分类颜色
        color = "#6B7280"  # 默认颜色
        if self.color_service:
            try:
                color = self.color_service.get_category_color(category_name)
            except:
                pass

        label.setStyleSheet(f"""
            QLabel {{
                background-color: {color}20;
                color: {color};
                border: 1px solid {color}40;
                border-radius: 4px;
                padding: 2px 6px;
                font-size: 11px;
                font-weight: 500;
            }}
        """)
        
        return label

    def _create_styled_tag_labels(self, tags):
        """创建样式化标签组"""
        container = QWidget()
        layout = QHBoxLayout(container)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)

        if not tags:
            no_tags_label = QLabel("无标签")
            no_tags_label.setStyleSheet("""
                QLabel {
                    color: #9CA3AF;
                    font-size: 11px;
                    font-style: italic;
                    padding: 2px 0;
                }
            """)
            layout.addWidget(no_tags_label)
        else:
            for tag in tags[:5]:  # 最多显示5个标签
                tag_label = QLabel(tag)
                
                # 获取标签颜色
                color = "#6B7280"  # 默认颜色
                if self.color_service:
                    try:
                        color = self.color_service.get_tag_color(tag)
                    except:
                        pass

                tag_label.setStyleSheet(f"""
                    QLabel {{
                        background-color: {color}15;
                        color: {color};
                        border: 1px solid {color}30;
                        border-radius: 3px;
                        padding: 1px 4px;
                        font-size: 10px;
                        font-weight: 500;
                    }}
                """)
                layout.addWidget(tag_label)

            if len(tags) > 5:
                more_label = QLabel(f"+{len(tags) - 5}")
                more_label.setStyleSheet("""
                    QLabel {
                        color: #9CA3AF;
                        font-size: 10px;
                        padding: 1px 4px;
                    }
                """)
                layout.addWidget(more_label)

        layout.addStretch()
        return container

    def _get_content_stats(self, content):
        """获取内容统计信息"""
        if not content:
            return "内容: 空"
        
        char_count = len(content)
        line_count = content.count('\n') + 1
        word_count = len(content.split())
        
        return f"📊 {char_count} 字符, {line_count} 行, {word_count} 词"

    def _get_card_style(self):
        """获取卡片样式"""
        return """
            QFrame {
                background-color: #FFFFFF;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
                margin: 0px;
            }
            QFrame:hover {
                border: 1px solid #D1D5DB;
                background-color: #F9FAFB;
            }
        """ 