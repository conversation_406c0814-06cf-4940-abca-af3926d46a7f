import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import PromptModel, Prompt
from create_prompt_dialog import CreatePromptDialog
from PySide6.QtWidgets import QApplication

def test_auxiliary_save():
    app = QApplication(sys.argv)
    
    model = PromptModel()
    
    # 创建一个新的提示词
    prompt = Prompt(
        title="测试辅助选择保存",
        content="测试内容",
        category="通用",
        tags=["测试"],
        auxiliary_fields={
            "负向提示词": "这是负向提示词内容",
            "参数设置": "steps=20, cfg=7.5"
        }
    )
    
    # 保存到数据库
    prompt_id = model.add_prompt(prompt)
    print(f"保存的提示词ID: {prompt_id}")
    
    # 从数据库获取
    retrieved_prompt = model.get_prompt(prompt_id)
    print(f"从数据库获取的auxiliary_fields: {retrieved_prompt.auxiliary_fields}")
    
    # 创建编辑对话框
    dialog = CreatePromptDialog(edit_prompt=retrieved_prompt)
    
    # 模拟用户修改辅助选择字段
    # 首先需要加载数据
    dialog.load_prompt_data()
    
    # 检查动态字段是否正确加载
    print(f"动态字段: {list(dialog.dynamic_fields.keys())}")
    
    # 模拟用户修改字段内容
    if "负向提示词" in dialog.dynamic_fields:
        field_input = dialog.dynamic_fields["负向提示词"]["input"]
        if hasattr(field_input, 'setPlainText'):
            field_input.setPlainText("修改后的负向提示词")
        else:
            field_input.setText("修改后的负向提示词")
        print("已修改负向提示词字段")
    
    if "参数设置" in dialog.dynamic_fields:
        field_input = dialog.dynamic_fields["参数设置"]["input"]
        if hasattr(field_input, 'setPlainText'):
            field_input.setPlainText("steps=30, cfg=8.0")
        else:
            field_input.setText("steps=30, cfg=8.0")
        print("已修改参数设置字段")
    
    # 获取动态字段数据
    dynamic_data = dialog.get_dynamic_fields_data()
    print(f"获取的动态字段数据: {dynamic_data}")
    
    # 模拟保存
    retrieved_prompt.auxiliary_fields = dynamic_data
    model.update_prompt(retrieved_prompt)
    
    # 再次从数据库获取
    updated_prompt = model.get_prompt(prompt_id)
    print(f"保存后的auxiliary_fields: {updated_prompt.auxiliary_fields}")

if __name__ == "__main__":
    test_auxiliary_save() 