import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import PromptModel, Prompt
from create_prompt_dialog import CreatePromptDialog
from PySide6.QtWidgets import QApplication

def test_auxiliary_issue():
    app = QApplication(sys.argv)
    
    model = PromptModel()
    
    # 创建一个新的提示词，包含媒体文件
    prompt = Prompt(
        title="测试辅助选择问题",
        content="测试内容",
        category="通用",
        tags=["测试"],
        auxiliary_fields={
            "负向提示词": "这是负向提示词内容",
            "参数设置": "steps=20, cfg=7.5"
        },
        media_files=["test1.jpg", "test2.mp4"]
    )
    
    # 保存到数据库
    prompt_id = model.add_prompt(prompt)
    print(f"保存的提示词ID: {prompt_id}")
    
    # 从数据库获取
    retrieved_prompt = model.get_prompt(prompt_id)
    print(f"从数据库获取的auxiliary_fields: {retrieved_prompt.auxiliary_fields}")
    print(f"从数据库获取的media_files: {retrieved_prompt.media_files}")
    
    # 检查是否有问题
    if retrieved_prompt.auxiliary_fields == retrieved_prompt.media_files:
        print("问题发现：auxiliary_fields 和 media_files 相同！")
    else:
        print("数据正常")
    
    # 创建编辑对话框
    dialog = CreatePromptDialog(edit_prompt=retrieved_prompt)
    
    # 检查编辑对话框中的动态字段
    dialog.load_prompt_data()
    print(f"编辑对话框中的动态字段: {list(dialog.dynamic_fields.keys())}")
    
    # 检查动态字段的值
    for field_name, field_info in dialog.dynamic_fields.items():
        field_input = field_info['input']
        if hasattr(field_input, 'toPlainText'):
            value = field_input.toPlainText()
        else:
            value = field_input.text()
        print(f"字段 '{field_name}' 的值: '{value}'")
    
    # 获取动态字段数据
    dynamic_data = dialog.get_dynamic_fields_data()
    print(f"获取的动态字段数据: {dynamic_data}")
    
    # 检查是否有问题
    if dynamic_data == retrieved_prompt.media_files:
        print("问题发现：动态字段数据 和 media_files 相同！")
    else:
        print("动态字段数据正常")

if __name__ == "__main__":
    test_auxiliary_issue() 