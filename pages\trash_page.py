#!/usr/bin/env python3
"""
回收站页面组件
从ContentArea提取的回收站相关逻辑
"""
from PySide6.QtWidgets import QVBoxLayout, QHBoxLayout, QScrollArea, QWidget, QLabel, QPushButton, QMessageBox
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from pages.base_page import BasePage
from components.style_manager import StyleManager


class TrashPage(BasePage):
    """回收站页面 - 继承BasePage，处理回收站相关功能"""
    
    # 定义信号
    restore_requested = Signal(int)  # 恢复提示词信号
    permanent_delete_requested = Signal(int)  # 永久删除提示词信号
    empty_trash_requested = Signal()  # 清空回收站信号
    
    # 新增批量操作信号
    batch_restore_requested = Signal(list)  # 批量恢复信号，传递prompt_id列表
    batch_delete_requested = Signal(list)  # 批量删除信号，传递prompt_id列表
    
    def __init__(self, parent=None):
        # 先初始化必要的属性，再调用父类初始化
        self.trash_prompts = []
        self.trash_cards = []
        self.trash_selected_cards = []
        super().__init__(parent)
        
    def setup_ui(self):
        """设置UI布局 - 从ContentArea.create_trash_page提取"""
        print(f"🏗️  TrashPage.setup_ui() 开始...")
        main_layout = self.get_main_layout()
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # 页面标题
        title_label = QLabel("回收站")
        title_label.setFont(QFont("微软雅黑", 16, QFont.Bold))
        title_label.setStyleSheet("color: #1F2937; margin-bottom: 10px;")
        main_layout.addWidget(title_label)
        
        # 工具栏 - 恢复和删除按钮
        toolbar_layout = QHBoxLayout()
        
        # 恢复选中按钮
        self.restore_selected_btn = QPushButton("恢复选中")
        self.restore_selected_btn.setEnabled(False)
        self.restore_selected_btn.setStyleSheet(StyleManager.get_button_style("secondary"))
        toolbar_layout.addWidget(self.restore_selected_btn)
        
        # 永久删除选中按钮
        self.delete_selected_btn = QPushButton("永久删除选中")
        self.delete_selected_btn.setEnabled(False)
        self.delete_selected_btn.setStyleSheet(StyleManager.get_button_style("danger"))
        toolbar_layout.addWidget(self.delete_selected_btn)
        
        # 清空回收站按钮
        self.empty_trash_btn = QPushButton("清空回收站")
        self.empty_trash_btn.setStyleSheet(StyleManager.get_button_style("danger_outline"))
        toolbar_layout.addWidget(self.empty_trash_btn)
        
        toolbar_layout.addStretch()
        main_layout.addLayout(toolbar_layout)
        
        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        
        # 应用StyleManager滚动条样式
        scrollbar_style = StyleManager.get_scrollbar_style()
        self.scroll_area.setStyleSheet(scrollbar_style)
        
        # 内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(10, 10, 10, 10)
        self.content_layout.setSpacing(12)
        
        # 确保内容容器有合适的尺寸策略
        from PySide6.QtWidgets import QSizePolicy
        self.content_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
        # 设置焦点策略，确保页面能接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        self.scroll_area.setWidget(self.content_widget)
        main_layout.addWidget(self.scroll_area)
        
        # 连接信号
        self.restore_selected_btn.clicked.connect(self.restore_selected_prompts)
        self.delete_selected_btn.clicked.connect(self.permanently_delete_selected_prompts)
        self.empty_trash_btn.clicked.connect(self.empty_trash)
        
        print(f"✅ TrashPage.setup_ui() 完成!")
        print(f"📋 创建的组件: 标题标签, 3个按钮, 滚动区域")
        print(f"📏 主布局边距: 20px, 间距: 20px")
        
    def load_data(self):
        """实现BasePage要求的抽象方法 - 初始化时调用"""
        # 初始化时不加载数据，等待页面切换时传入model参数再加载
        print("🔄 TrashPage初始化 - 等待model参数")
        pass
        
    def load_data_with_model(self, trash_prompts=None, model=None):
        """加载回收站数据 - 从ContentArea.load_trash_content提取"""
        print(f"📋 load_data_with_model被调用: model={model is not None}, trash_prompts={trash_prompts is not None}")
        
        # 如果提供了model，获取已删除的提示词
        if model:
            try:
                self.trash_prompts = model.get_deleted_prompts()
                print(f"📊 从model获取到 {len(self.trash_prompts)} 条已删除提示词")
            except Exception as e:
                print(f"❌ 从model获取数据失败: {e}")
                self.trash_prompts = []
        elif trash_prompts is not None:
            self.trash_prompts = trash_prompts
            print(f"📊 使用传入的trash_prompts: {len(self.trash_prompts)} 条")
        else:
            self.trash_prompts = []
            print("📊 没有数据源，使用空列表")
            
        print(f"🧹 开始清理现有UI内容...")
        # 清除现有内容
        for i in reversed(range(self.content_layout.count())):
            child = self.content_layout.itemAt(i).widget()
            if child:
                child.setParent(None)
        
        self.trash_cards.clear()
        self.trash_selected_cards.clear()
        print(f"✅ UI内容清理完成")
        
        if not self.trash_prompts:
            # 如果回收站为空，显示空状态提示
            print("📭 回收站为空，显示空状态提示")
            empty_label = QLabel("回收站为空")
            empty_label.setAlignment(Qt.AlignCenter)
            empty_label.setStyleSheet("""
                QLabel {
                    color: #9CA3AF;
                    font-size: 14px;
                    padding: 40px;
                }
            """)
            self.content_layout.addWidget(empty_label)
        else:
            # 添加回收站项目
            print(f"🏗️  开始创建 {len(self.trash_prompts)} 个回收站卡片...")
            for i, prompt in enumerate(self.trash_prompts):
                try:
                    print(f"  创建卡片 {i+1}/{len(self.trash_prompts)}: {prompt.title[:30]}...")
                    card = self.create_trash_item(prompt)
                    self.trash_cards.append(card)
                    self.content_layout.addWidget(card)
                    print(f"  ✅ 卡片 {i+1} 创建成功")
                except Exception as e:
                    print(f"  ❌ 卡片 {i+1} 创建失败: {e}")
            print(f"✅ 所有卡片创建完成")
            
        # 添加弹簧
        self.content_layout.addStretch()
        
        # 更新按钮状态
        self.update_selection_status()
        
        # 强制刷新UI
        self.content_widget.updateGeometry()
        self.scroll_area.updateGeometry()
        self.updateGeometry()
        self.update()
        
        print(f"🔄 load_data_with_model完成，UI已刷新")
        
    def create_trash_item(self, prompt):
        """创建回收站项目 - 从ContentArea.create_trash_item提取"""
        # 导入TrashPromptCard（需要在这里动态导入避免循环导入）
        from main import TrashPromptCard
        
        # 创建TrashPromptCard
        card = TrashPromptCard(prompt, self)
        
        # 连接卡片的选中状态变化信号
        card.clicked.connect(lambda checked=False, c=card: self.on_card_selection_changed(c, c.is_selected if hasattr(c, 'is_selected') else False))
        
        # 确保卡片可见
        card.show()
        card.setVisible(True)
        
        return card
        
    def restore_selected_prompts(self):
        """恢复选中的提示词"""
        if not self.trash_selected_cards:
            return
            
        reply = QMessageBox.question(
            self, 
            "确认恢复", 
            f"确定要恢复选中的 {len(self.trash_selected_cards)} 个提示词吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.Yes
        )
        
        if reply == QMessageBox.Yes:
            prompt_ids = []
            for card in self.trash_selected_cards[:]:
                if hasattr(card, 'prompt'):
                    prompt_ids.append(card.prompt.id)
            if prompt_ids:
                self.batch_restore_requested.emit(prompt_ids)
                
    def permanently_delete_selected_prompts(self):
        """永久删除选中的提示词"""
        if not self.trash_selected_cards:
            return
            
        reply = QMessageBox.question(
            self, 
            "确认删除", 
            f"确定要永久删除选中的 {len(self.trash_selected_cards)} 个提示词吗？\n此操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            prompt_ids = []
            for card in self.trash_selected_cards[:]:
                if hasattr(card, 'prompt'):
                    prompt_ids.append(card.prompt.id)
            if prompt_ids:
                self.batch_delete_requested.emit(prompt_ids)
                    
    def empty_trash(self):
        """清空回收站"""
        if not self.trash_prompts:
            QMessageBox.information(self, "提示", "回收站已经是空的")
            return
            
        reply = QMessageBox.question(
            self,
            "确认清空",
            f"确定要清空回收站吗？\n将永久删除 {len(self.trash_prompts)} 个提示词，此操作不可恢复！",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.empty_trash_requested.emit()
            
    def update_selection_status(self):
        """更新选择状态"""
        selected_count = len(self.trash_selected_cards)
        self.restore_selected_btn.setEnabled(selected_count > 0)
        self.delete_selected_btn.setEnabled(selected_count > 0)
        
    def on_card_selection_changed(self, card, is_selected):
        """处理卡片选择状态变化"""
        if is_selected:
            if card not in self.trash_selected_cards:
                self.trash_selected_cards.append(card)
        else:
            if card in self.trash_selected_cards:
                self.trash_selected_cards.remove(card)
                
        self.update_selection_status()
        
    def clear_trash_selections(self):
        """清除回收站中所有卡片的选中状态"""
        for card in self.trash_selected_cards:
            if hasattr(card, 'set_selected'):
                card.set_selected(False)
        self.trash_selected_cards.clear()
        self.update_selection_status()
    
    def select_all_trash_cards(self):
        """全选回收站中的所有卡片"""
        # 遍历所有卡片并选中
        for card in self.trash_cards:
            if hasattr(card, 'set_selected') and hasattr(card, 'is_selected'):
                card.is_selected = True
                card.set_selected(True)
                if card not in self.trash_selected_cards:
                    self.trash_selected_cards.append(card)
                # 强制刷新卡片UI
                card.update()
        
        self.update_selection_status()
        
    def on_trash_card_clicked(self, card):
        """处理回收站卡片点击事件"""
        # 触发卡片选择状态变化
        is_selected = hasattr(card, 'is_selected') and card.is_selected
        self.on_card_selection_changed(card, is_selected)
    
    def refresh(self) -> None:
        """重写refresh方法，确保可以刷新回收站数据"""
        print("🔄 正在刷新回收站页面...")
        
        # 尝试从多个途径获取model
        model = None
        
        # 方法1: 从parent链查找
        parent = self.parent()
        while parent:
            if hasattr(parent, 'model'):
                model = parent.model
                print(f"✅ 从父级 {type(parent).__name__} 找到model")
                break
            if hasattr(parent, 'parent_window') and hasattr(parent.parent_window, 'model'):
                model = parent.parent_window.model
                print(f"✅ 从父级的parent_window找到model")
                break
            parent = parent.parent()
        
        # 方法2: 如果方法1失败，尝试从window查找
        if not model:
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                for widget in app.allWidgets():
                    if hasattr(widget, 'model') and widget.__class__.__name__ == 'PromptAssistantRedesigned':
                        model = widget.model
                        print("✅ 从应用程序窗口找到model")
                        break
        
        if model:
            print(f"🔄 开始加载回收站数据...")
            self.load_data_with_model(model=model)
            print(f"✅ 回收站数据加载完成")
        else:
            print("❌ 无法找到model，显示空状态")
            self.load_data_with_model(trash_prompts=[])
    
    def get_parent_window(self):
        """获取父窗口，用于状态栏更新"""
        parent = self.parent()
        while parent:
            if hasattr(parent, 'parent_window') and parent.parent_window:
                return parent.parent_window
            if hasattr(parent, 'status_bar'):
                return parent
            parent = parent.parent()
        return None
    
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 按下Escape键清除所有选择
        if event.key() == Qt.Key_Escape:
            if self.trash_selected_cards:
                self.clear_trash_selections()
                # 更新状态栏
                parent_window = self.get_parent_window()
                if parent_window and hasattr(parent_window, 'status_bar'):
                    parent_window.status_bar.set_status("已取消所有选择")
        # 按下Delete键永久删除选中的提示词
        elif event.key() == Qt.Key_Delete:
            if self.trash_selected_cards:
                self.permanently_delete_selected_prompts()
        # 按下Ctrl+A全选
        elif event.key() == Qt.Key_A and event.modifiers() & Qt.ControlModifier:
            self.select_all_trash_cards()
            # 更新状态栏
            parent_window = self.get_parent_window()
            if parent_window and hasattr(parent_window, 'status_bar'):
                parent_window.status_bar.set_status("已全选所有提示词")
        else:
            super().keyPressEvent(event) 