#!/usr/bin/env python3
"""
AI平台配置对话框
提供AI平台选择、配置和管理功能 - 现代化设计
"""

from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QLabel,
    QStackedWidget, QScrollArea, QWidget, QGridLayout, QComboBox,
    QSpinBox, QMessageBox, QDialog, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from views.base_components import BaseDialog
from .platform_selection_widget import PlatformCard
from config.ai_platforms import PLATFORM_CATEGORIES, PLATFORM_UI_CONFIG
from services.ai_config_manager import ai_config_manager
from create_prompt_dialog import position_dialog_right

# 导入设计系统
from config.design_system import (
    COLORS, SPACING, RADIUS, SHADOWS, TYPOGRAPHY, 
    BUTTON_STYLES, INPUT_STYLES, COMBOBOX_STYLES, PLATFORM_COLORS
)


class PlatformInfoWidget(QWidget):
    """一个专门用于显示平台信息的独立、简约组件"""
    def __init__(self, platform_id, platform_config, parent=None):
        super().__init__(parent)
        self.platform_id = platform_id
        self.platform_config = platform_config
        self._init_ui()

    def _init_ui(self):
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)

        # Icon
        icon_label = self._create_icon()
        main_layout.addWidget(icon_label)

        # Text content
        text_layout = QVBoxLayout()
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(4)

        name_label = QLabel(self.platform_config.get('display_name', '未知平台'))
        name_label.setStyleSheet(f"""
            color: {COLORS['gray_800']};
            font-size: {TYPOGRAPHY['sizes']['lg']};
            font-weight: {TYPOGRAPHY['weights']['semibold']};
            background-color: transparent;
        """)
        
        desc_label = QLabel(self.platform_config.get('description', ''))
        desc_label.setStyleSheet(f"""
            color: {COLORS['gray_500']};
            font-size: {TYPOGRAPHY['sizes']['sm']};
            background-color: transparent;
        """)

        text_layout.addWidget(name_label)
        text_layout.addWidget(desc_label)
        
        main_layout.addLayout(text_layout)
        main_layout.addStretch()
        
    def _create_icon(self):
        from PySide6.QtGui import QPainter, QPixmap, QColor, QBrush, QFont
        
        icon_label = QLabel()
        icon_label.setFixedSize(40, 40)
        
        pixmap = QPixmap(40, 40)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        platform_color = PLATFORM_COLORS.get(self.platform_id, COLORS['gray_400'])
        painter.setBrush(QBrush(QColor(platform_color)))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, 40, 40)
        
        painter.setPen(QColor(COLORS['white']))
        font = QFont("Arial", 14, QFont.Bold)
        painter.setFont(font)
        
        icon_text = self.platform_config.get('display_name', 'AI')[:1].upper()
        painter.drawText(0, 0, 40, 40, Qt.AlignCenter, icon_text)
        
        painter.end()
        
        icon_label.setPixmap(pixmap)
        return icon_label


class AIPlatformDialog(BaseDialog):
    """AI平台配置对话框 - 现代化设计"""
    
    platform_configured = Signal(str)  # 平台配置完成信号
    platform_removed = Signal(str)     # 平台移除信号
    
    def __init__(self, parent=None, management_mode=False):
        super().__init__(parent, "AI平台配置", 720, 750)
        self.management_mode = management_mode
        self.current_step = 'selection'  # selection/configuration
        self.selected_platform = None
        self.current_category = '热门推荐'
        self.search_text = ''
        
        # UI组件引用
        self.api_key_input = None
        self.model_combo = None
        self.test_btn = None
        self.status_label = None
        
        self.setup_dialog_ui()
    
    def showEvent(self, event):
        """显示事件，确保对话框显示在正确位置"""
        super().showEvent(event)
        # 显示在主窗口右侧
        position_dialog_right(self, self.parent())
    
    def setup_dialog_ui(self):
        """设置对话框UI"""
        # 使用BaseDialog提供的content_layout
        content_layout = self.content_layout
        content_layout.setContentsMargins(0, 0, 0, 0)
        content_layout.setSpacing(0)
        
        # 步骤指示器
        self.create_step_indicator(content_layout)
        
        # 内容堆栈
        self.content_stack = QStackedWidget()
        content_layout.addWidget(self.content_stack)
        
        # 创建各个页面
        self.create_platform_selection_page()
        self.create_configuration_page()
        
        # 底部按钮
        self.create_dialog_buttons(content_layout)
    
    def create_step_indicator(self, layout):
        """创建现代化步骤指示器"""
        step_frame = QFrame()
        step_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['gray_50']};
                border-bottom: 1px solid {COLORS['gray_200']};
                padding: {SPACING['md']};
            }}
        """)
        
        step_layout = QHBoxLayout(step_frame)
        step_layout.setContentsMargins(24, 12, 24, 12)
        
        # 步骤1
        self.step1_label = QLabel("1. 选择平台")
        self.step1_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['primary']};
                font-weight: {TYPOGRAPHY['weights']['semibold']};
                font-size: {TYPOGRAPHY['sizes']['base']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        
        # 分隔符
        separator = QLabel("→")
        separator.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_300']};
                font-size: {TYPOGRAPHY['sizes']['lg']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        
        # 步骤2
        self.step2_label = QLabel("2. 配置参数")
        self.step2_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_400']};
                font-weight: {TYPOGRAPHY['weights']['medium']};
                font-size: {TYPOGRAPHY['sizes']['base']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        
        step_layout.addWidget(self.step1_label)
        step_layout.addWidget(separator)
        step_layout.addWidget(self.step2_label)
        step_layout.addStretch()
        
        layout.addWidget(step_frame)
    
    def create_platform_selection_page(self):
        """创建平台选择页面"""
        selection_widget = QWidget()
        layout = QVBoxLayout(selection_widget)
        layout.setContentsMargins(24, 24, 24, 16)
        layout.setSpacing(20)
        
        # 搜索框
        search_layout = QHBoxLayout()
        self.platform_search = QLineEdit()
        self.platform_search.setPlaceholderText("搜索AI平台...")
        self.platform_search.setStyleSheet(INPUT_STYLES['search'])
        self.platform_search.textChanged.connect(self.filter_platforms)
        search_layout.addWidget(self.platform_search)
        layout.addLayout(search_layout)
        
        # 分类标签栏
        self.create_category_tabs(layout)
        
        # 平台网格区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background: {COLORS['gray_100']};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background: {COLORS['gray_300']};
                border-radius: 4px;
                min-height: 20px;
            }}
            QScrollBar::handle:vertical:hover {{
                background: {COLORS['gray_400']};
            }}
        """)
        
        self.platforms_grid_widget = QWidget()
        self.platforms_grid_layout = QGridLayout(self.platforms_grid_widget)
        self.platforms_grid_layout.setSpacing(20)
        scroll_area.setWidget(self.platforms_grid_widget)
        
        layout.addWidget(scroll_area)
        
        # 加载平台卡片
        self.load_platform_cards()
        
        self.content_stack.addWidget(selection_widget)
    
    def create_category_tabs(self, layout):
        """创建现代化分类标签栏"""
        tabs_layout = QHBoxLayout()
        tabs_layout.setSpacing(12)
        
        self.category_buttons = {}
        
        for category_name in PLATFORM_CATEGORIES.keys():
            btn = QPushButton(category_name)
            btn.setCheckable(True)
            btn.setChecked(category_name == self.current_category)
            
            # 根据状态设置样式
            if btn.isChecked():
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {COLORS['primary']};
                        color: {COLORS['white']};
                        border: none;
                        border-radius: {RADIUS['lg']};
                        padding: {SPACING['sm']} {SPACING['lg']};
                        font-size: {TYPOGRAPHY['sizes']['sm']};
                        font-weight: {TYPOGRAPHY['weights']['medium']};
                        font-family: {TYPOGRAPHY['font_family']};
                        min-height: 36px;
                    }}
                    QPushButton:hover {{
                        background-color: {COLORS['primary_hover']};
                    }}
                """)
            else:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {COLORS['gray_100']};
                        color: {COLORS['gray_600']};
                        border: 1px solid {COLORS['gray_200']};
                        border-radius: {RADIUS['lg']};
                        padding: {SPACING['sm']} {SPACING['lg']};
                        font-size: {TYPOGRAPHY['sizes']['sm']};
                        font-weight: {TYPOGRAPHY['weights']['medium']};
                        font-family: {TYPOGRAPHY['font_family']};
                        min-height: 36px;
                    }}
                    QPushButton:hover {{
                        background-color: {COLORS['gray_200']};
                        border-color: {COLORS['gray_300']};
                    }}
                """)
            
            btn.clicked.connect(lambda checked, cat=category_name: self.switch_category(cat))
            
            self.category_buttons[category_name] = btn
            tabs_layout.addWidget(btn)
        
        tabs_layout.addStretch()
        layout.addLayout(tabs_layout)
    
    def create_configuration_page(self):
        """创建配置页面"""
        config_widget = QWidget()
        layout = QVBoxLayout(config_widget)
        layout.setContentsMargins(24, 16, 24, 16)
        layout.setSpacing(16)
        
        # 平台信息展示
        self.platform_info_widget = QWidget()
        self.platform_info_layout = QVBoxLayout(self.platform_info_widget)
        self.platform_info_layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.platform_info_widget)
        
        # 配置表单区域
        form_scroll = QScrollArea()
        form_scroll.setWidgetResizable(True)
        form_scroll.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background: {COLORS['gray_100']};
                width: 8px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical {{
                background: {COLORS['gray_300']};
                border-radius: 4px;
            }}
        """)
        
        self.config_form_widget = QWidget()
        self.config_form_layout = QVBoxLayout(self.config_form_widget)
        self.config_form_layout.setSpacing(8)
        form_scroll.setWidget(self.config_form_widget)
        
        form_scroll.setMinimumHeight(350)
        layout.addWidget(form_scroll)
        
        self.content_stack.addWidget(config_widget)
    
    def create_dialog_buttons(self, layout):
        """创建现代化底部按钮"""
        buttons_frame = QFrame()
        buttons_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['gray_50']};
                border-top: 1px solid {COLORS['gray_200']};
            }}
        """)
        
        buttons_layout = QHBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(24, 16, 24, 16)
        buttons_layout.setSpacing(12)
        
        # 返回按钮
        self.back_btn = QPushButton("← 返回")
        self.back_btn.setStyleSheet(BUTTON_STYLES['secondary'])
        self.back_btn.clicked.connect(self.go_back)
        self.back_btn.setVisible(False)
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet(BUTTON_STYLES['secondary'])
        cancel_btn.clicked.connect(self.reject)
        
        # 确认按钮
        self.confirm_btn = QPushButton("确认")
        self.confirm_btn.setStyleSheet(BUTTON_STYLES['primary'])
        self.confirm_btn.clicked.connect(self.confirm_action)
        self.confirm_btn.setEnabled(False)
        
        buttons_layout.addWidget(self.back_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addWidget(self.confirm_btn)
        
        layout.addWidget(buttons_frame)
    
    def load_platform_cards(self):
        """加载平台卡片"""
        # 清空现有卡片
        self.clear_grid_layout()
        
        configured_platforms = set(ai_config_manager.get_configured_platform_ids())
        
        row, col = 0, 0
        max_cols = 3
        
        for category_name, category_info in PLATFORM_CATEGORIES.items():
            if not self.should_show_category(category_name):
                continue
                
            for platform_id in category_info['platforms']:
                if not self.matches_search_filter(platform_id):
                    continue
                    
                platform_config = PLATFORM_UI_CONFIG.get(platform_id, {})
                configured = platform_id in configured_platforms
                
                card = PlatformCard(platform_id, platform_config, configured)
                card.platform_selected.connect(self.on_platform_selected)
                
                self.platforms_grid_layout.addWidget(card, row, col)
                col += 1
                if col >= max_cols:
                    col = 0
                    row += 1
    
    def clear_grid_layout(self):
        """清空网格布局"""
        while self.platforms_grid_layout.count():
            child = self.platforms_grid_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def should_show_category(self, category_name):
        """判断是否应该显示该分类"""
        return category_name == self.current_category
    
    def matches_search_filter(self, platform_id):
        """判断平台是否匹配搜索条件"""
        if not self.search_text:
            return True
        
        platform_config = PLATFORM_UI_CONFIG.get(platform_id, {})
        search_text = self.search_text.lower()
        
        # 搜索平台名称和描述
        display_name = platform_config.get('display_name', '').lower()
        description = platform_config.get('description', '').lower()
        
        return search_text in display_name or search_text in description
    
    def switch_category(self, category_name):
        """切换分类"""
        # 更新按钮状态
        for name, btn in self.category_buttons.items():
            btn.setChecked(name == category_name)
            
            # 更新按钮样式
            if btn.isChecked():
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {COLORS['primary']};
                        color: {COLORS['white']};
                        border: none;
                        border-radius: {RADIUS['lg']};
                        padding: {SPACING['sm']} {SPACING['lg']};
                        font-size: {TYPOGRAPHY['sizes']['sm']};
                        font-weight: {TYPOGRAPHY['weights']['medium']};
                        font-family: {TYPOGRAPHY['font_family']};
                        min-height: 36px;
                    }}
                    QPushButton:hover {{
                        background-color: {COLORS['primary_hover']};
                    }}
                """)
            else:
                btn.setStyleSheet(f"""
                    QPushButton {{
                        background-color: {COLORS['gray_100']};
                        color: {COLORS['gray_600']};
                        border: 1px solid {COLORS['gray_200']};
                        border-radius: {RADIUS['lg']};
                        padding: {SPACING['sm']} {SPACING['lg']};
                        font-size: {TYPOGRAPHY['sizes']['sm']};
                        font-weight: {TYPOGRAPHY['weights']['medium']};
                        font-family: {TYPOGRAPHY['font_family']};
                        min-height: 36px;
                    }}
                    QPushButton:hover {{
                        background-color: {COLORS['gray_200']};
                        border-color: {COLORS['gray_300']};
                    }}
                """)
        
        self.current_category = category_name
        self.load_platform_cards()
    
    def filter_platforms(self, text):
        """过滤平台"""
        self.search_text = text.strip()
        self.load_platform_cards()
    
    def on_platform_selected(self, platform_id: str):
        """处理平台选择"""
        self.selected_platform = platform_id
        self.load_configuration_page(platform_id)
        self.content_stack.setCurrentIndex(1)  # 切换到配置页面
        self.current_step = 'configuration'
        self.update_step_indicator()
        self.update_navigation_buttons()
    
    def load_configuration_page(self, platform_id: str):
        """加载配置页面"""
        # 清空现有表单
        self.clear_form_layout()
        
        # 显示平台信息
        self.show_platform_info(platform_id)
        
        # 创建配置表单
        self.create_platform_config_form(platform_id)
    
    def clear_form_layout(self):
        """清空表单布局"""
        while self.config_form_layout.count():
            child = self.config_form_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
        
        while self.platform_info_layout.count():
            child = self.platform_info_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def show_platform_info(self, platform_id: str):
        """使用独立的PlatformInfoWidget显示平台信息"""
        # 1. 清空旧内容
        while self.platform_info_layout.count():
            child = self.platform_info_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

        # 2. 获取配置
        platform_config = PLATFORM_UI_CONFIG.get(platform_id, {})

        # 3. 创建并添加新组件
        info_widget = PlatformInfoWidget(platform_id, platform_config)
        self.platform_info_layout.addWidget(info_widget)
    
    def create_platform_config_form(self, platform_id: str):
        """创建统一的、更紧凑的平台配置表单"""
        # 创建统一的表单容器
        form_container_frame = QFrame()
        form_container_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {COLORS['white']};
                border: 1px solid {COLORS['gray_200']};
                border-radius: {RADIUS['lg']};
                padding: {SPACING['lg']};
            }}
        """)
        
        form_container_layout = QVBoxLayout(form_container_frame)
        form_container_layout.setSpacing(12) # 容器内各部分间距
        
        # API密钥输入
        self.create_api_key_input(form_container_layout)
        
        # 分隔线
        form_container_layout.addWidget(self._create_form_separator())
        
        # 模型选择
        from config.ai_config import AI_PLATFORMS
        platform_config = AI_PLATFORMS.get(platform_id, {})
        if 'models' in platform_config:
            self.create_model_selection(form_container_layout, platform_config['models'])
            
            # 分隔线
            form_container_layout.addWidget(self._create_form_separator())

        # 连接测试
        self.create_connection_test_section(form_container_layout)
        
        self.config_form_layout.addWidget(form_container_frame)
        
        # 加载现有配置
        self.load_existing_config(platform_id)

    def _create_form_separator(self):
        """创建表单内的分隔线"""
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setFixedHeight(1)
        separator.setStyleSheet(f"background-color: {COLORS['gray_200']}; border: none;")
        return separator
        
    def create_api_key_input(self, parent_layout):
        """创建API密钥输入控件（无独立卡片）"""
        key_section_layout = QVBoxLayout()
        key_section_layout.setSpacing(8) # 内部间距减小
        
        # 标题
        key_title = QLabel("API密钥 *")
        key_title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_800']};
                font-size: {TYPOGRAPHY['sizes']['base']};
                font-weight: {TYPOGRAPHY['weights']['semibold']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        key_section_layout.addWidget(key_title)
        
        # 输入框和按钮
        input_layout = QHBoxLayout()
        input_layout.setSpacing(12)
        
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("请输入API密钥")
        self.api_key_input.setEchoMode(QLineEdit.Password)
        self.api_key_input.setStyleSheet(INPUT_STYLES['default_compact'])
        self.api_key_input.textChanged.connect(self.validate_form)
        
        # 显示/隐藏密钥按钮
        show_key_btn = QPushButton("👁")
        show_key_btn.setStyleSheet(BUTTON_STYLES['small'])
        show_key_btn.clicked.connect(self.toggle_api_key_visibility)
        
        input_layout.addWidget(self.api_key_input)
        input_layout.addWidget(show_key_btn)
        key_section_layout.addLayout(input_layout)
        
        parent_layout.addLayout(key_section_layout)
    
    def create_model_selection(self, parent_layout, models_config):
        """创建模型选择控件（无独立卡片）"""
        model_section_layout = QVBoxLayout()
        model_section_layout.setSpacing(8) # 内部间距减小

        # 标题
        model_title = QLabel("默认模型")
        model_title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_800']};
                font-size: {TYPOGRAPHY['sizes']['base']};
                font-weight: {TYPOGRAPHY['weights']['semibold']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        model_section_layout.addWidget(model_title)
        
        # 模型选择下拉框
        self.model_combo = QComboBox()
        self.model_combo.setStyleSheet(COMBOBOX_STYLES['default_compact'])
        
        for model_id, model_info in models_config.items():
            self.model_combo.addItem(model_id, model_id)
        
        model_section_layout.addWidget(self.model_combo)
        parent_layout.addLayout(model_section_layout)

    def create_connection_test_section(self, parent_layout):
        """创建连接测试区域（无独立卡片）"""
        test_section_layout = QVBoxLayout()
        test_section_layout.setSpacing(8) # 内部间距减小
        
        # 标题
        test_title = QLabel("连接测试")
        test_title.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_800']};
                font-size: {TYPOGRAPHY['sizes']['base']};
                font-weight: {TYPOGRAPHY['weights']['semibold']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        test_section_layout.addWidget(test_title)
        
        # 测试按钮和状态
        test_button_layout = QHBoxLayout()
        test_button_layout.setSpacing(8) # 按钮和状态标签间距减小
        
        self.test_btn = QPushButton("测试连接")
        self.test_btn.setStyleSheet(BUTTON_STYLES['primary_compact'])
        self.test_btn.clicked.connect(self.test_connection)
        
        self.status_label = QLabel("未测试")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_500']};
                font-size: {TYPOGRAPHY['sizes']['sm']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        
        test_button_layout.addWidget(self.test_btn)
        test_button_layout.addWidget(self.status_label)
        test_button_layout.addStretch()
        
        test_section_layout.addLayout(test_button_layout)
        parent_layout.addLayout(test_section_layout)
    
    def load_existing_config(self, platform_id: str):
        """加载现有配置"""
        config = ai_config_manager.get_platform_config(platform_id)
        if config:
            if self.api_key_input:
                self.api_key_input.setText(config.get('api_key', ''))
            if self.model_combo and config.get('default_model'):
                index = self.model_combo.findData(config['default_model'])
                if index >= 0:
                    self.model_combo.setCurrentIndex(index)
    
    def toggle_api_key_visibility(self):
        """切换API密钥可见性"""
        if self.api_key_input:
            if self.api_key_input.echoMode() == QLineEdit.Password:
                self.api_key_input.setEchoMode(QLineEdit.Normal)
            else:
                self.api_key_input.setEchoMode(QLineEdit.Password)
    
    def validate_form(self):
        """验证表单"""
        valid = bool(self.api_key_input and self.api_key_input.text().strip())
        self.confirm_btn.setEnabled(valid)
        if self.test_btn:
            self.test_btn.setEnabled(valid)
    
    def test_connection(self):
        """测试连接"""
        if not self.api_key_input or not self.api_key_input.text().strip():
            return
        
        self.test_btn.setEnabled(False)
        self.status_label.setText("测试中...")
        self.status_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['warning']};
                font-size: {TYPOGRAPHY['sizes']['sm']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        
        # 简化的测试逻辑（实际应该调用API）
        try:
            # 这里应该调用真实的API测试
            import time
            import threading
            
            def test_api():
                time.sleep(1)  # 模拟网络请求
                # 模拟测试结果
                success = len(self.api_key_input.text()) > 10  # 简单的验证
                
                # 在主线程更新UI
                from PySide6.QtCore import QTimer
                QTimer.singleShot(0, lambda: self.on_test_complete(success))
            
            threading.Thread(target=test_api, daemon=True).start()
            
        except Exception as e:
            self.on_test_complete(False, str(e))
    
    def on_test_complete(self, success, error_msg=None):
        """测试完成回调"""
        self.test_btn.setEnabled(True)
        
        if success:
            self.status_label.setText("✅ 连接成功")
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['success']};
                    font-size: {TYPOGRAPHY['sizes']['sm']};
                    font-family: {TYPOGRAPHY['font_family']};
                    background: transparent;
                    border: none;
                }}
            """)
            if self.selected_platform:
                ai_config_manager.update_platform_status(self.selected_platform, 'connected')
        else:
            self.status_label.setText(f"❌ 连接失败: {error_msg or '未知错误'}")
            self.status_label.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['error']};
                    font-size: {TYPOGRAPHY['sizes']['sm']};
                    font-family: {TYPOGRAPHY['font_family']};
                    background: transparent;
                    border: none;
                }}
            """)
            if self.selected_platform:
                ai_config_manager.update_platform_status(self.selected_platform, 'error', error_msg)
    
    def update_step_indicator(self):
        """更新步骤指示器"""
        if self.current_step == 'selection':
            self.step1_label.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['primary']};
                    font-weight: {TYPOGRAPHY['weights']['semibold']};
                    font-size: {TYPOGRAPHY['sizes']['base']};
                    font-family: {TYPOGRAPHY['font_family']};
                    background: transparent;
                    border: none;
                }}
            """)
            self.step2_label.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['gray_400']};
                    font-weight: {TYPOGRAPHY['weights']['medium']};
                    font-size: {TYPOGRAPHY['sizes']['base']};
                    font-family: {TYPOGRAPHY['font_family']};
                    background: transparent;
                    border: none;
                }}
            """)
        else:
            self.step1_label.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['success']};
                    font-weight: {TYPOGRAPHY['weights']['semibold']};
                    font-size: {TYPOGRAPHY['sizes']['base']};
                    font-family: {TYPOGRAPHY['font_family']};
                    background: transparent;
                    border: none;
                }}
            """)
            self.step2_label.setStyleSheet(f"""
                QLabel {{
                    color: {COLORS['primary']};
                    font-weight: {TYPOGRAPHY['weights']['semibold']};
                    font-size: {TYPOGRAPHY['sizes']['base']};
                    font-family: {TYPOGRAPHY['font_family']};
                    background: transparent;
                    border: none;
                }}
            """)
    
    def update_navigation_buttons(self):
        """更新导航按钮"""
        if self.current_step == 'configuration':
            self.back_btn.setVisible(True)
            self.confirm_btn.setText("保存配置")
        else:
            self.back_btn.setVisible(False)
            self.confirm_btn.setText("确认")
    
    def go_back(self):
        """返回上一步"""
        if self.current_step == 'configuration':
            self.content_stack.setCurrentIndex(0)
            self.current_step = 'selection'
            self.selected_platform = None
            self.update_step_indicator()
            self.update_navigation_buttons()
            self.confirm_btn.setEnabled(False)
    
    def confirm_action(self):
        """确认操作"""
        if self.current_step == 'configuration':
            if self.save_platform_configuration():
                self.platform_configured.emit(self.selected_platform)
                self.accept()
        else:
            # 在选择步骤直接确认（当前不应该启用）
            pass
    
    def save_platform_configuration(self):
        """保存平台配置"""
        if not self.selected_platform or not self.api_key_input:
            return False
        
        api_key = self.api_key_input.text().strip()
        if not api_key:
            QMessageBox.warning(self, "配置错误", "请输入API密钥")
            return False
        
        config = {
            'api_key': api_key,
            'default_model': self.model_combo.currentData() if self.model_combo else None,
            'custom_params': {}
        }
        
        # 保存配置
        ai_config_manager.add_platform_config(self.selected_platform, config)
        
        return True 