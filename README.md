# Prompt Assistant 提示词收藏助手

一个用于管理和收藏AI提示词的桌面应用程序。

## 功能特性

- 提示词卡片式管理
- 标签和分类筛选
- 多媒体支持（图片、视频）
- 收藏与置顶功能
- 提示词历史版本管理

## 新增功能：视频缩略图

现在应用程序支持视频缩略图功能，可以在提示词卡片和创建/编辑提示词页面中显示视频文件的第一帧作为预览缩略图。

功能亮点：
- 在提示词卡片中显示视频的第一帧作为缩略图
- 在创建/编辑提示词页面的媒体预览区域显示视频缩略图
- 点击视频缩略图可使用系统默认播放器打开视频

## 安装和运行

### 依赖项

```
pip install -r requirements.txt
```

主要依赖：
- PySide6：用于UI界面
- OpenCV：用于视频缩略图提取（可选但推荐）

### 视频缩略图功能依赖

视频缩略图功能依赖于OpenCV库，如果未安装，程序仍会正常工作，但只会显示默认的视频播放图标而不会显示视频帧缩略图。

安装OpenCV：
```
pip install opencv-python
```

### 运行程序

```
python main.py
```

## 使用方法

1. 启动程序
2. 点击"+"按钮添加新的提示词
3. 填写提示词信息，可以添加图片或视频
4. 使用标签筛选或分类筛选查找提示词
5. 点击提示词卡片中的视频缩略图可使用系统默认播放器打开视频

## 备注

如果视频缩略图功能不工作（仅显示默认播放图标），请确认：
1. 已安装OpenCV库（`pip install opencv-python`）
2. 视频文件格式受支持（支持mp4, avi, mov, wmv, flv, webm等常见格式）
3. 视频文件未损坏且可正常打开