#!/usr/bin/env python3
"""
样式配置文件
存储所有UI组件的样式信息，减少代码中的样式硬编码
"""

# 颜色定义
COLORS = {
    # 基础颜色
    "primary": "#0EA5E9",      # 主要色（天蓝色）
    "primary_dark": "#0284C7",  # 深主色
    "primary_light": "#E0F2FE", # 浅主色
    "primary_bg": "#F0F9FF",    # 主色背景
    "primary_text": "#0C4A6E",  # 主色文字
    "primary_border": "#BAE6FD", # 主色边框
    
    # 强调色
    "accent": "#8B5CF6",       # 强调色（紫色）
    "accent_dark": "#6D28D9",   # 深强调色
    "accent_light": "#EDE9FE",  # 浅强调色
    
    # 成功、警告、错误
    "success": "#10B981",      # 成功（绿色）
    "warning": "#F59E0B",      # 警告（橙色）
    "error": "#EF4444",        # 错误（红色）
    "info": "#6366F1",         # 信息（靛蓝色）
    
    # 灰度
    "gray_50": "#F9FAFB",
    "gray_100": "#F3F4F6",
    "gray_200": "#E5E7EB",
    "gray_300": "#D1D5DB",
    "gray_400": "#9CA3AF",
    "gray_500": "#6B7280",
    "gray_600": "#4B5563",
    "gray_700": "#374151",
    "gray_800": "#1F2937",
    "gray_900": "#111827",
    
    # 标准颜色
    "white": "#FFFFFF",
    "black": "#000000",
    "transparent": "transparent",
    
    # 特殊用途
    "favorite": "#FFD700",     # 收藏（金色）
    "favorite_border": "#EAB308", # 收藏边框
}

# 按钮样式
BUTTON_STYLES = {
    # 筛选按钮默认样式
    "filter_button": f"""
        QPushButton {{
            background-color: {COLORS["primary_bg"]};
            color: {COLORS["primary_text"]};
            border: 1px solid {COLORS["primary_border"]};
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }}
        QPushButton:hover {{
            background-color: {COLORS["primary_light"]};
            border-color: {COLORS["primary"]};
        }}
        QToolTip {{
            background-color: {COLORS["gray_700"]};
            color: {COLORS["white"]};
            border: 1px solid {COLORS["gray_600"]};
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }}
    """,
    
    # 筛选按钮激活样式
    "filter_button_active": f"""
        QPushButton {{
            background-color: {COLORS["primary"]};
            color: {COLORS["white"]};
            border: none;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }}
        QPushButton:hover {{
            background-color: {COLORS["primary_dark"]};
        }}
        QToolTip {{
            background-color: {COLORS["gray_700"]};
            color: {COLORS["white"]};
            border: 1px solid {COLORS["gray_600"]};
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }}
    """,
    
    # 主按钮样式
    "primary_button": f"""
        QPushButton {{
            background-color: {COLORS["primary"]};
            color: {COLORS["white"]};
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
            font-weight: 500;
        }}
        QPushButton:hover {{
            background-color: {COLORS["primary_dark"]};
        }}
        QPushButton:disabled {{
            background-color: {COLORS["gray_300"]};
            color: {COLORS["gray_500"]};
        }}
    """,
    
    # 次要按钮样式
    "secondary_button": f"""
        QPushButton {{
            background-color: {COLORS["white"]};
            color: {COLORS["gray_700"]};
            border: 1px solid {COLORS["gray_300"]};
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 14px;
        }}
        QPushButton:hover {{
            background-color: {COLORS["gray_50"]};
            border-color: {COLORS["gray_400"]};
        }}
        QPushButton:disabled {{
            background-color: {COLORS["gray_100"]};
            color: {COLORS["gray_400"]};
            border-color: {COLORS["gray_200"]};
        }}
    """,
    
    # 图标按钮样式
    "icon_button": f"""
        QPushButton {{
            background-color: {COLORS["transparent"]};
            border: none;
            border-radius: 4px;
            padding: 4px;
        }}
        QPushButton:hover {{
            background-color: {COLORS["gray_100"]};
        }}
    """,
    
    # 添加按钮样式
    "add_button": f"""
        QPushButton {{
            background-color: {COLORS["primary"]};
            color: {COLORS["white"]};
            border: none;
            border-radius: 20px;
            font-size: 24px;
            font-weight: bold;
        }}
        QPushButton:hover {{
            background-color: {COLORS["primary_dark"]};
        }}
    """
}

# 卡片样式
CARD_STYLES = {
    # 提示词卡片样式
    "prompt_card": f"""
        QFrame {{
            background-color: {COLORS["white"]};
            border: 1px solid {COLORS["gray_200"]};
            border-radius: 8px;
        }}
        QFrame:hover {{
            border-color: {COLORS["primary"]};
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }}
    """,
    
    # 选中的卡片样式
    "prompt_card_selected": f"""
        QFrame {{
            background-color: {COLORS["primary_light"]};
            border: 2px solid {COLORS["primary"]};
            border-radius: 8px;
        }}
    """
}

# 输入框样式
INPUT_STYLES = {
    # 标准输入框样式
    "input": f"""
        QLineEdit, QTextEdit, QComboBox {{
            background-color: {COLORS["white"]};
            border: 1px solid {COLORS["gray_300"]};
            border-radius: 4px;
            padding: 8px;
            font-size: 14px;
            selection-background-color: {COLORS["primary_light"]};
        }}
        QLineEdit:focus, QTextEdit:focus, QComboBox:focus {{
            border-color: {COLORS["primary"]};
        }}
        QComboBox::drop-down {{
            width: 20px;
            border: none;
            background: {COLORS["transparent"]};
        }}
    """,
    
    # 搜索框样式
    "search_input": f"""
        QLineEdit {{
            background-color: {COLORS["white"]};
            border: 1px solid {COLORS["gray_300"]};
            border-radius: 16px;
            padding: 8px 36px 8px 36px; /* 左右都有图标 */
            font-size: 14px;
        }}
        QLineEdit:focus {{
            border-color: {COLORS["primary"]};
        }}
    """
}

# 标签样式
TAG_STYLES = {
    # 标准标签样式
    "tag": f"""
        QLabel {{
            background-color: {COLORS["primary_light"]};
            color: {COLORS["primary_dark"]};
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 11px;
        }}
    """,
    
    # 可点击标签样式
    "clickable_tag": f"""
        QLabel {{
            background-color: {COLORS["primary_light"]};
            color: {COLORS["primary_dark"]};
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 11px;
        }}
        QLabel:hover {{
            background-color: {COLORS["primary"]};
            color: {COLORS["white"]};
        }}
    """
}

# 对话框样式
DIALOG_STYLES = {
    # 标准对话框样式
    "dialog": f"""
        QDialog {{
            background-color: {COLORS["white"]};
            border: 1px solid {COLORS["gray_300"]};
            border-radius: 8px;
        }}
    """,
    
    # 标题栏样式
    "title_bar": f"""
        QWidget {{
            background-color: {COLORS["white"]};
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            border-bottom: 1px solid {COLORS["gray_200"]};
        }}
    """
}

# 容器样式
CONTAINER_STYLES = {
    # 标准容器样式
    "container": f"""
        QWidget {{
            background-color: {COLORS["white"]};
        }}
    """,
    
    # 分组框样式
    "group_box": f"""
        QGroupBox {{
            font-size: 14px;
            font-weight: 600;
            color: {COLORS["gray_700"]};
            border: 1px solid {COLORS["gray_200"]};
            border-radius: 8px;
            margin-top: 12px;
            padding-top: 10px;
        }}
        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 8px 0 8px;
            background-color: {COLORS["white"]};
        }}
    """
}

# 状态栏样式
STATUS_BAR_STYLE = f"""
    QWidget {{
        background-color: {COLORS["white"]};
        border-top: 1px solid {COLORS["gray_200"]};
    }}
    QLabel {{
        color: {COLORS["gray_600"]};
        font-size: 12px;
        padding: 2px 10px;
    }}
"""

# 导航栏样式
NAV_BAR_STYLE = f"""
    QWidget {{
        background-color: {COLORS["primary"]};
    }}
    QPushButton {{
        background-color: transparent;
        border: none;
        border-radius: 4px;
        color: {COLORS["white"]};
        padding: 8px;
        margin: 4px;
    }}
    QPushButton:hover {{
        background-color: rgba(255, 255, 255, 0.1);
    }}
    QPushButton:checked {{
        background-color: rgba(255, 255, 255, 0.2);
    }}
""" 