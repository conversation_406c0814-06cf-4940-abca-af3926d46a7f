# AI配置功能使用说明

## 功能概述

AI配置功能已成功集成到设置页面，为您的提示词收藏助手添加了强大的AI能力。您现在可以：

- 🔑 管理多平台API密钥（OpenAI、Claude、百度文心一言等）
- ⚙️ 配置AI参数（Temperature、Max Tokens等）
- 🔍 测试API连接状态
- 💾 保存和恢复配置

## 使用方法

### 1. 打开AI配置页面
1. 启动主程序：`python main.py`
2. 点击左下角的"设置"按钮
3. 在设置页面中找到"AI配置"部分

### 2. 配置API密钥
1. 在"API密钥配置"区域找到对应平台（如OpenAI）
2. 输入您的API密钥
3. 点击👁按钮可以显示/隐藏密钥
4. 点击"测试"按钮验证连接

### 3. 调整AI参数
- **Temperature**: 控制AI回复的创造性（0.0-1.0）
- **Max Tokens**: 设置最大生成token数量（100-4096）

### 4. 保存配置
点击"保存AI配置"按钮保存您的设置

## 支持的AI平台

### 当前支持
- ✅ **OpenAI** (GPT-3.5-turbo, GPT-4)

### 即将支持
- 🚧 **Anthropic Claude** (Claude-3系列)
- 🚧 **百度文心一言** (ERNIE-Bot系列)
- 🚧 **阿里通义千问** (Qwen系列)

## 状态指示

- **未配置**: 灰色文字，表示还未输入API密钥
- **已配置**: 绿色文字，表示API密钥已保存
- **✅ 连接成功**: 绿色文字，表示API连接测试成功
- **❌ 连接失败**: 红色文字，表示API连接测试失败

## 获取API密钥

### OpenAI
1. 访问 [OpenAI API Keys](https://platform.openai.com/api-keys)
2. 登录您的OpenAI账户
3. 点击"Create new secret key"
4. 复制生成的API密钥

### 其他平台
- **Claude**: [Anthropic Console](https://console.anthropic.com/)
- **文心一言**: [百度智能云](https://cloud.baidu.com/)
- **通义千问**: [阿里云平台](https://dashscope.aliyuncs.com/)

## 安全提示

- ⚠️ API密钥是敏感信息，请妥善保管
- 🔒 本软件将API密钥加密存储在本地
- 🚫 请勿将API密钥分享给他人
- 💡 建议定期更换API密钥

## 故障排除

### AI服务不可用
如果看到"⚠️ AI服务不可用"提示：
```bash
pip install aiohttp requests openai
```

### 连接测试失败
1. 检查API密钥是否正确
2. 确认网络连接正常
3. 验证API密钥是否有足够余额
4. 检查API密钥权限设置

### 配置丢失
配置文件存储在：
- Windows: `%APPDATA%/PromptAssistant/`
- macOS: `~/Library/Preferences/PromptAssistant/`
- Linux: `~/.config/PromptAssistant/`

## 下一步开发

1. **提示词测试功能**: 在提示词卡片添加AI测试按钮
2. **结果管理**: 保存和对比AI生成结果
3. **费用统计**: 跟踪API使用量和费用
4. **更多平台**: 支持更多AI平台和模型

## 技术架构

### 文件结构
```
config/
  └── ai_config.py          # AI平台配置
services/
  ├── ai_service.py         # AI服务主接口
  └── api/
      ├── base_adapter.py   # 抽象基类
      └── openai_adapter.py # OpenAI适配器
enhanced_settings_help.py   # 设置页面（包含AI配置组）
```

### 扩展性
- 模块化设计，易于添加新的AI平台
- 统一接口，支持异步和同步调用
- 配置驱动，无需修改代码即可支持新模型

---

🎉 **恭喜！您现在拥有了一个完整的AI集成提示词管理系统！** 