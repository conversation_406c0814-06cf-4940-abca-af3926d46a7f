#!/usr/bin/env python3
"""
调试UI事件连接问题
"""

from PySide6.QtWidgets import QApplication
import sys
from model import PromptModel, Prompt
from main import PromptCardWidget

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🔍 开始调试UI事件连接问题...")

# 获取一个测试提示词
model = PromptModel()
prompts = model.get_all_prompts()
if not prompts:
    print("❌ 没有找到测试数据")
    exit(1)

test_prompt = prompts[0]
print(f"📝 测试提示词: ID={test_prompt.id}, 收藏状态={test_prompt.is_favorite}")

# 创建PromptCardWidget
print("🏗️ 创建PromptCardWidget...")
card = PromptCardWidget(test_prompt)
print("✅ PromptCardWidget创建成功")

# 检查action_buttons
if 'favorite' in card.action_buttons:
    btn = card.action_buttons['favorite']
    print(f"🔘 收藏按钮属性:")
    print(f"   is_favorited: {getattr(btn, 'is_favorited', '未设置')}")
    print(f"   normal_svg: {hasattr(btn, 'normal_svg')}")
    print(f"   active_svg: {hasattr(btn, 'active_svg')}")
    
    # 检查信号连接
    try:
        signal_count = btn.clicked.receivers()
        print(f"   连接的信号数量: {signal_count}")
    except Exception as e:
        print(f"   无法获取信号信息: {e}")
    
    # 检查card是否有on_favorite_action方法
    if hasattr(card, 'on_favorite_action'):
        print("   ✅ card有on_favorite_action方法")
    else:
        print("   ❌ card缺少on_favorite_action方法")
    
    # 尝试手动触发按钮点击
    print("🖱️ 模拟点击收藏按钮...")
    
    # 记录点击前状态
    original_state = test_prompt.is_favorite
    print(f"   点击前数据库状态: {original_state}")
    
    # 触发点击信号
    btn.clicked.emit()
    
    # 检查点击后状态
    updated_prompt = model.get_prompt(test_prompt.id)
    print(f"   点击后数据库状态: {updated_prompt.is_favorite}")
    
    if updated_prompt.is_favorite != original_state:
        print("✅ 事件连接正常工作！数据库状态已更新")
    else:
        print("❌ 事件连接可能有问题，数据库状态未改变")
        
    # 恢复原始状态
    if updated_prompt.is_favorite != original_state:
        model.toggle_favorite(test_prompt.id)
        print("🔄 已恢复原始状态")
        
else:
    print("❌ 未找到收藏按钮！")

print("🏁 调试完成") 