#!/usr/bin/env python3
"""
OpenAI API适配器
实现OpenAI GPT模型的API调用
"""
import asyncio
import json
import time
from typing import Dict, List, Optional, Any
import aiohttp

from .base_adapter import BaseAdapter, AIRequest, AIResponse, APIError
from config.ai_config import AI_PLATFORMS

class OpenAIAdapter(BaseAdapter):
    """OpenAI API适配器"""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        """
        初始化OpenAI适配器
        :param api_key: OpenAI API密钥
        :param base_url: 自定义API基础URL
        """
        default_base_url = AI_PLATFORMS['openai']['base_url']
        super().__init__(api_key, base_url or default_base_url)
        self.platform_config = AI_PLATFORMS['openai']
    
    @property
    def platform_name(self) -> str:
        """平台名称"""
        return "OpenAI"
    
    @property
    def supported_models(self) -> List[str]:
        """支持的模型列表"""
        return list(self.platform_config['models'].keys())
    
    async def chat_completion(self, request: AIRequest) -> AIResponse:
        """
        发送OpenAI聊天完成请求
        :param request: AI请求
        :return: AI响应
        :raises: APIError
        """
        start_time = time.time()
        
        # 构建请求数据
        payload = {
            "model": request.model,
            "messages": [
                {"role": "user", "content": request.prompt}
            ],
            "temperature": request.temperature,
            "max_tokens": request.max_tokens,
            "top_p": request.top_p,
            "presence_penalty": request.presence_penalty,
            "frequency_penalty": request.frequency_penalty
        }
        
        # 添加额外参数
        if request.extra_params:
            payload.update(request.extra_params)
        
        # 发送请求
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.get_headers(),
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    
                    latency = time.time() - start_time
                    response_data = await response.json()
                    
                    if response.status != 200:
                        error_msg = response_data.get('error', {}).get('message', 'Unknown error')
                        raise APIError(
                            code=str(response.status),
                            message=error_msg,
                            details=response_data
                        )
                    
                    # 解析响应
                    choice = response_data['choices'][0]
                    content = choice['message']['content']
                    finish_reason = choice.get('finish_reason')
                    
                    usage = response_data.get('usage', {})
                    total_tokens = usage.get('total_tokens', 0)
                    prompt_tokens = usage.get('prompt_tokens', 0)
                    completion_tokens = usage.get('completion_tokens', 0)
                    
                    # 计算费用
                    cost = self.calculate_cost(request.model, prompt_tokens, completion_tokens)
                    
                    return AIResponse(
                        content=content,
                        model=request.model,
                        tokens_used=total_tokens,
                        cost=cost,
                        latency=latency,
                        request_id=response_data.get('id'),
                        finish_reason=finish_reason,
                        extra_data={
                            'usage': usage,
                            'response_data': response_data
                        }
                    )
                    
        except aiohttp.ClientError as e:
            raise APIError(
                code="CLIENT_ERROR",
                message=f"请求失败: {str(e)}",
                details={"exception": str(e)}
            )
        except asyncio.TimeoutError:
            raise APIError(
                code="TIMEOUT",
                message="请求超时",
                details={"timeout": 30}
            )
        except Exception as e:
            raise APIError(
                code="UNKNOWN_ERROR",
                message=f"未知错误: {str(e)}",
                details={"exception": str(e)}
            )
    
    def validate_api_key(self) -> bool:
        """
        验证OpenAI API密钥是否有效
        :return: True if valid, False otherwise
        """
        try:
            # 使用同步方式进行简单验证
            import requests
            
            response = requests.get(
                f"{self.base_url}/models",
                headers=self.get_headers(),
                timeout=10
            )
            
            return response.status_code == 200
            
        except Exception:
            return False
    
    def get_model_info(self, model: str) -> Dict[str, Any]:
        """
        获取OpenAI模型信息
        :param model: 模型名称
        :return: 模型信息字典
        """
        models = self.platform_config['models']
        if model not in models:
            raise ValueError(f"不支持的模型: {model}")
        
        return models[model]
    
    def get_headers(self) -> Dict[str, str]:
        """
        获取OpenAI请求头
        :return: 请求头字典
        """
        return {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}',
            'User-Agent': 'PromptAssistant/1.0'
        }

# 同步版本的简单实现（用于测试）
class OpenAIAdapterSync:
    """OpenAI适配器同步版本（用于测试）"""
    
    def __init__(self, api_key: str, base_url: Optional[str] = None):
        self.api_key = api_key
        self.base_url = base_url or AI_PLATFORMS['openai']['base_url']
        self.platform_config = AI_PLATFORMS['openai']
    
    def chat_completion_sync(self, prompt: str, model: str = "gpt-3.5-turbo") -> Dict[str, Any]:
        """
        同步版本的聊天完成（用于测试）
        :param prompt: 提示词
        :param model: 模型名称
        :return: 响应数据
        """
        import requests
        
        payload = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": 0.7,
            "max_tokens": 1000
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.api_key}'
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                return {
                    'success': False,
                    'error': response.json().get('error', {}).get('message', 'Unknown error')
                }
            
            data = response.json()
            content = data['choices'][0]['message']['content']
            
            return {
                'success': True,
                'content': content,
                'usage': data.get('usage', {}),
                'model': model
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            } 