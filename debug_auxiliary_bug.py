import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import PromptModel, Prompt

def debug_auxiliary_bug():
    model = PromptModel()
    
    # 创建一个测试提示词
    prompt = Prompt(
        title="调试测试",
        content="测试内容",
        category="通用",
        tags=["测试"],
        auxiliary_fields={
            "负向提示词": "这是负向提示词",
            "参数设置": "steps=20",
            "自定义": "自定义内容"
        },
        media_files=["test1.jpg", "test2.mp4"]
    )
    
    print("原始auxiliary_fields:", prompt.auxiliary_fields)
    print("原始media_files:", prompt.media_files)
    
    # 保存到数据库
    prompt_id = model.add_prompt(prompt)
    print(f"保存的ID: {prompt_id}")
    
    # 从数据库获取
    retrieved = model.get_prompt(prompt_id)
    print("从数据库获取的auxiliary_fields:", retrieved.auxiliary_fields)
    print("从数据库获取的media_files:", retrieved.media_files)
    
    # 检查是否有问题
    if retrieved.auxiliary_fields == retrieved.media_files:
        print("问题发现：auxiliary_fields 和 media_files 相同！")
    else:
        print("数据正常")

if __name__ == "__main__":
    debug_auxiliary_bug() 