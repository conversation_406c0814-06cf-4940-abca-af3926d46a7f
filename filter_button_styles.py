def get_active_filter_button_style(self):
    """获取激活状态的筛选按钮样式"""
    return """
        QPushButton {
            background-color: #EBF4FF;
            color: #1E40AF;
            border: 1px solid #BFDBFE;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        QPushButton:hover {
            background-color: #DBEAFE;
            border-color: #93C5FD;
        }
        QToolTip {
            background-color: #374151;
            color: white;
            border: 1px solid #4B5563;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }
    """
    
def get_filter_button_style(self):
    """获取默认状态的筛选按钮样式"""
    return """
        QPushButton {
            background-color: white;
            color: #374151;
            border: 1px solid #D1D5DB;
            border-radius: 4px;
            padding: 6px 12px;
            font-size: 12px;
            margin: 2px;
        }
        QPushButton:hover {
            background-color: #F3F4F6;
            border-color: #9CA3AF;
        }
        QToolTip {
            background-color: #374151;
            color: white;
            border: 1px solid #4B5563;
            border-radius: 4px;
            padding: 4px;
            font-size: 11px;
        }
    """ 