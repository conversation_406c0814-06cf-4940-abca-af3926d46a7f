#!/usr/bin/env python3
"""
从文件导入提示词对话框
从main.py提取的ImportFileDialog组件
"""
import json
import csv
import re
from pathlib import Path
from PySide6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame, QTextEdit, QMessageBox, QFileDialog)
from PySide6.QtCore import Qt

from views.base_components import BaseDialog
from model import Prompt


class ImportFileDialog(BaseDialog):
    """从文件导入提示词对话框"""
    
    def __init__(self, parent=None, model=None):
        super().__init__(parent)
        self.model = model
        self.analyzed_prompts = []  # 存储分析结果
        self.selected_file_path = None
        
        self.setWindowTitle("从文件导入提示词")
        self.setFixedSize(500, 600)
        
        # 应用统一的对话框样式
        from components.style_manager import StyleManager
        self.setStyleSheet(StyleManager.get_dialog_style('import_file'))
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # 标题
        title_label = QLabel("从文件导入提示词")
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 16px;
                font-weight: 600;
            }
        """)
        layout.addWidget(title_label)
        
        # 说明文字
        desc_label = QLabel("选择要导入的提示词文件（支持JSON、CSV、Markdown格式）")
        desc_label.setStyleSheet("color: #374151; font-size: 14px;")
        layout.addWidget(desc_label)
        
        # 文件选择按钮
        self.import_btn = self.create_secondary_button("选择文件", "#10B981")
        self.import_btn.clicked.connect(self.select_file)
        layout.addWidget(self.import_btn)
        
        # 分析结果显示区域
        self.analysis_group = QFrame()
        self.analysis_group.setStyleSheet("""
            QFrame {
                border: 1px solid #E5E7EB;
                border-radius: 6px;
                background-color: #F9FAFB;
                padding: 10px;
            }
        """)
        self.analysis_group.setVisible(False)
        layout.addWidget(self.analysis_group)
        
        analysis_layout = QVBoxLayout(self.analysis_group)
        analysis_layout.setSpacing(15)
        
        # 文件信息
        self.file_info_label = QLabel()
        self.file_info_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 500;
            }
        """)
        analysis_layout.addWidget(self.file_info_label)
        
        # 提示词数量
        self.count_label = QLabel()
        self.count_label.setStyleSheet("color: #374151; font-size: 13px;")
        analysis_layout.addWidget(self.count_label)
        
        # 提示词预览列表
        self.preview_label = QLabel("提示词预览:")
        self.preview_label.setStyleSheet("color: #374151; font-size: 13px; font-weight: 500;")
        analysis_layout.addWidget(self.preview_label)
        
        self.preview_list = QTextEdit()
        self.preview_list.setStyleSheet("""
            QTextEdit {
                border: 1px solid #E5E7EB;
                border-radius: 4px;
                background-color: white;
                padding: 8px;
                font-size: 12px;
                max-height: 200px;
            }
        """)
        self.preview_list.setReadOnly(True)
        analysis_layout.addWidget(self.preview_list)
        
        # 确认导入按钮
        self.confirm_btn = self.create_primary_button("确认导入")
        self.confirm_btn.clicked.connect(self.confirm_import)
        self.confirm_btn.setVisible(False)
        analysis_layout.addWidget(self.confirm_btn)
        
        # 底部按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        
        cancel_btn = self.create_outline_button("取消")
        cancel_btn.clicked.connect(self.reject)
        
        button_layout.addStretch()
        button_layout.addWidget(cancel_btn)
        layout.addLayout(button_layout)
    
    def select_file(self):
        """选择文件并分析"""
        if not self.model:
            QMessageBox.warning(self, "错误", "无法获取提示词数据")
            return
        
        # 打开文件选择对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择提示词文件",
            "",
            "所有支持的文件 (*.json *.csv *.md);;JSON文件 (*.json);;CSV文件 (*.csv);;Markdown文件 (*.md)"
        )
        
        if not file_path:
            return
        
        self.selected_file_path = file_path
        
        try:
            # 分析文件
            self.analyze_file(file_path)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"文件分析失败: {str(e)}")
    
    def analyze_file(self, file_path):
        """分析文件内容"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.json':
            self.analyze_json_file(file_path)
        elif file_ext == '.csv':
            self.analyze_csv_file(file_path)
        elif file_ext == '.md':
            self.analyze_markdown_file(file_path)
        else:
            QMessageBox.warning(self, "错误", "不支持的文件格式")
            return
    
    def analyze_json_file(self, file_path):
        """分析JSON文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            data = [data]
        
        self.analyzed_prompts = []
        for item in data:
            try:
                prompt = Prompt.from_dict(item)
                self.analyzed_prompts.append(prompt)
            except Exception as e:
                print(f"解析提示词失败: {e}")
        
        self.show_analysis_result("JSON", file_path)
    
    def analyze_csv_file(self, file_path):
        """分析CSV文件"""
        self.analyzed_prompts = []
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # 解析标签
                    tags = []
                    if row.get('标签'):
                        tags = [tag.strip() for tag in row['标签'].split(',') if tag.strip()]
                    
                    # 创建Prompt对象
                    prompt = Prompt(
                        title=row.get('标题', ''),
                        content=row.get('内容', ''),
                        category=row.get('分类', ''),
                        tags=tags,
                        type=row.get('类型', '文本'),
                        is_favorite=1 if row.get('收藏') == '是' else 0,
                        is_pinned=1 if row.get('置顶') == '是' else 0
                    )
                    self.analyzed_prompts.append(prompt)
                except Exception as e:
                    print(f"解析提示词失败: {e}")
        
        self.show_analysis_result("CSV", file_path)
    
    def analyze_markdown_file(self, file_path):
        """分析Markdown文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的Markdown解析
        # 分割为各个提示词块
        prompt_blocks = re.split(r'## \d+\.', content)
        
        self.analyzed_prompts = []
        for block in prompt_blocks[1:]:  # 跳过第一个空块
            try:
                lines = block.strip().split('\n')
                if not lines:
                    continue
                
                # 提取标题（第一行）
                title = lines[0].strip()
                
                # 提取其他信息
                category = ''
                tags = []
                prompt_content = ''
                prompt_type = '文本'
                
                in_content = False
                for line in lines[1:]:
                    line = line.strip()
                    if not line or line == '---':
                        continue
                    
                    if line.startswith('**分类:**'):
                        category = line.replace('**分类:**', '').strip()
                    elif line.startswith('**标签:**'):
                        tags_text = line.replace('**标签:**', '').strip()
                        tags = [tag.strip('`') for tag in re.findall(r'`([^`]+)`', tags_text)]
                    elif line.startswith('**类型:**'):
                        prompt_type = line.replace('**类型:**', '').strip()
                    elif line.startswith('**内容:**'):
                        in_content = True
                        continue
                    elif in_content and line.startswith('**'):
                        in_content = False
                    elif in_content:
                        prompt_content += line + '\n'
                
                # 创建Prompt对象
                prompt = Prompt(
                    title=title,
                    content=prompt_content.strip(),
                    category=category,
                    tags=tags,
                    type=prompt_type
                )
                self.analyzed_prompts.append(prompt)
                
            except Exception as e:
                print(f"解析提示词失败: {e}")
        
        self.show_analysis_result("Markdown", file_path)
    
    def show_analysis_result(self, file_type, file_path):
        """显示分析结果"""
        file_name = Path(file_path).name
        total_count = len(self.analyzed_prompts)
        
        # 更新文件信息
        self.file_info_label.setText(f"文件: {file_name} ({file_type}格式)")
        
        # 更新数量信息
        self.count_label.setText(f"发现 {total_count} 个提示词")
        
        # 更新预览列表
        preview_text = ""
        for i, prompt in enumerate(self.analyzed_prompts[:5], 1):  # 只显示前5个
            preview_text += f"{i}. {prompt.title}\n"
            if prompt.category:
                preview_text += f"   分类: {prompt.category}\n"
            if prompt.tags:
                tags_str = ', '.join(prompt.tags)
                preview_text += f"   标签: {tags_str}\n"
            preview_text += "\n"
        
        if total_count > 5:
            preview_text += f"... 还有 {total_count - 5} 个提示词\n"
        
        self.preview_list.setPlainText(preview_text.strip())
        
        # 显示分析结果区域和确认按钮
        self.analysis_group.setVisible(True)
        self.confirm_btn.setVisible(True)
    
    def confirm_import(self):
        """确认导入"""
        if not self.analyzed_prompts:
            QMessageBox.warning(self, "错误", "没有可导入的提示词")
            return
        
        try:
            # 执行实际导入
            imported_count = 0
            for prompt in self.analyzed_prompts:
                try:
                    self.model.add_prompt(prompt)
                    imported_count += 1
                except Exception as e:
                    print(f"导入提示词失败: {e}")
            
            QMessageBox.information(self, "成功", f"成功导入 {imported_count} 个提示词")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入失败: {str(e)}")
    
    def import_from_file(self):
        """从文件导入提示词（保留原方法以兼容）"""
        self.select_file()
    
    def import_from_json(self, file_path):
        """从JSON文件导入（直接导入，不分析）"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            data = [data]
        
        imported_count = 0
        for item in data:
            try:
                # 创建Prompt对象
                prompt = Prompt.from_dict(item)
                # 添加到数据库
                self.model.add_prompt(prompt)
                imported_count += 1
            except Exception as e:
                print(f"导入提示词失败: {e}")
        
        print(f"成功导入 {imported_count} 个提示词")
    
    def import_from_csv(self, file_path):
        """从CSV文件导入（直接导入，不分析）"""
        imported_count = 0
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                try:
                    # 解析标签
                    tags = []
                    if row.get('标签'):
                        tags = [tag.strip() for tag in row['标签'].split(',') if tag.strip()]
                    
                    # 创建Prompt对象
                    prompt = Prompt(
                        title=row.get('标题', ''),
                        content=row.get('内容', ''),
                        category=row.get('分类', ''),
                        tags=tags,
                        type=row.get('类型', '文本'),
                        is_favorite=1 if row.get('收藏') == '是' else 0,
                        is_pinned=1 if row.get('置顶') == '是' else 0
                    )
                    
                    # 添加到数据库
                    self.model.add_prompt(prompt)
                    imported_count += 1
                except Exception as e:
                    print(f"导入提示词失败: {e}")
        
        print(f"成功导入 {imported_count} 个提示词")
    
    def import_from_markdown(self, file_path):
        """从Markdown文件导入（直接导入，不分析）"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的Markdown解析
        # 分割为各个提示词块
        prompt_blocks = re.split(r'## \d+\.', content)
        
        imported_count = 0
        for block in prompt_blocks[1:]:  # 跳过第一个空块
            try:
                lines = block.strip().split('\n')
                if not lines:
                    continue
                
                # 提取标题（第一行）
                title = lines[0].strip()
                
                # 提取其他信息
                category = ''
                tags = []
                prompt_content = ''
                prompt_type = '文本'
                
                in_content = False
                for line in lines[1:]:
                    line = line.strip()
                    if not line or line == '---':
                        continue
                    
                    if line.startswith('**分类:**'):
                        category = line.replace('**分类:**', '').strip()
                    elif line.startswith('**标签:**'):
                        tags_text = line.replace('**标签:**', '').strip()
                        tags = [tag.strip('`') for tag in re.findall(r'`([^`]+)`', tags_text)]
                    elif line.startswith('**类型:**'):
                        prompt_type = line.replace('**类型:**', '').strip()
                    elif line.startswith('**内容:**'):
                        in_content = True
                        continue
                    elif in_content and line.startswith('**'):
                        in_content = False
                    elif in_content:
                        prompt_content += line + '\n'
                
                # 创建Prompt对象
                prompt = Prompt(
                    title=title,
                    content=prompt_content.strip(),
                    category=category,
                    tags=tags,
                    type=prompt_type
                )
                
                # 添加到数据库
                self.model.add_prompt(prompt)
                imported_count += 1
                
            except Exception as e:
                print(f"导入提示词失败: {e}")
        
        print(f"成功导入 {imported_count} 个提示词") 