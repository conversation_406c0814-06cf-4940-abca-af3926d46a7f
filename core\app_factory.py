#!/usr/bin/env python3
"""
应用工厂模块
负责创建和配置应用的所有组件，实现依赖注入
"""
from typing import Dict, Any, Optional
from model import PromptModel
from services.prompt_service import PromptService
from services.tag_service import TagService  
from services.category_service import CategoryService
from services.color_service import ColorService
from controllers.app_controller import AppController
from core.dependency_container import DependencyContainer, injectable, inject
from interfaces.service_interfaces import (
    IPromptService, ITagService, ICategoryService, IColorService
)


class AppFactory:
    """应用工厂类，负责创建和配置所有服务实例"""
    
    def __init__(self):
        """初始化应用工厂"""
        self.container = DependencyContainer()
        self._setup_dependencies()
    
    def _setup_dependencies(self):
        """设置依赖关系"""
        # 创建服务实例（不使用容器，直接创建）
        self.model = PromptModel("sqlite")
        self.prompt_service = PromptService(self.model)
        self.tag_service = TagService(self.model)
        self.category_service = CategoryService(self.model)
        self.color_service = ColorService()
        
        # 创建控制器实例
        self.app_controller = AppController(
            prompt_service=self.prompt_service,
            tag_service=self.tag_service,
            category_service=self.category_service,
            color_service=self.color_service
        )
    
    def get_model(self) -> PromptModel:
        """获取数据模型实例"""
        return self.model
    
    def get_prompt_service(self) -> IPromptService:
        """获取提示词服务实例"""
        return self.prompt_service
    
    def get_tag_service(self) -> ITagService:
        """获取标签服务实例"""
        return self.tag_service
    
    def get_category_service(self) -> ICategoryService:
        """获取分类服务实例"""
        return self.category_service
    
    def get_color_service(self) -> IColorService:
        """获取颜色服务实例"""
        return self.color_service
    
    def get_app_controller(self) -> AppController:
        """获取应用控制器实例"""
        return self.app_controller
    
    def create_main_window(self):
        """创建主窗口实例"""
        # 这里会在后续步骤中实现，现在先返回None
        return None
    
    def configure_services(self, config: Dict[str, Any]):
        """配置服务设置"""
        # 配置缓存设置
        if "cache_ttl" in config:
            prompt_service = self.get_prompt_service()
            if hasattr(prompt_service, '_cache_ttl'):
                prompt_service._cache_ttl = config["cache_ttl"]
        
        # 配置调试模式
        if "debug_mode" in config:
            # 可以在这里配置调试相关设置
            pass
    
    def get_container(self) -> DependencyContainer:
        """获取依赖注入容器"""
        return self.container


# 全局应用工厂实例
app_factory: Optional[AppFactory] = None

def get_app_factory() -> AppFactory:
    """获取应用工厂单例"""
    global app_factory
    if app_factory is None:
        app_factory = AppFactory()
    return app_factory

def initialize_app(config: Optional[Dict[str, Any]] = None) -> AppFactory:
    """初始化应用"""
    factory = get_app_factory()
    if config:
        factory.configure_services(config)
    return factory 