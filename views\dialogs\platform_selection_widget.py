#!/usr/bin/env python3
"""
AI平台选择组件
提供现代化的平台卡片选择界面
"""

from PySide6.QtWidgets import (
    QWidget, QLabel, QVBoxLayout, QHBoxLayout, QGraphicsDropShadowEffect
)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QPainter, QPixmap, QColor, QLinearGradient, QBrush, QFont
from pathlib import Path

# 导入设计系统
from config.design_system import COLORS, SPACING, RADIUS, SHADOWS, TYPOGRAPHY, PLATFORM_COLORS


class PlatformCard(QWidget):
    """现代化平台选择卡片组件"""
    
    platform_selected = Signal(str)  # 平台被选中信号
    
    def __init__(self, platform_id, platform_config, configured=False):
        super().__init__()
        self.platform_id = platform_id
        self.platform_config = platform_config
        self.configured = configured
        self.is_hovered = False
        
        # 设置固定尺寸
        self.setFixedSize(220, 140)
        
        # 设置基础样式和阴影
        self.setup_card_style()
        self.setup_shadow_effect()
        self.setup_card_ui()
        
        # 设置鼠标跟踪
        self.setMouseTracking(True)
    
    def setup_card_style(self):
        """设置卡片基础样式"""
        self.setStyleSheet(f"""
            PlatformCard {{
                background-color: {COLORS['white']};
                border: 1px solid {COLORS['gray_200']};
                border-radius: {RADIUS['lg']};
            }}
        """)
    
    def setup_shadow_effect(self):
        """设置阴影效果"""
        self.shadow_effect = QGraphicsDropShadowEffect()
        self.shadow_effect.setBlurRadius(8)
        self.shadow_effect.setXOffset(0)
        self.shadow_effect.setYOffset(2)
        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
        self.setGraphicsEffect(self.shadow_effect)
    
    def setup_card_ui(self):
        """设置卡片UI布局"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(12)
        
        # 顶部区域：图标和状态
        top_layout = QHBoxLayout()
        top_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建平台图标
        self.icon_label = self.create_platform_icon()
        top_layout.addWidget(self.icon_label)
        top_layout.addStretch()
        
        # 配置状态指示器
        if self.configured:
            status_widget = self.create_status_indicator()
            top_layout.addWidget(status_widget)
        
        layout.addLayout(top_layout)
        
        # 中部：平台名称
        self.name_label = QLabel(self.platform_config.get('display_name', '未知平台'))
        self.name_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_800']};
                font-size: {TYPOGRAPHY['sizes']['lg']};
                font-weight: {TYPOGRAPHY['weights']['semibold']};
                font-family: {TYPOGRAPHY['font_family']};
                background: transparent;
                border: none;
            }}
        """)
        layout.addWidget(self.name_label)
        
        # 底部：描述文本
        self.desc_label = QLabel(self.platform_config.get('description', ''))
        self.desc_label.setStyleSheet(f"""
            QLabel {{
                color: {COLORS['gray_500']};
                font-size: {TYPOGRAPHY['sizes']['sm']};
                font-weight: {TYPOGRAPHY['weights']['normal']};
                font-family: {TYPOGRAPHY['font_family']};
                line-height: {TYPOGRAPHY['line_heights']['relaxed']};
                background: transparent;
                border: none;
            }}
        """)
        self.desc_label.setWordWrap(True)
        layout.addWidget(self.desc_label)
        
        # 添加底部弹性空间
        layout.addStretch()
    
    def create_platform_icon(self):
        """创建精美的平台图标"""
        icon_label = QLabel()
        icon_label.setFixedSize(48, 48)
        
        # 获取平台专属颜色
        platform_color = PLATFORM_COLORS.get(self.platform_id, COLORS['gray_500'])
        
        # 创建渐变背景图标
        pixmap = QPixmap(48, 48)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 创建渐变效果
        gradient = QLinearGradient(0, 0, 48, 48)
        gradient.setColorAt(0, QColor(platform_color))
        gradient.setColorAt(1, QColor(platform_color).darker(120))
        
        # 绘制圆形背景
        painter.setBrush(QBrush(gradient))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, 48, 48)
        
        # 绘制平台标识文字
        painter.setPen(QColor(COLORS['white']))
        font = QFont(TYPOGRAPHY['font_family'].split(',')[0], 16, QFont.Bold)
        painter.setFont(font)
        
        # 获取平台标识（取前两个字符）
        icon_text = self.platform_config.get('display_name', 'AI')[:2].upper()
        painter.drawText(0, 0, 48, 48, Qt.AlignCenter, icon_text)
        
        painter.end()
        
        icon_label.setPixmap(pixmap)
        return icon_label
    
    def create_status_indicator(self):
        """创建现代化状态指示器"""
        status_widget = QWidget()
        status_widget.setFixedSize(24, 24)
        
        # 创建状态图标
        pixmap = QPixmap(24, 24)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制成功状态圆圈背景
        painter.setBrush(QBrush(QColor(COLORS['success'])))
        painter.setPen(Qt.NoPen)
        painter.drawEllipse(0, 0, 24, 24)
        
        # 绘制白色勾号
        painter.setPen(QColor(COLORS['white']))
        painter.setStrokeWidth(2)
        # 简化的勾号路径
        painter.drawLine(7, 12, 10, 15)
        painter.drawLine(10, 15, 17, 8)
        
        painter.end()
        
        status_label = QLabel()
        status_label.setPixmap(pixmap)
        status_label.setFixedSize(24, 24)
        
        return status_label
    
    def update_hover_state(self, hovered):
        """更新悬停状态"""
        if self.is_hovered != hovered:
            self.is_hovered = hovered
            
            if hovered:
                # 悬停状态：增强阴影和边框
                self.setStyleSheet(f"""
                    PlatformCard {{
                        background-color: {COLORS['gray_50']};
                        border: 1px solid {COLORS['primary']};
                        border-radius: {RADIUS['lg']};
                    }}
                """)
                
                # 更新阴影效果
                if hasattr(self, 'shadow_effect') and self.shadow_effect:
                    try:
                        self.shadow_effect.setBlurRadius(16)
                        self.shadow_effect.setYOffset(4)
                        self.shadow_effect.setColor(QColor(59, 130, 246, 40))  # 蓝色阴影
                    except RuntimeError:
                        pass
            else:
                # 默认状态
                self.setStyleSheet(f"""
                    PlatformCard {{
                        background-color: {COLORS['white']};
                        border: 1px solid {COLORS['gray_200']};
                        border-radius: {RADIUS['lg']};
                    }}
                """)
                
                # 恢复默认阴影
                if hasattr(self, 'shadow_effect') and self.shadow_effect:
                    try:
                        self.shadow_effect.setBlurRadius(8)
                        self.shadow_effect.setYOffset(2)
                        self.shadow_effect.setColor(QColor(0, 0, 0, 30))
                    except RuntimeError:
                        pass
    
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.update_hover_state(True)
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_hover_state(False)
        super().leaveEvent(event)
    
    def mousePressEvent(self, event):
        """处理点击事件"""
        if event.button() == Qt.LeftButton:
            # 点击时的视觉反馈
            self.setStyleSheet(f"""
                PlatformCard {{
                    background-color: {COLORS['primary_light']};
                    border: 1px solid {COLORS['primary']};
                    border-radius: {RADIUS['lg']};
                }}
            """)
            
            # 发射选择信号
            self.platform_selected.emit(self.platform_id)
        
        super().mousePressEvent(event)
    
    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            # 恢复悬停状态
            self.update_hover_state(True)
        super().mouseReleaseEvent(event) 