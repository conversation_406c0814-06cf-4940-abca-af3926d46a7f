#!/usr/bin/env python3
"""
测试历史记录保存逻辑的正确性
检查是否应该保存旧版本而不是新版本到历史记录
"""

from model import PromptModel, Prompt
from datetime import datetime

print("🧪 历史记录保存逻辑测试")
print("=" * 50)

model = PromptModel()

# 创建初始提示词
print("📝 创建初始提示词（版本1）")
prompt = Prompt(
    title="逻辑测试",
    content="这是版本1的内容",
    category="测试",
    created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    updated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
)

prompt_id = model.add_prompt(prompt)
print(f"✅ 创建完成，ID={prompt_id}")

# 检查初始历史记录
print("\n📋 初始历史记录:")
history = model.get_prompt_history(prompt_id)
for h in history:
    print(f"  版本{h.version}: '{h.content}'")

print("\n" + "="*50)
print("🔍 关键问题分析：历史记录保存逻辑")

# 模拟编辑过程，详细跟踪
print("\n步骤1：准备编辑（版本1 → 版本2）")
current_prompt = model.get_prompt(prompt_id)
print(f"编辑前的当前内容: '{current_prompt.content}'")

print("\n步骤2：修改内容")
current_prompt.content = "这是版本2的内容"
current_prompt.updated_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
print(f"准备保存的新内容: '{current_prompt.content}'")

print("\n步骤3：执行update_prompt")
print("🤔 思考：update_prompt应该做什么？")
print("方案A（当前实现）：保存新版本到历史记录")
print("方案B（常见逻辑）：保存旧版本到历史记录")

model.update_prompt(current_prompt)

print("\n步骤4：检查更新后的状态")
updated_prompt = model.get_prompt(prompt_id)
print(f"更新后的当前内容: '{updated_prompt.content}'")

history_after = model.get_prompt_history(prompt_id)
print("更新后的历史记录:")
for h in history_after:
    print(f"  版本{h.version}: '{h.content}'")

print("\n🔍 逻辑分析:")
print("根据常见的版本控制逻辑，历史记录应该保存'被替换的旧版本'")
print("这样用户可以看到所有曾经使用过的版本")

print("\n当前逻辑问题分析:")
has_old_version = any(h.content == "这是版本1的内容" for h in history_after)
has_new_version = any(h.content == "这是版本2的内容" for h in history_after)

print(f"历史记录中有旧版本（版本1）？ {has_old_version}")
print(f"历史记录中有新版本（版本2）？ {has_new_version}")
print(f"当前提示词内容: '{updated_prompt.content}'")

if has_new_version and updated_prompt.content == "这是版本2的内容":
    print("\n🐛 发现逻辑问题！")
    print("当前版本和历史记录中的最新版本内容相同")
    print("这意味着历史记录保存的是'新版本'而不是'旧版本'")
    print("用户看到的历史记录不包含'真正的当前版本'")
    print("\n用户体验问题：")
    print("- 用户编辑前点击历史记录：看不到当前版本")
    print("- 用户编辑后点击历史记录：看不到刚刚保存的版本")
else:
    print("✅ 逻辑看起来正常")

# 清理
print(f"\n🧹 清理测试数据...")
model.permanently_delete_prompt(prompt_id)
print("✅ 清理完成")

print("\n" + "=" * 50)
print("🏁 测试完成")

print("\n💡 修复建议:")
print("历史记录保存逻辑应该改为：")
print("1. 在更新主表之前，先保存当前版本到历史记录")
print("2. 然后再更新主表为新版本")
print("3. 这样历史记录始终包含所有曾经使用过的版本") 