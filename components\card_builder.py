#!/usr/bin/env python3
"""
卡片建造者模块
使用建造者模式构建PromptCardWidget，消除复杂的构建逻辑
"""
from typing import Optional, Dict, List
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                              QPushButton, QFrame)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPainter, QPen, QColor

from model import Prompt
from components.button_factory import ButtonFactory
from components.style_manager import StyleManager


class PromptCardBuilder:
    """提示词卡片建造者 - 使用建造者模式逐步构建卡片"""
    
    def __init__(self):
        self.card = None
        self.prompt = None
        self.color_service = None
        self._main_layout = None
    
    def create_card(self, prompt: Prompt, color_service=None) -> 'PromptCardBuilder':
        """创建基础卡片框架"""
        # 导入这里是为了避免循环导入
        from main import PromptCardWidget
        
        self.prompt = prompt
        self.color_service = color_service
        
        # 创建卡片基础框架，但不初始化UI
        self.card = QFrame()
        self.card.prompt = prompt
        self.card.color_service = color_service
        self.card.is_selected = False
        self.card.hovered = False
        
        # 设置基础样式
        self.card.setStyleSheet("""
            QFrame {
                background-color: white;
            }
        """)
        
        # 创建主布局
        self._main_layout = QVBoxLayout(self.card)
        self._main_layout.setContentsMargins(12, 10, 12, 10)
        self._main_layout.setSpacing(8)
        
        return self
    
    def add_header(self) -> 'PromptCardBuilder':
        """添加头部区域（分类 + 操作按钮）"""
        if not self.card:
            raise ValueError("必须先调用create_card()方法")
        
        header_widget = self._create_header_section()
        self._main_layout.addWidget(header_widget)
        
        return self
    
    def add_title(self) -> 'PromptCardBuilder':
        """添加标题区域"""
        if not self.card:
            raise ValueError("必须先调用create_card()方法")
        
        title_widget = self._create_title_section()
        self._main_layout.addWidget(title_widget)
        
        return self
    
    def add_content(self) -> 'PromptCardBuilder':
        """添加内容区域"""
        if not self.card:
            raise ValueError("必须先调用create_card()方法")
        
        content_widget = self._create_content_section()
        self._main_layout.addWidget(content_widget)
        
        return self
    
    def add_media_carousel(self) -> 'PromptCardBuilder':
        """添加媒体轮播（如果有媒体文件）"""
        # 媒体轮播在add_content中已经处理
        return self
    
    def add_tags(self) -> 'PromptCardBuilder':
        """添加标签区域"""
        if not self.card:
            raise ValueError("必须先调用create_card()方法")
        
        tags_widget = self._create_tags_section()
        self._main_layout.addWidget(tags_widget)
        
        return self
    
    def add_actions(self) -> 'PromptCardBuilder':
        """添加操作按钮（已在header中处理）"""
        # 操作按钮在add_header中已经处理
        return self
    
    def build(self) -> QFrame:
        """构建最终的卡片"""
        if not self.card:
            raise ValueError("必须先调用create_card()方法")
        
        # 设置自定义绘制事件
        self.card.paintEvent = self._create_paint_event()
        
        # 设置鼠标事件
        self._setup_mouse_events()
        
        return self.card
    
    def _create_header_section(self) -> QWidget:
        """创建头部区域 - 复制自原始create_top_section方法"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        
        # 左侧分类信息
        category_name = getattr(self.prompt, 'category', '通用')
        if self.color_service:
            category_color = self.color_service.get_category_color(category_name)
        else:
            category_color = "#F3F4F6"  # 默认颜色
        
        category_label = QLabel(category_name)
        category_label.setStyleSheet(f"""
            QLabel {{
                background-color: {category_color};
                color: #1F2937;
                font-size: 10px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }}
        """)
        layout.addWidget(category_label)
        
        layout.addStretch()
        
        # 右侧操作按钮组
        buttons_widget = self._create_action_buttons()
        layout.addWidget(buttons_widget)
        
        return widget
    
    def _create_action_buttons(self) -> QWidget:
        """创建操作按钮组 - 使用ButtonFactory"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(2)
        
        # 获取按钮图标
        button_icons = self._get_action_button_icons()
        
        # 存储按钮引用
        action_buttons = {}
        
        for key, svg in button_icons.items():
            # 跳过添加active状态的图标
            if key == 'favorite_active' or key == 'pin_active':
                continue
                
            # 为带状态的按钮创建特殊按钮
            if key == 'favorite':
                is_favorited = hasattr(self.prompt, 'is_favorite') and self.prompt.is_favorite == 1
                btn = ButtonFactory.create_favorite_button(
                    button_icons['favorite'], 
                    button_icons['favorite_active'],
                    '收藏',
                    is_favorited
                )
            elif key == 'pin':
                is_pinned = hasattr(self.prompt, 'is_pinned') and self.prompt.is_pinned == 1
                btn = ButtonFactory.create_pin_button(
                    button_icons['pin'], 
                    button_icons['pin_active'],
                    '置顶',
                    is_pinned
                )
            else:
                # 其他按钮使用小图标按钮
                tooltips = {
                    'copy': '复制',
                    'edit': '编辑',
                    'history': '历史',
                    'delete': '删除'
                }
                btn = ButtonFactory.create_small_icon_button(svg, tooltips.get(key, key))
            
            # 存储按钮引用
            action_buttons[key] = btn
            layout.addWidget(btn)
        
        # 将按钮引用存储到卡片中
        self.card.action_buttons = action_buttons
        
        return widget
    
    def _create_title_section(self) -> QLabel:
        """创建标题区域 - 复制自原始create_title_section方法"""
        title_label = QLabel(self.prompt.title)
        title_label.setStyleSheet("""
            QLabel {
                color: #1F2937;
                font-size: 14px;
                font-weight: 600;
                background: transparent;
                border: none;
            }
        """)
        title_label.setWordWrap(True)
        title_label.setMaximumHeight(40)
        return title_label
    
    def _create_content_section(self) -> QWidget:
        """创建内容区域 - 复制自原始create_content_section方法"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        
        # 检查是否有媒体文件
        has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
        
        if has_media:
            # 状态B：带媒体布局
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            layout.setSpacing(8)
            
            # 左侧缩略图轮播
            from main import ThumbnailCarousel
            thumbnail_carousel = ThumbnailCarousel(self.prompt.media_files, self.card)
            layout.addWidget(thumbnail_carousel)
            
            # 右侧内容预览
            content_layout = QVBoxLayout()
            content_layout.setContentsMargins(0, 0, 0, 0)
            content_layout.setSpacing(4)
            
            # 添加"内容预览"标签
            preview_label = QLabel("内容预览")
            preview_label.setStyleSheet("""
                QLabel {
                    color: #4B5563;
                    font-size: 11px;
                    font-weight: bold;
                    background: transparent;
                    border: none;
                }
            """)
            content_layout.addWidget(preview_label)
            
            # 添加内容预览
            content_label = self._create_content_preview()
            content_layout.addWidget(content_label)
            
            layout.addLayout(content_layout, 1)
            
        else:
            # 状态A：纯文本布局
            layout = QVBoxLayout(widget)
            layout.setContentsMargins(0, 0, 0, 0)
            
            content_label = self._create_content_preview()
            layout.addWidget(content_label)
            
        return widget
    
    def _create_content_preview(self) -> QLabel:
        """创建内容预览标签"""
        content_text = self.prompt.content
        if len(content_text) > 120:
            content_text = content_text[:120] + "..."
        
        content_label = QLabel(content_text)
        content_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 12px;
                line-height: 1.4;
                background: transparent;
                border: none;
            }
        """)
        content_label.setWordWrap(True)
        content_label.setMaximumHeight(60)
        
        return content_label
    
    def _create_tags_section(self) -> QWidget:
        """创建标签区域 - 复制自原始create_tags_section方法"""
        widget = QWidget()
        StyleManager.apply_transparent_background(widget)
        
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(4)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        
        # 获取标签
        tags = []
        if hasattr(self.prompt, 'tags'):
            if self.prompt.tags is not None:
                if isinstance(self.prompt.tags, str):
                    tags = [tag.strip() for tag in self.prompt.tags.split(',') if tag.strip()]
                else:
                    tags = list(self.prompt.tags)
        
        # 最多显示5个标签
        tag_count = 0
        for tag in tags:
            if tag_count >= 5:
                break
                
            tag_label = self._create_tag_label(tag)
            layout.addWidget(tag_label)
            tag_count += 1
            
        # 如果有更多标签，显示+N标签
        if len(tags) > 5:
            more_label = self._create_more_tags_label(len(tags) - 5)
            layout.addWidget(more_label)
            
        layout.addStretch()
        return widget
    
    def _create_tag_label(self, tag_text: str) -> QLabel:
        """创建单个标签"""
        label = QLabel(tag_text)
        
        # 获取标签颜色
        if self.color_service:
            tag_color = self.color_service.get_tag_color(tag_text)
        else:
            tag_color = "#4B5563"  # 默认颜色
        
        label.setStyleSheet(f"""
            QLabel {{
                background-color: #F3F4F6;
                color: {tag_color};
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }}
        """)
        return label
    
    def _create_more_tags_label(self, count: int) -> QLabel:
        """创建显示更多标签的标签"""
        label = QLabel(f"+{count}")
        label.setStyleSheet("""
            QLabel {
                background-color: #F3F4F6;
                color: #9CA3AF;
                font-size: 11px;
                padding: 2px 6px;
                border-radius: 4px;
                border: none;
                font-weight: 500;
            }
        """)
        return label
    
    def _get_action_button_icons(self) -> Dict[str, str]:
        """获取操作按钮的SVG图标定义 - 复制自原始方法"""
        return {
            'favorite': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'favorite_active': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'pin_active': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="#3B82F6" xmlns="http://www.w3.org/2000/svg">
                <path d="M9.5 2.5L14.5 7.5L12 10L22 20L20 22L10 12L7.5 14.5L2.5 9.5L9.5 2.5Z" stroke="#2563EB" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'copy': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M5 15H4C3.46957 15 2.96086 14.7893 2.58579 14.4142C2.21071 14.0391 2 13.5304 2 13V4C2 3.46957 2.21071 2.96086 2.58579 2.58579C2.96086 2.21071 3.46957 2 4 2H13C13.5304 2 14.0391 2.21071 14.4142 2.58579C14.7893 2.96086 15 3.46957 15 4V5" stroke="currentColor" stroke-width="2"/>
            </svg>''',
            'edit': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'delete': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M3 6H5H21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M8 6V4C8 3.46957 8.21071 2.96086 8.58579 2.58579C8.96086 2.21071 9.46957 2 10 2H14C14.5304 2 15.0391 2.21071 15.4142 2.58579C15.7893 2.96086 16 3.46957 16 4V6M19 6V20C19 20.5304 18.7893 21.0391 18.4142 21.4142C18.0391 21.7893 17.5304 22 17 22H7C6.46957 22 5.96086 21.7893 5.58579 21.4142C5.21071 21.0391 5 20.5304 5 20V6H19Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>''',
            'history': '''<svg width="12" height="12" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V12L16 16" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
            </svg>'''
        }
    
    def _create_paint_event(self):
        """创建自定义绘制事件 - 复制自原始paintEvent方法"""
        def paint_event(event):
            painter = QPainter(self.card)
            painter.setRenderHint(QPainter.Antialiasing)
            
            # 检查置顶状态和媒体状态
            is_pinned = hasattr(self.prompt, 'is_pinned') and self.prompt.is_pinned == 1
            has_media = self.prompt.has_media if hasattr(self.prompt, 'has_media') else bool(self.prompt.media_files)
            
            # 设置画笔
            pen = QPen()
            if getattr(self.card, 'is_selected', False):
                pen.setColor(QColor("#3B82F6"))
                pen.setWidth(2)
            elif is_pinned:
                pen.setColor(QColor("#93C5FD"))
                pen.setWidth(2)
            elif has_media:
                pen.setColor(QColor("#C4B5FD"))
                pen.setWidth(2)
            elif getattr(self.card, 'hovered', False):
                pen.setColor(QColor("#9ca3af"))
                pen.setWidth(2)
            else:
                pen.setColor(QColor("#e0e0e0"))
                pen.setWidth(1)
            
            painter.setPen(pen)
            
            # 设置背景填充
            if is_pinned and not getattr(self.card, 'is_selected', False):
                painter.setBrush(QColor("#F0F7FF"))
            elif has_media and not getattr(self.card, 'is_selected', False) and not is_pinned:
                painter.setBrush(QColor("#F5F3FF"))
            else:
                painter.setBrush(Qt.NoBrush)
            
            # 绘制圆角矩形
            rect = self.card.rect().adjusted(1, 1, -1, -1)
            painter.drawRoundedRect(rect, 6, 6)
        
        return paint_event
    
    def _setup_mouse_events(self):
        """设置鼠标事件"""
        def enter_event(event):
            self.card.hovered = True
            self.card.update()
        
        def leave_event(event):
            self.card.hovered = False
            self.card.update()
        
        self.card.enterEvent = enter_event
        self.card.leaveEvent = leave_event 