#!/usr/bin/env python3
"""
分类服务模块
提供分类相关业务逻辑，将数据访问与UI分离
"""
from typing import List, Dict, Set, Optional
import time

from model import PromptModel, Prompt
from utils.error_handler import log_error, DatabaseError, DataError
from interfaces.service_interfaces import ICategoryService

class CategoryService(ICategoryService):
    """分类服务类，处理分类相关业务逻辑"""
    
    def __init__(self, model: PromptModel):
        """
        初始化分类服务
        :param model: 提示词数据模型
        """
        self.model = model
        self._cache = {
            'categories': [],
            'category_counts': {},
            'last_updated': 0
        }
        self._cache_ttl = 30  # 缓存有效期（秒）
    
    def get_all_categories(self, use_cache: bool = True) -> List[str]:
        """
        获取所有分类
        :param use_cache: 是否使用缓存
        :return: 分类列表
        """
        current_time = time.time()
        
        # 如果启用缓存且缓存未过期，则使用缓存
        if use_cache and self._cache['categories'] and \
           current_time - self._cache['last_updated'] < self._cache_ttl:
            return self._cache['categories']
        
        try:
            # 获取所有提示词
            prompts = self.model.get_all_prompts()
            
            # 收集所有分类
            categories = set()
            for prompt in prompts:
                if prompt.category and prompt.category.strip():
                    categories.add(prompt.category.strip())
            
            # 按字母顺序排序
            sorted_categories = sorted(list(categories))
            
            # 更新缓存
            self._cache['categories'] = sorted_categories
            self._cache['last_updated'] = current_time
            
            return sorted_categories
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取分类列表失败", str(e))
    
    def get_category_counts(self, use_cache: bool = True) -> Dict[str, int]:
        """
        获取分类及其数量
        :param use_cache: 是否使用缓存
        :return: 分类及数量字典，格式为 {category: count}
        """
        current_time = time.time()
        
        # 如果启用缓存且缓存未过期，则使用缓存
        if use_cache and self._cache['category_counts'] and \
           current_time - self._cache['last_updated'] < self._cache_ttl:
            return self._cache['category_counts']
        
        try:
            # 获取所有提示词
            prompts = self.model.get_all_prompts()
            
            # 计算分类数量
            category_counts = {}
            for prompt in prompts:
                category = prompt.category.strip() if prompt.category else ""
                if category:
                    if category in category_counts:
                        category_counts[category] += 1
                    else:
                        category_counts[category] = 1
            
            # 更新缓存
            self._cache['category_counts'] = category_counts
            self._cache['last_updated'] = current_time
            
            return category_counts
        except Exception as e:
            log_error(e)
            raise DatabaseError("获取分类数量失败", str(e))
    
    def get_prompts_by_category(self, category: str) -> List[Prompt]:
        """
        获取特定分类的提示词
        :param category: 分类名称，如果为"全部"则返回所有提示词
        :return: 属于该分类的提示词列表
        """
        try:
            if not category or category == "全部":
                return self.model.get_all_prompts()
            
            # 获取所有提示词并筛选
            prompts = self.model.get_all_prompts()
            return [p for p in prompts if p.category == category]
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"获取分类'{category}'的提示词失败", str(e))
    
    def rename_category(self, old_name: str, new_name: str) -> int:
        """
        重命名分类
        :param old_name: 旧分类名称
        :param new_name: 新分类名称
        :return: 更新的提示词数量
        """
        try:
            if not old_name or not new_name:
                raise DataError("分类名称不能为空")
            
            if old_name == new_name:
                return 0
            
            # 获取该分类的所有提示词
            prompts = self.get_prompts_by_category(old_name)
            
            # 更新分类名称
            count = 0
            for prompt in prompts:
                prompt.category = new_name
                self.model.update_prompt(prompt)
                count += 1
            
            # 清除缓存
            self.invalidate_cache()
            
            return count
        except DataError:
            raise
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"重命名分类'{old_name}'为'{new_name}'失败", str(e))
    
    def delete_category(self, category_name: str, move_to: str = "") -> int:
        """
        删除分类
        :param category_name: 要删除的分类名称
        :param move_to: 将该分类的提示词移动到其他分类，为空则不移动
        :return: 更新的提示词数量
        """
        try:
            if not category_name:
                raise DataError("分类名称不能为空")
            
            # 获取该分类的所有提示词
            prompts = self.get_prompts_by_category(category_name)
            
            # 更新提示词的分类
            count = 0
            for prompt in prompts:
                prompt.category = move_to
                self.model.update_prompt(prompt)
                count += 1
            
            # 清除缓存
            self.invalidate_cache()
            
            return count
        except DataError:
            raise
        except Exception as e:
            log_error(e)
            raise DatabaseError(f"删除分类'{category_name}'失败", str(e))
    
    def validate_category(self, category: str) -> bool:
        """
        验证分类名称是否有效
        :param category: 分类名称
        :return: 是否有效
        """
        # 分类不能为空
        if not category.strip():
            return False
        
        # 分类长度限制
        if len(category) > 50:
            return False
        
        # 可以添加其他验证规则
        
        return True
    
    def normalize_category(self, category: str) -> str:
        """
        标准化分类名称
        :param category: 原始分类名称
        :return: 标准化后的分类名称
        """
        return category.strip()
    
    def get_popular_categories(self, limit: int = 10) -> List[str]:
        """
        获取热门分类
        :param limit: 返回的分类数量限制
        :return: 热门分类列表
        """
        try:
            category_counts = self.get_category_counts()
            # 按使用次数排序，返回前limit个
            sorted_categories = sorted(category_counts.items(), key=lambda x: x[1], reverse=True)
            return [category for category, count in sorted_categories[:limit]]
        except Exception as e:
            log_error(e)
            return []
    
    def search_categories(self, keyword: str) -> List[str]:
        """
        搜索分类
        :param keyword: 搜索关键词
        :return: 匹配的分类列表
        """
        try:
            all_categories = self.get_all_categories()
            keyword_lower = keyword.lower()
            # 模糊匹配分类
            return [category for category in all_categories if keyword_lower in category.lower()]
        except Exception as e:
            log_error(e)
            return []
    
    def invalidate_cache(self):
        """清除缓存"""
        self._cache['categories'] = []
        self._cache['category_counts'] = {}
        self._cache['last_updated'] = 0 