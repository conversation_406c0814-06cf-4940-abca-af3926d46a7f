from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QIcon, QPainter, QColor, QPen, QAction
from PySide6.QtWidgets import QLineEdit

class SearchBox(QLineEdit):
    """
    自定义搜索框组件，具有搜索图标、清除按钮和延迟搜索功能
    """
    # 自定义信号，当搜索内容变化时发射
    searchTextChanged = Signal(str)
    
    def __init__(self, parent=None, delay=300):
        """
        初始化搜索框
        
        参数:
            parent: 父组件
            delay: 延迟搜索的时间（毫秒），设置为0表示实时搜索
        """
        super().__init__(parent)
        
        # 设置基本属性
        self.setPlaceholderText("搜索提示词...")
        self.setClearButtonEnabled(True)
        
        # 创建搜索图标 - 使用QIcon.fromTheme作为备选方案
        try:
            # 不使用图标，直接设置简洁样式
            if True:
                # 如果没有内置图标，则使用字体图标或简单样式
                self.setStyleSheet("""
                    QLineEdit {
                        padding: 8px 12px 8px 4px;
                        border: 1px solid #D1D5DB;
                        border-radius: 6px;
                        font-size: 14px;
                        background-color: white;
                        text-indent: 0px;
                    }
                    QLineEdit:focus {
                        border-color: #3B82F6;
                        outline: none;
                    }
                """)
            else:
                # 使用系统图标
                search_action = QAction(search_icon, "搜索", self)
                self.addAction(search_action, QLineEdit.LeadingPosition)
                self.setStyleSheet("""
                    QLineEdit {
                        padding: 8px 12px 8px 4px;
                        border: 1px solid #D1D5DB;
                        border-radius: 6px;
                        font-size: 14px;
                        background-color: white;
                        text-indent: 0px;
                    }
                    QLineEdit:focus {
                        border-color: #3B82F6;
                        outline: none;
                    }
                """)
        except Exception as e:
            # 出现异常时，使用没有图标的简单样式
            print(f"搜索图标加载失败: {e}")
            self.setStyleSheet("""
                QLineEdit {
                    padding: 8px 12px;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    font-size: 14px;
                    background-color: white;
                }
                QLineEdit:focus {
                    border-color: #3B82F6;
                    outline: none;
                }
            """)
        
        # 样式已在上面设置
        
        # 设置延迟搜索
        self.delay = delay
        self._timer = QTimer(self)
        self._timer.setSingleShot(True)
        self._timer.timeout.connect(self._emit_search_signal)
        
        # 连接信号和槽
        self.textChanged.connect(self._on_text_changed)
        
    def _on_text_changed(self, text):
        """
        当文本变化时，启动计时器
        """
        if self.delay > 0:
            self._timer.start(self.delay)
        else:
            self._emit_search_signal()
            
    def _emit_search_signal(self):
        """
        发射搜索信号
        """
        self.searchTextChanged.emit(self.text())
        
    def set_delay(self, delay):
        """
        设置延迟时间
        """
        self.delay = delay
        
    def get_search_svg(self):
        """
        获取搜索图标的SVG数据
        """
        # 直接返回SVG字符串内容而不是文件路径
        return """
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="#6B7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        """
        
    def paintEvent(self, event):
        """
        自定义绘制事件，添加搜索图标
        """
        super().paintEvent(event) 