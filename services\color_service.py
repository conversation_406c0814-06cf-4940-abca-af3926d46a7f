#!/usr/bin/env python3
"""
颜色管理服务模块
提供分类和标签的语义颜色分配功能
"""
import hashlib
from typing import Dict, List, Optional, Tuple
import json
from interfaces.service_interfaces import IColorService

class ColorService(IColorService):
    """颜色管理服务类，处理分类和标签的颜色分配"""
    
    def __init__(self):
        """初始化颜色服务"""
        # 分类颜色池（背景色）
        self.category_colors = {
            # 技术类
            "技术": "#E3F2FD",
            "编程": "#E3F2FD", 
            "开发": "#E3F2FD",
            "代码": "#E3F2FD",
            "软件": "#E3F2FD",
            "系统": "#E3F2FD",
            "网络": "#E3F2FD",
            "数据": "#E3F2FD",
            "算法": "#E3F2FD",
            "架构": "#E3F2FD",
            
            # 创意类
            "创意": "#F3E5F5",
            "设计": "#F3E5F5",
            "艺术": "#F3E5F5",
            "创作": "#F3E5F5",
            "灵感": "#F3E5F5",
            "美学": "#F3E5F5",
            "视觉": "#F3E5F5",
            "色彩": "#F3E5F5",
            "风格": "#F3E5F5",
            "创意": "#F3E5F5",
            
            # 工作类
            "工作": "#E8F5E8",
            "办公": "#E8F5E8",
            "商务": "#E8F5E8",
            "管理": "#E8F5E8",
            "项目": "#E8F5E8",
            "团队": "#E8F5E8",
            "协作": "#E8F5E8",
            "效率": "#E8F5E8",
            "流程": "#E8F5E8",
            "计划": "#E8F5E8",
            
            # 学习类
            "学习": "#FFF3E0",
            "教育": "#FFF3E0",
            "培训": "#FFF3E0",
            "知识": "#FFF3E0",
            "研究": "#FFF3E0",
            "课程": "#FFF3E0",
            "教程": "#FFF3E0",
            "指南": "#FFF3E0",
            "文档": "#FFF3E0",
            "资料": "#FFF3E0",
            
            # 生活类
            "生活": "#FCE4EC",
            "日常": "#FCE4EC",
            "娱乐": "#FCE4EC",
            "休闲": "#FCE4EC",
            "兴趣": "#FCE4EC",
            "爱好": "#FCE4EC",
            "美食": "#FCE4EC",
            "旅行": "#FCE4EC",
            "健康": "#FCE4EC",
            "运动": "#FCE4EC",
            
            # 其他类
            "其他": "#F1F3F4",
            "杂项": "#F1F3F4",
            "未分类": "#F1F3F4",
        }
        
        # 标签颜色池（字体色）
        self.tag_colors = {
            # AI相关
            "AI": "#1976D2",
            "人工智能": "#1976D2",
            "机器学习": "#1976D2",
            "深度学习": "#1976D2",
            "神经网络": "#1976D2",
            "GPT": "#1976D2",
            "ChatGPT": "#1976D2",
            "Claude": "#1976D2",
            "模型": "#1976D2",
            "算法": "#1976D2",
            
            # 创意相关
            "创意": "#7B1FA2",
            "设计": "#7B1FA2",
            "艺术": "#7B1FA2",
            "灵感": "#7B1FA2",
            "美学": "#7B1FA2",
            "视觉": "#7B1FA2",
            "色彩": "#7B1FA2",
            "风格": "#7B1FA2",
            "创作": "#7B1FA2",
            "表达": "#7B1FA2",
            
            # 技术相关
            "技术": "#388E3C",
            "编程": "#388E3C",
            "开发": "#388E3C",
            "代码": "#388E3C",
            "软件": "#388E3C",
            "系统": "#388E3C",
            "架构": "#388E3C",
            "框架": "#388E3C",
            "库": "#388E3C",
            "API": "#388E3C",
            
            # 工具相关
            "工具": "#F57C00",
            "软件": "#F57C00",
            "应用": "#F57C00",
            "平台": "#F57C00",
            "服务": "#F57C00",
            "插件": "#F57C00",
            "扩展": "#F57C00",
            "集成": "#F57C00",
            "自动化": "#F57C00",
            "工作流": "#F57C00",
            
            # 效率相关
            "效率": "#D32F2F",
            "优化": "#D32F2F",
            "提升": "#D32F2F",
            "改进": "#D32F2F",
            "加速": "#D32F2F",
            "简化": "#D32F2F",
            "自动化": "#D32F2F",
            "流程": "#D32F2F",
            "管理": "#D32F2F",
            "组织": "#D32F2F",
            
            # 其他
            "其他": "#424242",
            "杂项": "#424242",
            "未分类": "#424242",
        }
        
        # 已分配的颜色记录
        self.allocated_category_colors = {}
        self.allocated_tag_colors = {}
        
        # 备用颜色池（当语义颜色不够时使用）
        self.fallback_category_colors = [
            "#E1F5FE", "#F3E5F5", "#E8F5E8", "#FFF3E0", "#FCE4EC",
            "#E0F2F1", "#F1F8E9", "#FFF8E1", "#FCE4EC", "#F3E5F5",
            "#E8EAF6", "#F3E5F5", "#E0F2F1", "#FFF3E0", "#FCE4EC"
        ]
        
        self.fallback_tag_colors = [
            "#1976D2", "#7B1FA2", "#388E3C", "#F57C00", "#D32F2F",
            "#0277BD", "#6A1B9A", "#2E7D32", "#E65100", "#C62828",
            "#1565C0", "#4A148C", "#1B5E20", "#BF360C", "#B71C1C"
        ]
    
    def get_category_color(self, category_name: str) -> str:
        """
        获取分类颜色（背景色）
        :param category_name: 分类名称
        :return: 颜色代码
        """
        if not category_name:
            return self.fallback_category_colors[0]
        
        # 检查是否已分配
        if category_name in self.allocated_category_colors:
            return self.allocated_category_colors[category_name]
        
        # 尝试语义匹配
        for keyword, color in self.category_colors.items():
            if keyword in category_name:
                # 检查颜色是否已被使用
                if color not in self.allocated_category_colors.values():
                    self.allocated_category_colors[category_name] = color
                    return color
        
        # 使用哈希分配备用颜色
        hash_value = hash(category_name.lower())
        color_index = hash_value % len(self.fallback_category_colors)
        
        # 找到未使用的颜色
        for i in range(len(self.fallback_category_colors)):
            color = self.fallback_category_colors[(color_index + i) % len(self.fallback_category_colors)]
            if color not in self.allocated_category_colors.values():
                self.allocated_category_colors[category_name] = color
                return color
        
        # 如果所有颜色都被使用，使用第一个
        color = self.fallback_category_colors[0]
        self.allocated_category_colors[category_name] = color
        return color
    
    def get_tag_color(self, tag_name: str) -> str:
        """
        获取标签颜色（字体色）
        :param tag_name: 标签名称
        :return: 颜色代码
        """
        if not tag_name:
            return self.fallback_tag_colors[0]
        
        # 检查是否已分配
        if tag_name in self.allocated_tag_colors:
            return self.allocated_tag_colors[tag_name]
        
        # 尝试语义匹配
        for keyword, color in self.tag_colors.items():
            if keyword in tag_name:
                # 检查颜色是否已被使用
                if color not in self.allocated_tag_colors.values():
                    self.allocated_tag_colors[tag_name] = color
                    return color
        
        # 使用哈希分配备用颜色
        hash_value = hash(tag_name.lower())
        color_index = hash_value % len(self.fallback_tag_colors)
        
        # 找到未使用的颜色
        for i in range(len(self.fallback_tag_colors)):
            color = self.fallback_tag_colors[(color_index + i) % len(self.fallback_tag_colors)]
            if color not in self.allocated_tag_colors.values():
                self.allocated_tag_colors[tag_name] = color
                return color
        
        # 如果所有颜色都被使用，使用第一个
        color = self.fallback_tag_colors[0]
        self.allocated_tag_colors[tag_name] = color
        return color
    
    def get_all_allocated_colors(self) -> Tuple[Dict[str, str], Dict[str, str]]:
        """
        获取所有已分配的颜色
        :return: (分类颜色字典, 标签颜色字典)
        """
        return self.allocated_category_colors.copy(), self.allocated_tag_colors.copy()
    
    def clear_allocations(self):
        """清除所有颜色分配"""
        self.allocated_category_colors.clear()
        self.allocated_tag_colors.clear()
    
    def load_allocations(self, category_colors: Dict[str, str], tag_colors: Dict[str, str]):
        """
        加载已分配的颜色
        :param category_colors: 分类颜色字典
        :param tag_colors: 标签颜色字典
        """
        self.allocated_category_colors = category_colors.copy()
        self.allocated_tag_colors = tag_colors.copy() 