import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import PromptModel
from create_prompt_dialog import CreatePromptDialog
from PySide6.QtWidgets import QApplication

def test_edit_prompt():
    app = QApplication(sys.argv)
    
    model = PromptModel()
    
    # 获取ID为21的提示词
    prompt = model.get_prompt(21)
    
    if prompt:
        print(f"提示词ID: {prompt.id}")
        print(f"标题: {prompt.title}")
        print(f"auxiliary_fields: {prompt.auxiliary_fields}")
        
        # 创建一个模拟的父窗口，包含model属性
        from PySide6.QtWidgets import QWidget
        
        class MockParent(QWidget):
            def __init__(self, model):
                super().__init__()
                self.model = model
        
        mock_parent = MockParent(model)
        
        # 创建编辑对话框
        dialog = CreatePromptDialog(parent=mock_parent, edit_prompt=prompt)
        dialog.show()
        
        # 运行对话框
        result = dialog.exec()
        
        if result == dialog.accepted:
            print("编辑成功")
        else:
            print("编辑取消")
    else:
        print("未找到提示词")

if __name__ == "__main__":
    test_edit_prompt() 