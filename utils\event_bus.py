#!/usr/bin/env python3
"""
事件总线模块
提供组件间的通信机制，减少组件间的直接依赖
"""
from typing import Dict, List, Any, Callable, Set, Optional
from PySide6.QtCore import QObject, Signal, Slot
from enum import Enum, auto


class EventType(Enum):
    """事件类型枚举"""
    # 应用程序事件
    APP_INIT = auto()              # 应用程序初始化
    APP_EXIT = auto()              # 应用程序退出
    
    # 提示词相关事件
    PROMPT_CREATED = auto()        # 创建提示词
    PROMPT_UPDATED = auto()        # 更新提示词
    PROMPT_DELETED = auto()        # 删除提示词
    PROMPT_SELECTED = auto()       # 选择提示词
    PROMPT_DESELECTED = auto()     # 取消选择提示词
    PROMPT_COPIED = auto()         # 复制提示词
    PROMPT_FAVORITED = auto()      # 收藏提示词
    PROMPT_UNFAVORITED = auto()    # 取消收藏提示词
    PROMPT_PINNED = auto()         # 置顶提示词
    PROMPT_UNPINNED = auto()       # 取消置顶提示词
    PROMPTS_LOADED = auto()        # 提示词加载完成
    
    # 筛选器相关事件
    FILTER_CHANGED = auto()        # 筛选条件改变
    CATEGORY_FILTER_CHANGED = auto() # 分类筛选改变
    TAG_FILTER_CHANGED = auto()    # 标签筛选改变
    FAVORITE_FILTER_CHANGED = auto() # 收藏筛选改变
    SEARCH_FILTER_CHANGED = auto() # 搜索筛选改变
    
    # 视图相关事件
    VIEW_CHANGED = auto()          # 视图类型改变
    PAGE_CHANGED = auto()          # 页面切换
    
    # 对话框相关事件
    DIALOG_OPENED = auto()         # 对话框打开
    DIALOG_CLOSED = auto()         # 对话框关闭


class Event:
    """事件类，包含事件类型和数据"""
    def __init__(self, event_type: EventType, data: Any = None):
        self.type = event_type
        self.data = data
        
    def __str__(self):
        return f"Event(type={self.type.name}, data={self.data})"


class QtEventBus(QObject):
    """基于Qt信号槽机制的事件总线"""
    
    # 定义事件信号
    event_occurred = Signal(Event)
    
    def __init__(self):
        super().__init__()
        self._subscribers: Dict[EventType, List[Callable]] = {}
        
    def subscribe(self, event_type: EventType, callback: Callable):
        """订阅事件"""
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        
        if callback not in self._subscribers[event_type]:
            self._subscribers[event_type].append(callback)
            
        # 连接Qt信号
        self.event_occurred.connect(lambda event: self._handle_event(event, callback, event_type))
        
    def unsubscribe(self, event_type: EventType, callback: Callable):
        """取消订阅事件"""
        if event_type in self._subscribers and callback in self._subscribers[event_type]:
            self._subscribers[event_type].remove(callback)
            
            # 断开Qt信号连接
            # 注意：这种方式可能会断开所有连接，实际使用中可能需要更精细的控制
            try:
                self.event_occurred.disconnect(lambda event: self._handle_event(event, callback, event_type))
            except:
                pass  # 忽略断开连接失败的情况
            
    def publish(self, event: Event):
        """发布事件"""
        # 发送Qt信号
        self.event_occurred.emit(event)
        
    def _handle_event(self, event: Event, callback: Callable, subscribed_type: EventType):
        """处理事件，只处理匹配的事件类型"""
        if event.type == subscribed_type:
            callback(event)


# 全局事件总线实例
event_bus = QtEventBus()


# 装饰器，用于简化事件订阅
def subscribe(event_type: EventType):
    """事件订阅装饰器"""
    def decorator(func):
        event_bus.subscribe(event_type, func)
        return func
    return decorator


# 辅助函数
def publish_event(event_type: EventType, data: Any = None):
    """发布事件的辅助函数"""
    event = Event(event_type, data)
    event_bus.publish(event) 