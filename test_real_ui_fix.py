#!/usr/bin/env python3
"""
验证修复后的实际UI事件连接
"""

from PySide6.QtWidgets import QApplication
import sys
from model import PromptModel, Prompt
from main import PromptCardWidget

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🔍 测试修复后的UI事件连接...")

# 获取一个测试提示词
model = PromptModel()
prompts = model.get_all_prompts()
if not prompts:
    print("❌ 没有找到测试数据")
    exit(1)

test_prompt = prompts[0]
print(f"📝 测试提示词: ID={test_prompt.id}, 收藏状态={test_prompt.is_favorite}")

# 创建PromptCardWidget（会使用PromptCardBuilder）
print("🏗️ 创建PromptCardWidget...")
card = PromptCardWidget(test_prompt)
print("✅ PromptCardWidget创建成功")

# 检查按钮和事件连接
if hasattr(card, 'action_buttons') and 'favorite' in card.action_buttons:
    btn = card.action_buttons['favorite']
    print(f"🔘 收藏按钮属性:")
    print(f"   is_favorited: {getattr(btn, 'is_favorited', '未设置')}")
    
    # 记录点击前状态
    original_state = test_prompt.is_favorite
    print(f"   点击前数据库状态: {original_state}")
    
    # 模拟点击
    print("🖱️ 模拟点击收藏按钮...")
    btn.clicked.emit()
    
    # 检查点击后状态
    updated_prompt = model.get_prompt(test_prompt.id)
    print(f"   点击后数据库状态: {updated_prompt.is_favorite}")
    
    if updated_prompt.is_favorite != original_state:
        print("✅ 修复成功！数据库状态已正确更新")
        # 恢复原始状态
        model.toggle_favorite(test_prompt.id)
        print("🔄 已恢复原始状态")
    else:
        print("❌ 修复失败，数据库状态未改变")
        
else:
    print("❌ 未找到收藏按钮！")

print("🏁 测试完成") 