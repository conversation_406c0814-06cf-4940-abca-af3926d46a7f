#!/usr/bin/env python3
"""
调试has_media判断失败的问题
"""
import sys
from PySide6.QtWidgets import QApplication
from model import PromptModel
from pathlib import Path

def debug_has_media():
    """调试has_media判断逻辑"""
    print("🔍 调试has_media判断逻辑...")
    print("=" * 50)
    
    # 创建应用程序
    app = QApplication.instance() or QApplication(sys.argv)
    
    # 获取数据
    model = PromptModel("sqlite", "prompts.db")
    prompts = model.get_all_prompts()
    
    print(f"📊 总共找到 {len(prompts)} 个提示词")
    
    # 检查每个prompt的媒体文件情况
    for i, prompt in enumerate(prompts[:10]):  # 只检查前10个
        print(f"\n📝 Prompt {i+1}: {prompt.title}")
        print(f"   ID: {prompt.id}")
        
        # 检查has_media属性
        has_has_media = hasattr(prompt, 'has_media')
        print(f"   hasattr(prompt, 'has_media'): {has_has_media}")
        
        if has_has_media:
            print(f"   prompt.has_media: {prompt.has_media}")
        
        # 检查media_files
        print(f"   prompt.media_files: {prompt.media_files}")
        print(f"   type(prompt.media_files): {type(prompt.media_files)}")
        print(f"   bool(prompt.media_files): {bool(prompt.media_files)}")
        
        # 计算has_media的值
        has_media = prompt.has_media if hasattr(prompt, 'has_media') else bool(prompt.media_files)
        print(f"   🎯 最终has_media: {has_media}")
        
        # 如果有媒体文件，检查文件是否存在
        if prompt.media_files:
            print("   📁 媒体文件详情:")
            if isinstance(prompt.media_files, list):
                for j, media_file in enumerate(prompt.media_files):
                    exists = Path(media_file).exists()
                    ext = Path(media_file).suffix.lower()
                    is_image = ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
                    print(f"      {j+1}. {media_file}")
                    print(f"         存在: {exists}, 扩展名: {ext}, 是图片: {is_image}")
            else:
                print(f"      非列表类型: {prompt.media_files}")
    
    # 特别检查测试图片提示词
    print("\n" + "="*50)
    print("🎯 特别检查测试图片提示词...")
    
    test_prompt = None
    for prompt in prompts:
        if prompt.title == "测试图片提示词":
            test_prompt = prompt
            break
    
    if test_prompt:
        print(f"✅ 找到测试图片提示词")
        print(f"   prompt.media_files = {test_prompt.media_files}")
        print(f"   type = {type(test_prompt.media_files)}")
        print(f"   len = {len(test_prompt.media_files) if test_prompt.media_files else 'N/A'}")
        print(f"   bool = {bool(test_prompt.media_files)}")
        
        has_media_check = test_prompt.has_media if hasattr(test_prompt, 'has_media') else bool(test_prompt.media_files)
        print(f"   🎯 has_media计算结果: {has_media_check}")
        
        if not has_media_check:
            print("❌ has_media为False，这就是为什么ThumbnailCarousel没有创建的原因！")
        else:
            print("✅ has_media为True，应该能创建ThumbnailCarousel")
    else:
        print("❌ 未找到测试图片提示词")

if __name__ == "__main__":
    debug_has_media() 