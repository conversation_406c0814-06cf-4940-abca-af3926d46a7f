#!/usr/bin/env python3
"""
测试PromptService功能
"""
import sys
import os
import unittest
import sqlite3
from pathlib import Path

# 将项目根目录添加到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model import PromptModel, Prompt, SQLiteStorage
from services.prompt_service import PromptService
from utils.error_handler import DataError

class TestPromptService(unittest.TestCase):
    """测试PromptService类"""
    
    def setUp(self):
        """测试前设置"""
        # 使用内存中的SQLite数据库
        self.db_path = ":memory:"
        
        # 直接创建存储后端，确保数据库表已初始化
        self.storage = SQLiteStorage(self.db_path)
        
        # 然后创建模型和服务
        self.model = PromptModel("sqlite", self.db_path)
        self.model.storage = self.storage  # 确保使用同一个存储实例
        self.service = PromptService(self.model)
        
        # 创建一些测试数据
        self.test_prompt1 = Prompt(
            title="测试提示词1",
            content="这是测试内容1",
            tags=["测试", "示例"],
            category="测试分类"
        )
        self.test_prompt2 = Prompt(
            title="测试提示词2",
            content="这是测试内容2",
            tags=["测试", "重要"],
            category="测试分类"
        )
        
        # 添加测试数据到数据库
        self.prompt1_id = self.model.add_prompt(self.test_prompt1)
        self.prompt2_id = self.model.add_prompt(self.test_prompt2)
    
    def test_get_all_prompts(self):
        """测试获取所有提示词"""
        prompts = self.service.get_all_prompts(use_cache=False)
        self.assertEqual(len(prompts), 2)
        self.assertEqual(prompts[0].title, "测试提示词1")
        self.assertEqual(prompts[1].title, "测试提示词2")
    
    def test_get_prompt(self):
        """测试获取特定提示词"""
        prompt = self.service.get_prompt(self.prompt1_id)
        self.assertIsNotNone(prompt)
        self.assertEqual(prompt.title, "测试提示词1")
        self.assertEqual(prompt.content, "这是测试内容1")
        self.assertEqual(prompt.category, "测试分类")
        self.assertEqual(len(prompt.tags), 2)
        self.assertIn("测试", prompt.tags)
        self.assertIn("示例", prompt.tags)
    
    def test_create_prompt(self):
        """测试创建提示词"""
        new_prompt = Prompt(
            title="新提示词",
            content="新提示词内容",
            tags=["新", "测试"],
            category="新分类"
        )
        
        prompt_id = self.service.create_prompt(new_prompt)
        self.assertIsNotNone(prompt_id)
        
        # 验证新创建的提示词
        saved_prompt = self.service.get_prompt(prompt_id)
        self.assertEqual(saved_prompt.title, "新提示词")
        self.assertEqual(saved_prompt.content, "新提示词内容")
        self.assertIn("新", saved_prompt.tags)
    
    def test_create_prompt_validation(self):
        """测试创建提示词的数据验证"""
        # 尝试创建标题为空的提示词
        empty_title_prompt = Prompt(
            title="",
            content="内容",
            tags=["测试"],
            category="分类"
        )
        
        # 应该引发DataError
        with self.assertRaises(DataError):
            self.service.create_prompt(empty_title_prompt)
    
    def test_update_prompt(self):
        """测试更新提示词"""
        # 获取现有提示词
        prompt = self.service.get_prompt(self.prompt1_id)
        
        # 修改属性
        prompt.title = "已更新的标题"
        prompt.content = "已更新的内容"
        prompt.tags.append("已更新")
        
        # 保存更新
        result = self.service.update_prompt(prompt)
        self.assertTrue(result)
        
        # 验证更新
        updated_prompt = self.service.get_prompt(self.prompt1_id)
        self.assertEqual(updated_prompt.title, "已更新的标题")
        self.assertEqual(updated_prompt.content, "已更新的内容")
        self.assertIn("已更新", updated_prompt.tags)
    
    def test_delete_prompt(self):
        """测试删除提示词"""
        # 确认提示词存在
        self.assertIsNotNone(self.service.get_prompt(self.prompt2_id))
        
        # 删除提示词
        result = self.service.delete_prompt(self.prompt2_id)
        self.assertTrue(result)
        
        # 验证提示词已删除
        prompts = self.service.get_all_prompts(use_cache=False)
        self.assertEqual(len(prompts), 1)
        self.assertEqual(prompts[0].id, self.prompt1_id)
    
    def test_filter_prompts(self):
        """测试筛选提示词"""
        # 添加更多测试数据
        self.model.add_prompt(Prompt(
            title="收藏提示词",
            content="这个应该被收藏",
            tags=["收藏", "示例"],
            category="收藏分类",
            is_favorite=1
        ))
        
        # 按标签筛选
        filters = {'tags': {"示例"}}
        filtered_prompts = self.service.filter_prompts(filters)
        self.assertEqual(len(filtered_prompts), 2)  # 应该有2个带"示例"标签的提示词
        
        # 按分类筛选
        filters = {'category': "收藏分类"}
        filtered_prompts = self.service.filter_prompts(filters)
        self.assertEqual(len(filtered_prompts), 1)
        self.assertEqual(filtered_prompts[0].category, "收藏分类")
        
        # 按收藏状态筛选
        filters = {'is_favorite': True}
        filtered_prompts = self.service.filter_prompts(filters)
        self.assertEqual(len(filtered_prompts), 1)
        self.assertEqual(filtered_prompts[0].is_favorite, 1)
        
        # 按搜索文本筛选
        filters = {'search_text': "收藏"}
        filtered_prompts = self.service.filter_prompts(filters)
        self.assertEqual(len(filtered_prompts), 1)
        self.assertEqual(filtered_prompts[0].title, "收藏提示词")
    
    def test_sort_prompts(self):
        """测试排序提示词"""
        # 确保有足够的测试数据
        self.model.add_prompt(Prompt(
            title="A提示词",
            content="按字母排序应该在前面",
            category="A分类",
            is_pinned=1  # 置顶
        ))
        
        prompts = self.service.get_all_prompts(use_cache=False)
        
        # 按标题升序排序
        sorted_prompts = self.service.sort_prompts(prompts, "title_asc")
        self.assertEqual(sorted_prompts[0].title, "A提示词")  # 置顶的排在最前面
        
        # 按分类升序排序
        sorted_prompts = self.service.sort_prompts(prompts, "category_asc")
        # 置顶的排在最前面，然后是按分类排序
        self.assertEqual(sorted_prompts[0].title, "A提示词")
        
        # 确认置顶逻辑
        self.assertEqual(sorted_prompts[0].is_pinned, 1)


if __name__ == "__main__":
    unittest.main() 