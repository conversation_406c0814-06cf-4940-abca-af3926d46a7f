#!/usr/bin/env python3
"""
全面的历史记录测试，模拟真实用户操作流程
"""

from model import PromptModel, Prompt
from prompt_history_dialog import PromptHistoryDialog
from PySide6.QtWidgets import QApplication
from datetime import datetime
import sys

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🧪 全面历史记录测试（模拟真实用户操作）")
print("=" * 60)

# 创建模型
model = PromptModel()

# 模拟用户操作：创建提示词
print("👤 用户操作1：创建提示词（内容为'版本1'）")
prompt = Prompt(
    title="测试历史功能",
    content="版本1",
    category="测试",
    tags=["历史测试"],
    created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
    updated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
)

prompt_id = model.add_prompt(prompt)
print(f"✅ 创建成功，ID={prompt_id}")

# 模拟用户操作：点击历史记录按钮
print("\n👤 用户操作2：点击历史记录按钮")
print("🔍 检查数据库中的历史记录...")
history = model.get_prompt_history(prompt_id)
print(f"数据库历史记录数量: {len(history)}")
for h in history:
    print(f"  版本{h.version}: '{h.content}' (时间: {h.created_at})")

# 检查当前提示词内容
current_prompt = model.get_prompt(prompt_id)
print(f"当前提示词内容: '{current_prompt.content}'")

# 问题检查1：历史记录是否包含当前版本
current_in_history = any(h.content == current_prompt.content for h in history)
print(f"❓ 问题检查1：历史记录是否包含当前版本？ {current_in_history}")

print("\n" + "-" * 50)

# 模拟用户操作：编辑提示词
print("👤 用户操作3：编辑提示词（内容改为'版本2'）")
current_prompt.content = "版本2"
current_prompt.updated_at = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# 重要：查看编辑前的历史记录状态
print("🔍 编辑前的历史记录状态:")
before_edit_history = model.get_prompt_history(prompt_id)
for h in before_edit_history:
    print(f"  版本{h.version}: '{h.content}'")

# 执行更新
model.update_prompt(current_prompt)
print("✅ 编辑保存完成")

# 模拟用户操作：再次点击历史记录按钮
print("\n👤 用户操作4：再次点击历史记录按钮")
print("🔍 检查编辑后的历史记录...")
history_after = model.get_prompt_history(prompt_id)
print(f"数据库历史记录数量: {len(history_after)}")
for h in history_after:
    print(f"  版本{h.version}: '{h.content}' (时间: {h.created_at})")

# 检查当前提示词内容
current_prompt_after = model.get_prompt(prompt_id)
print(f"当前提示词内容: '{current_prompt_after.content}'")

# 问题检查2
print("\n🔍 关键问题分析:")
print(f"1. 历史记录中有'版本1'？ {any(h.content == '版本1' for h in history_after)}")
print(f"2. 历史记录中有'版本2'？ {any(h.content == '版本2' for h in history_after)}")
print(f"3. 当前内容是'版本2'？ {current_prompt_after.content == '版本2'}")

current_in_history_after = any(h.content == current_prompt_after.content for h in history_after)
print(f"4. 当前版本在历史记录中？ {current_in_history_after}")

if not current_in_history_after:
    print("🐛 问题确认：当前版本不在历史记录中！")
    print("   这就是用户报告的bug")
else:
    print("✅ 历史记录包含当前版本")

# 额外测试：模拟历史记录对话框的行为
print("\n🖥️ 模拟历史记录对话框显示逻辑:")

class MockParent:
    def __init__(self, model):
        self.model = model

try:
    mock_parent = MockParent(model)
    # 这里不实际创建对话框UI，只测试数据加载逻辑
    dialog_history = model.get_prompt_history(prompt_id)
    print(f"对话框获取到的历史记录数量: {len(dialog_history)}")
    
    if not dialog_history:
        print("❌ 对话框显示：没有历史版本记录")
    else:
        print("📋 对话框显示的历史版本:")
        for h in dialog_history:
            print(f"  版本 {h.version} - {h.created_at}")
            
except Exception as e:
    print(f"❌ 模拟对话框出错: {e}")

# 清理测试数据
print(f"\n🧹 清理测试数据...")
model.permanently_delete_prompt(prompt_id)
print("✅ 清理完成")

print("\n" + "=" * 60)
print("🏁 测试完成")

# 总结问题
print("\n📋 问题总结:")
print("如果测试显示历史记录功能正常，问题可能在于：")
print("1. 保存历史记录的时机不对")
print("2. 历史记录对话框的显示逻辑有问题") 
print("3. 界面更新和数据库操作的同步问题") 