#!/usr/bin/env python3
"""
数据模型文件，用于定义提示词数据结构和数据库操作
"""

import sqlite3
import json
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
from datetime import datetime

@dataclass
class Prompt:
    """
    提示词数据模型
    """
    id: Optional[int] = None
    title: str = ""
    content: str = ""
    tags: List[str] = None
    category: str = ""
    media_files: List[str] = None  # 用于存储图片和视频文件路径
    type: str = "文本"  # 提示词类型：文本、图片或视频
    auxiliary_fields: Dict[str, str] = None  # 辅助选择字段内容
    is_favorite: int = 0  # 收藏状态：0表示未收藏，1表示已收藏
    is_pinned: int = 0  # 置顶状态：0表示未置顶，1表示已置顶
    is_deleted: int = 0  # 删除状态：0表示正常，1表示已删除（在回收站）
    deleted_at: Optional[str] = None  # 删除时间
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    
    def __post_init__(self):
        # 初始化标签列表
        if self.tags is None:
            self.tags = []
        elif isinstance(self.tags, str):
            # 如果标签是字符串，尝试解析为列表
            try:
                self.tags = json.loads(self.tags)
            except json.JSONDecodeError:
                # 如果解析失败，按逗号分割
                self.tags = [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        
        # 确保标签是列表类型
        if not isinstance(self.tags, list):
            self.tags = []
            
        # 初始化媒体文件列表
        if self.media_files is None:
            self.media_files = []
            
        # 初始化辅助选择字段
        if self.auxiliary_fields is None:
            self.auxiliary_fields = {}
            
        # 根据媒体文件类型自动确定提示词类型
        if self.media_files and self.type == "文本":
            # 获取第一个媒体文件作为判断依据
            first_media = self.media_files[0]
            if first_media:
                from pathlib import Path
                file_path = Path(first_media)
                # 判断文件类型
                if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                    self.type = "图片"
                elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                    self.type = "视频"
            
        # 时间戳处理
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
        if self.updated_at is None:
            self.updated_at = datetime.now().isoformat()
    
    @property
    def has_media(self) -> bool:
        """判断提示词是否包含媒体文件"""
        return bool(self.media_files and len(self.media_files) > 0)
    
    def to_dict(self) -> Dict:
        """将Prompt对象转换为字典"""
        data = asdict(self)
        # 确保标签是JSON可序列化的列表
        if isinstance(data['tags'], list):
            pass  # 已经是列表，无需处理
        elif isinstance(data['tags'], str):
            try:
                data['tags'] = json.loads(data['tags'])
            except json.JSONDecodeError:
                data['tags'] = [tag.strip() for tag in data['tags'].split(',') if tag.strip()]
        else:
            data['tags'] = []
            
        return data
        
    @classmethod
    def from_dict(cls, data: Dict) -> 'Prompt':
        """从字典创建Prompt对象"""
        # 处理tags字段，确保它是一个列表
        if isinstance(data.get('tags'), str):
            try:
                data['tags'] = json.loads(data['tags'])
            except json.JSONDecodeError:
                data['tags'] = [tag.strip() for tag in data['tags'].split(',') if tag.strip()]
        elif data.get('tags') is None:
            data['tags'] = []
            
        # 确保标签是列表类型
        if not isinstance(data.get('tags'), list):
            data['tags'] = []
            
        # 处理media_files字段
        if isinstance(data.get('media_files'), str):
            try:
                data['media_files'] = json.loads(data['media_files'])
            except json.JSONDecodeError:
                data['media_files'] = []
        elif data.get('media_files') is None:
            data['media_files'] = []
            
        # 处理auxiliary_fields字段
        if isinstance(data.get('auxiliary_fields'), str):
            try:
                data['auxiliary_fields'] = json.loads(data['auxiliary_fields'])
            except json.JSONDecodeError:
                data['auxiliary_fields'] = {}
        elif data.get('auxiliary_fields') is None:
            data['auxiliary_fields'] = {}
        
        return cls(**data)

@dataclass
class PromptHistory:
    """
    提示词历史版本数据模型
    """
    id: Optional[int] = None
    prompt_id: int = 0
    title: str = ""
    content: str = ""
    tags: List[str] = None
    category: str = ""
    media_files: List[str] = None
    type: str = "文本"  # 提示词类型：文本、图片或视频
    auxiliary_fields: Dict[str, str] = None  # 辅助选择字段内容
    version: int = 0
    created_at: Optional[str] = None
    
    def __post_init__(self):
        # 初始化标签列表
        if self.tags is None:
            self.tags = []
        elif isinstance(self.tags, str):
            # 如果标签是字符串，尝试解析为列表
            try:
                self.tags = json.loads(self.tags)
            except json.JSONDecodeError:
                # 如果解析失败，按逗号分割
                self.tags = [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        
        # 确保标签是列表类型
        if not isinstance(self.tags, list):
            self.tags = []
            
        # 初始化媒体文件列表
        if self.media_files is None:
            self.media_files = []
        elif isinstance(self.media_files, str):
            # 如果媒体文件是字符串，尝试解析为列表
            try:
                self.media_files = json.loads(self.media_files)
            except json.JSONDecodeError:
                # 如果解析失败，设为空列表
                self.media_files = []
                
        # 确保媒体文件是列表类型
        if not isinstance(self.media_files, list):
            self.media_files = []
            
        # 初始化辅助选择字段
        if self.auxiliary_fields is None:
            self.auxiliary_fields = {}
            
        # 时间戳处理
        if self.created_at is None:
            self.created_at = datetime.now().isoformat()
    
    def to_prompt(self) -> Prompt:
        """将历史版本转换为Prompt对象"""
        # 确定提示词类型
        prompt_type = "文本"
        if hasattr(self, 'type') and self.type:
            prompt_type = self.type
        elif self.media_files and isinstance(self.media_files, list) and self.media_files:
            # 根据第一个媒体文件的类型判断
            from pathlib import Path
            file_path = Path(self.media_files[0])
            if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                prompt_type = "图片"
            elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                prompt_type = "视频"
        
        return Prompt(
            id=self.prompt_id,
            title=self.title,
            content=self.content,
            tags=self.tags,
            category=self.category,
            media_files=self.media_files,
            auxiliary_fields=self.auxiliary_fields,
            type=prompt_type,
            created_at=self.created_at
        )
    
    @classmethod
    def from_prompt(cls, prompt: Prompt, version: int = 1) -> 'PromptHistory':
        """从Prompt对象创建历史版本对象"""
        # 确保type字段存在
        prompt_type = prompt.type if hasattr(prompt, 'type') and prompt.type else "文本"
        
        return cls(
            prompt_id=prompt.id,
            title=prompt.title,
            content=prompt.content,
            tags=prompt.tags,
            category=prompt.category,
            media_files=prompt.media_files,
            auxiliary_fields=prompt.auxiliary_fields,
            type=prompt_type,
            version=version,
            created_at=datetime.now().isoformat()
        )

class StorageBackend:
    """存储后端基类"""
    
    def add_prompt(self, prompt: Prompt) -> int:
        raise NotImplementedError
    
    def update_prompt(self, prompt: Prompt):
        raise NotImplementedError
    
    def delete_prompt(self, prompt_id: int):
        raise NotImplementedError
    
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        raise NotImplementedError
    
    def get_all_prompts(self) -> List[Prompt]:
        raise NotImplementedError
    
    def search_prompts(self, keyword: str) -> List[Prompt]:
        raise NotImplementedError

    def get_all_tags(self) -> List[str]:
        """获取所有唯一的标签"""
        raise NotImplementedError

class SQLiteStorage(StorageBackend):
    """
    SQLite存储后端
    """
    def __init__(self, db_path: str = "prompts.db"):
        self.db_path = Path(db_path)
        self.init_database()
        
    def init_database(self):
        """初始化数据库，创建必要的表"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建提示词表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prompts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT,
                    content TEXT,
                    tags TEXT,
                    category TEXT,
                    media_files TEXT,
                    auxiliary_fields TEXT,
                    is_favorite INTEGER DEFAULT 0,
                    is_pinned INTEGER DEFAULT 0,
                    is_deleted INTEGER DEFAULT 0,
                    deleted_at TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')
            
            # 创建历史版本表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS prompt_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    prompt_id INTEGER,
                    title TEXT,
                    content TEXT,
                    tags TEXT,
                    category TEXT,
                    media_files TEXT,
                    auxiliary_fields TEXT,
                    version INTEGER,
                    created_at TEXT,
                    FOREIGN KEY (prompt_id) REFERENCES prompts (id) ON DELETE CASCADE
                )
            ''')
            
            # 检查prompts表是否有type字段
            cursor.execute("PRAGMA table_info(prompts)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'type' not in columns:
                # 添加type字段
                cursor.execute("ALTER TABLE prompts ADD COLUMN type TEXT DEFAULT '文本'")
                print("已向prompts表添加type字段")
                
            # 检查是否有is_deleted和deleted_at字段，如果没有则添加
            if 'is_deleted' not in columns:
                cursor.execute("ALTER TABLE prompts ADD COLUMN is_deleted INTEGER DEFAULT 0")
                print("已向prompts表添加is_deleted字段")
                
            if 'deleted_at' not in columns:
                cursor.execute("ALTER TABLE prompts ADD COLUMN deleted_at TEXT")
                print("已向prompts表添加deleted_at字段")
                
            # 检查是否有is_favorite字段，如果没有则添加
            if 'is_favorite' not in columns:
                cursor.execute("ALTER TABLE prompts ADD COLUMN is_favorite INTEGER DEFAULT 0")
                print("已向prompts表添加is_favorite字段")
                
            # 检查是否有is_pinned字段，如果没有则添加
            if 'is_pinned' not in columns:
                cursor.execute("ALTER TABLE prompts ADD COLUMN is_pinned INTEGER DEFAULT 0")
                print("已向prompts表添加is_pinned字段")
                
            # 检查prompt_history表是否有type字段
            cursor.execute("PRAGMA table_info(prompt_history)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'type' not in columns:
                # 添加type字段
                cursor.execute("ALTER TABLE prompt_history ADD COLUMN type TEXT DEFAULT '文本'")
                print("已向prompt_history表添加type字段")
                
            # 检查prompts表是否有auxiliary_fields字段
            cursor.execute("PRAGMA table_info(prompts)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'auxiliary_fields' not in columns:
                # 添加auxiliary_fields字段
                cursor.execute("ALTER TABLE prompts ADD COLUMN auxiliary_fields TEXT")
                print("已向prompts表添加auxiliary_fields字段")
                
            # 检查prompt_history表是否有auxiliary_fields字段
            cursor.execute("PRAGMA table_info(prompt_history)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'auxiliary_fields' not in columns:
                # 添加auxiliary_fields字段
                cursor.execute("ALTER TABLE prompt_history ADD COLUMN auxiliary_fields TEXT")
                print("已向prompt_history表添加auxiliary_fields字段")
            
            # 检查created_at和updated_at的类型
            cursor.execute("PRAGMA table_info(prompts)")
            columns = {column[1]: column[2] for column in cursor.fetchall()}
            
            # 如果类型不是TEXT，尝试修改
            if 'created_at' in columns and columns['created_at'] != 'TEXT':
                try:
                    # 创建临时表
                    cursor.execute('''
                        CREATE TABLE prompts_temp (
                            id INTEGER PRIMARY KEY AUTOINCREMENT,
                            title TEXT,
                            content TEXT,
                            tags TEXT,
                            category TEXT,
                            media_files TEXT,
                            type TEXT DEFAULT '文本',
                            is_favorite INTEGER DEFAULT 0,
                            is_pinned INTEGER DEFAULT 0,
                            created_at TEXT,
                            updated_at TEXT
                        )
                    ''')
                    
                    # 复制数据
                    cursor.execute('''
                        INSERT INTO prompts_temp 
                        SELECT id, title, content, tags, category, media_files, type, is_favorite, is_pinned,
                               created_at, updated_at
                        FROM prompts
                    ''')
                    
                    # 删除旧表
                    cursor.execute('DROP TABLE prompts')
                    
                    # 重命名临时表
                    cursor.execute('ALTER TABLE prompts_temp RENAME TO prompts')
                    
                    print("已修复prompts表中的created_at和updated_at字段类型")
                except Exception as e:
                    print(f"修复prompts表结构时出错: {e}")
            
            conn.commit()
        
    def add_prompt(self, prompt: Prompt) -> int:
        """添加提示词到数据库，返回提示词ID"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 将标签、媒体文件和辅助选择字段列表转换为JSON字符串
        tags_json = json.dumps(prompt.tags) if prompt.tags else "[]"
        media_files_json = json.dumps(prompt.media_files) if prompt.media_files else "[]"
        auxiliary_fields_json = json.dumps(prompt.auxiliary_fields) if prompt.auxiliary_fields else "{}"
        
        # 插入提示词记录
        cursor.execute('''
        INSERT INTO prompts (title, content, tags, category, media_files, auxiliary_fields, type, is_favorite, is_pinned, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            prompt.title,
            prompt.content,
            tags_json,
            prompt.category,
            media_files_json,
            auxiliary_fields_json,
            prompt.type,
            prompt.is_favorite,
            prompt.is_pinned,
            prompt.created_at,
            prompt.updated_at
        ))
        
        # 获取自动生成的ID
        prompt_id = cursor.lastrowid
        
        # 保存到历史记录
        cursor.execute('''
        INSERT INTO prompt_history (prompt_id, title, content, tags, category, media_files, auxiliary_fields, version, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            prompt_id,
            prompt.title,
            prompt.content,
            tags_json,
            prompt.category,
            media_files_json,
            auxiliary_fields_json,
            1,  # 首次保存为版本1
            prompt.created_at
        ))
        
        conn.commit()
        conn.close()
        
        # 更新提示词对象的ID
        prompt.id = prompt_id
        return prompt_id
        
    def update_prompt(self, prompt: Prompt):
        """更新提示词"""
        # 检查是否需要保存历史版本
        original_prompt = self.get_prompt(prompt.id)
        save_history = False
        
        if original_prompt:
            # 检查内容是否发生变化
            if original_prompt.content != prompt.content or \
               original_prompt.title != prompt.title or \
               original_prompt.category != prompt.category or \
               original_prompt.tags != prompt.tags or \
               original_prompt.auxiliary_fields != prompt.auxiliary_fields or \
               original_prompt.type != prompt.type:
                save_history = True
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 如果内容有变化，先保存当前版本到历史记录
        if save_history and original_prompt:
            # 查询当前最大版本号
            cursor.execute('''
            SELECT MAX(version) FROM prompt_history WHERE prompt_id = ?
            ''', (prompt.id,))
            result = cursor.fetchone()
            current_version = result[0] if result and result[0] else 0
            new_version = current_version + 1
            
            # 将原始提示词的标签、媒体文件和辅助选择字段转换为JSON字符串
            original_tags_json = json.dumps(original_prompt.tags) if original_prompt.tags else "[]"
            original_media_files_json = json.dumps(original_prompt.media_files) if original_prompt.media_files else "[]"
            original_auxiliary_fields_json = json.dumps(original_prompt.auxiliary_fields) if original_prompt.auxiliary_fields else "{}"
            
            # 保存当前版本（即将被替换的版本）到历史记录
            cursor.execute('''
            INSERT INTO prompt_history (prompt_id, title, content, tags, category, media_files, auxiliary_fields, version, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                prompt.id,
                original_prompt.title,
                original_prompt.content,
                original_tags_json,
                original_prompt.category,
                original_media_files_json,
                original_auxiliary_fields_json,
                new_version,
                original_prompt.updated_at
            ))
        
        # 将新版本的标签、媒体文件和辅助选择字段列表转换为JSON字符串
        tags_json = json.dumps(prompt.tags) if prompt.tags else "[]"
        media_files_json = json.dumps(prompt.media_files) if prompt.media_files else "[]"
        auxiliary_fields_json = json.dumps(prompt.auxiliary_fields) if prompt.auxiliary_fields else "{}"
        
        # 更新提示词记录为新版本
        cursor.execute('''
        UPDATE prompts
        SET title = ?, content = ?, tags = ?, category = ?, media_files = ?, auxiliary_fields = ?, type = ?, updated_at = ?
        WHERE id = ?
        ''', (
            prompt.title,
            prompt.content,
            tags_json,
            prompt.category,
            media_files_json,
            auxiliary_fields_json,
            prompt.type,
            prompt.updated_at,
            prompt.id
        ))
        
        conn.commit()
        conn.close()
    
    def save_prompt_history(self, prompt: Prompt) -> int:
        """保存提示词历史版本"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 获取当前提示词的最大版本号
            cursor.execute('''
                SELECT MAX(version) FROM prompt_history WHERE prompt_id=?
            ''', (prompt.id,))
            result = cursor.fetchone()
            max_version = result[0] if result[0] is not None else 0
            
            # 新版本号
            new_version = max_version + 1
            
            # 确保标签是列表类型，然后转为JSON字符串
            tags = prompt.tags if isinstance(prompt.tags, list) else []
            tags_json = json.dumps(tags)
            
            # 确保媒体文件是列表类型，然后转为JSON字符串
            media_files = prompt.media_files if isinstance(prompt.media_files, list) else []
            media_files_json = json.dumps(media_files)
            
            # 确保辅助选择字段是字典类型，然后转为JSON字符串
            auxiliary_fields = prompt.auxiliary_fields if isinstance(prompt.auxiliary_fields, dict) else {}
            auxiliary_fields_json = json.dumps(auxiliary_fields)
            
            # 确保type字段存在
            prompt_type = prompt.type if hasattr(prompt, 'type') and prompt.type else "文本"
            
            # 检查prompt_history表是否有type字段和auxiliary_fields字段
            cursor.execute("PRAGMA table_info(prompt_history)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'type' in columns and 'auxiliary_fields' in columns:
                # 如果有type字段和auxiliary_fields字段，使用包含这两个字段的SQL
                cursor.execute('''
                    INSERT INTO prompt_history (prompt_id, title, content, tags, category, media_files, auxiliary_fields, type, version)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (prompt.id, prompt.title, prompt.content, tags_json, 
                      prompt.category, media_files_json, auxiliary_fields_json, prompt_type, new_version))
            elif 'type' in columns:
                # 如果只有type字段，先添加auxiliary_fields字段
                try:
                    cursor.execute("ALTER TABLE prompt_history ADD COLUMN auxiliary_fields TEXT")
                    print("已向prompt_history表添加auxiliary_fields字段")
                except sqlite3.OperationalError:
                    # 字段可能已经存在
                    pass
                
                # 然后使用包含auxiliary_fields的SQL
                cursor.execute('''
                    INSERT INTO prompt_history (prompt_id, title, content, tags, category, media_files, auxiliary_fields, type, version)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (prompt.id, prompt.title, prompt.content, tags_json, 
                      prompt.category, media_files_json, auxiliary_fields_json, prompt_type, new_version))
            else:
                # 如果都没有，先添加这两个字段
                try:
                    cursor.execute("ALTER TABLE prompt_history ADD COLUMN type TEXT DEFAULT '文本'")
                    print("已向prompt_history表添加type字段")
                except sqlite3.OperationalError:
                    # 字段可能已经存在
                    pass
                    
                try:
                    cursor.execute("ALTER TABLE prompt_history ADD COLUMN auxiliary_fields TEXT")
                    print("已向prompt_history表添加auxiliary_fields字段")
                except sqlite3.OperationalError:
                    # 字段可能已经存在
                    pass
                
                # 然后使用包含这两个字段的SQL
                cursor.execute('''
                    INSERT INTO prompt_history (prompt_id, title, content, tags, category, media_files, auxiliary_fields, type, version)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (prompt.id, prompt.title, prompt.content, tags_json, 
                      prompt.category, media_files_json, auxiliary_fields_json, prompt_type, new_version))
            
            conn.commit()
            return cursor.lastrowid
    
    def get_prompt_history(self, prompt_id: int) -> List[PromptHistory]:
        """获取提示词的历史版本列表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row  # 使用字典行工厂
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM prompt_history 
                WHERE prompt_id=? 
                ORDER BY version DESC
            ''', (prompt_id,))
            
            history_items = []
            for row in cursor.fetchall():
                # 从数据库行中创建字典
                history_dict = dict(row)
                
                # 检查type字段是否存在
                prompt_type = "文本"  # 默认值
                if 'type' in history_dict:
                    prompt_type = history_dict['type']
                
                # 解析辅助选择字段
                auxiliary_fields = {}
                if 'auxiliary_fields' in history_dict and history_dict['auxiliary_fields']:
                    try:
                        auxiliary_fields = json.loads(history_dict['auxiliary_fields'])
                    except json.JSONDecodeError:
                        auxiliary_fields = {}
                
                # 创建历史版本对象
                history_item = PromptHistory(
                    id=history_dict['id'],
                    prompt_id=history_dict['prompt_id'],
                    title=history_dict['title'],
                    content=history_dict['content'],
                    tags=history_dict['tags'],
                    category=history_dict['category'],
                    media_files=history_dict['media_files'],
                    auxiliary_fields=auxiliary_fields,
                    type=prompt_type,
                    version=history_dict['version'],
                    created_at=history_dict['created_at']
                )
                history_items.append(history_item)
                
            return history_items
    
    def get_history_version(self, history_id: int) -> Optional[PromptHistory]:
        """获取特定的历史版本"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT * FROM prompt_history WHERE id=?
            ''', (history_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
                
            history_dict = dict(row)
            
            # 检查type字段是否存在
            prompt_type = "文本"  # 默认值
            if 'type' in history_dict:
                prompt_type = history_dict['type']
                
            # 解析辅助选择字段
            auxiliary_fields = {}
            if 'auxiliary_fields' in history_dict and history_dict['auxiliary_fields']:
                try:
                    auxiliary_fields = json.loads(history_dict['auxiliary_fields'])
                except json.JSONDecodeError:
                    auxiliary_fields = {}
            
            return PromptHistory(
                id=history_dict['id'],
                prompt_id=history_dict['prompt_id'],
                title=history_dict['title'],
                content=history_dict['content'],
                tags=history_dict['tags'],
                category=history_dict['category'],
                media_files=history_dict['media_files'],
                auxiliary_fields=auxiliary_fields,
                type=prompt_type,
                version=history_dict['version'],
                created_at=history_dict['created_at']
            )
    
    def restore_prompt_from_history(self, history_id: int) -> bool:
        """从历史版本恢复提示词"""
        # 获取历史版本
        history_version = self.get_history_version(history_id)
        if not history_version:
            return False
            
        # 转换为提示词对象
        prompt = history_version.to_prompt()
        
        # 更新提示词（不会触发历史记录保存，因为是恢复操作）
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 确保标签是列表类型，然后转为JSON字符串
            tags = prompt.tags if isinstance(prompt.tags, list) else []
            tags_json = json.dumps(tags)
            
            # 确保媒体文件是列表类型，然后转为JSON字符串
            media_files = prompt.media_files if isinstance(prompt.media_files, list) else []
            media_files_json = json.dumps(media_files)
            
            cursor.execute('''
                UPDATE prompts 
                SET title=?, content=?, tags=?, category=?, media_files=?, updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', (prompt.title, prompt.content, tags_json, 
                  prompt.category, media_files_json, prompt.id))
            conn.commit()
            
            return cursor.rowcount > 0
            
    def delete_prompt(self, prompt_id: int):
        """删除提示词(移至回收站)"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            # 将提示词标记为已删除，并记录删除时间
            cursor.execute('''
                UPDATE prompts 
                SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP 
                WHERE id=?
            ''', (prompt_id,))
            conn.commit()
            
    def permanently_delete_prompt(self, prompt_id: int):
        """永久删除提示词"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM prompts WHERE id=?', (prompt_id,))
            conn.commit()
            
    def restore_prompt(self, prompt_id: int):
        """从回收站恢复提示词"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE prompts 
                SET is_deleted = 0, deleted_at = NULL 
                WHERE id=?
            ''', (prompt_id,))
            conn.commit()
            
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        """根据ID获取提示词"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使用命名行
            cursor = conn.cursor()
            
            # 检查数据库中是否有type列和auxiliary_fields列
            cursor.execute("PRAGMA table_info(prompts)")
            columns = [column[1] for column in cursor.fetchall()]
            has_type_column = 'type' in columns
            has_auxiliary_fields_column = 'auxiliary_fields' in columns
            
            # 根据是否有type列和auxiliary_fields列构建查询
            if has_type_column and has_auxiliary_fields_column:
                query = '''
                SELECT id, title, content, tags, category, media_files, auxiliary_fields, type, is_favorite, is_pinned, created_at, updated_at
                FROM prompts
                WHERE id = ?
                '''
            elif has_type_column:
                query = '''
                SELECT id, title, content, tags, category, media_files, type, is_favorite, is_pinned, created_at, updated_at
                FROM prompts
                WHERE id = ?
                '''
            else:
                query = '''
                SELECT id, title, content, tags, category, media_files, is_favorite, is_pinned, created_at, updated_at
                FROM prompts
                WHERE id = ?
                '''
            
            # 查询提示词
            cursor.execute(query, (prompt_id,))
            row = cursor.fetchone()
            
            if not row:
                conn.close()
                return None
            
            # 将标签JSON字符串转换为列表
            try:
                tags = json.loads(row['tags']) if row['tags'] else []
            except json.JSONDecodeError:
                tags = []
                
            # 将媒体文件JSON字符串转换为列表
            try:
                media_files = json.loads(row['media_files']) if row['media_files'] else []
            except json.JSONDecodeError:
                media_files = []
                
            # 将辅助选择字段JSON字符串转换为字典
            auxiliary_fields = {}
            if has_auxiliary_fields_column and 'auxiliary_fields' in row.keys() and row['auxiliary_fields']:
                try:
                    auxiliary_fields = json.loads(row['auxiliary_fields'])
                    print(f"[DEBUG] 成功解析auxiliary_fields: {auxiliary_fields}")
                except json.JSONDecodeError as e:
                    auxiliary_fields = {}
                    print(f"[DEBUG] 解析auxiliary_fields失败: {e}")
            else:
                print(f"[DEBUG] auxiliary_fields列不存在或为空: has_auxiliary_fields_column={has_auxiliary_fields_column}, keys={list(row.keys()) if hasattr(row, 'keys') else 'no keys'}")
            
            # 获取类型，如果不存在则根据媒体文件推断
            prompt_type = '文本'  # 默认为文本类型
            if has_type_column and 'type' in row.keys():
                prompt_type = row['type']
            elif media_files:
                # 根据第一个媒体文件推断类型
                from pathlib import Path
                file_path = Path(media_files[0])
                if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                    prompt_type = "图片"
                elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                    prompt_type = "视频"
                
            # 创建并返回Prompt对象
            prompt = Prompt(
                id=row['id'],
                title=row['title'],
                content=row['content'],
                tags=tags,
                category=row['category'],
                media_files=media_files,
                auxiliary_fields=auxiliary_fields,
                type=prompt_type,
                is_favorite=row['is_favorite'],
                is_pinned=row['is_pinned'],
                created_at=row['created_at'] if 'created_at' in row.keys() else None,
                updated_at=row['updated_at'] if 'updated_at' in row.keys() else None
            )
            
            conn.close()
            return prompt
        except Exception as e:
            print(f"获取提示词出错: {e}")
            return None
        
    def get_all_prompts(self, include_deleted=False) -> List[Prompt]:
        """获取所有提示词
        
        Args:
            include_deleted: 是否包含已删除的提示词，默认为False
        """
        prompts = []
        with sqlite3.connect(self.db_path) as conn:
            # 启用外键支持
            conn.execute("PRAGMA foreign_keys = ON")
            
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 构建查询语句，根据include_deleted参数决定是否包含已删除的提示词
            query = "SELECT * FROM prompts"
            if not include_deleted:
                query += " WHERE is_deleted = 0 OR is_deleted IS NULL"
                
            # 执行查询
            cursor.execute(query)
            
            for row in cursor.fetchall():
                # 获取基本字段
                prompt = Prompt(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    category=row['category'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    is_favorite=row['is_favorite'] if 'is_favorite' in row.keys() else 0,
                    is_pinned=row['is_pinned'] if 'is_pinned' in row.keys() else 0,
                    is_deleted=row['is_deleted'] if 'is_deleted' in row.keys() else 0,
                    deleted_at=row['deleted_at'] if 'deleted_at' in row.keys() else None
                )
                
                # 处理tags字段，将JSON字符串转换为列表
                if row['tags']:
                    try:
                        prompt.tags = json.loads(row['tags'])
                    except json.JSONDecodeError:
                        prompt.tags = []
                    
                # 处理media_files字段，将JSON字符串转换为列表
                if 'media_files' in row.keys() and row['media_files']:
                    try:
                        prompt.media_files = json.loads(row['media_files'])
                    except json.JSONDecodeError:
                        prompt.media_files = []
                
                # 处理type字段
                if 'type' in row.keys() and row['type']:
                    prompt.type = row['type']
                
                # 处理auxiliary_fields字段
                if 'auxiliary_fields' in row.keys() and row['auxiliary_fields']:
                    try:
                        prompt.auxiliary_fields = json.loads(row['auxiliary_fields'])
                    except json.JSONDecodeError:
                        prompt.auxiliary_fields = {}
                else:
                    prompt.auxiliary_fields = {}
                
                prompts.append(prompt)
        
        return prompts
    
    def get_deleted_prompts(self) -> List[Prompt]:
        """获取已删除的提示词（回收站内容）"""
        prompts = []
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM prompts WHERE is_deleted = 1")
            
            for row in cursor.fetchall():
                prompt = Prompt(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    category=row['category'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    is_favorite=row['is_favorite'] if 'is_favorite' in row.keys() else 0,
                    is_pinned=row['is_pinned'] if 'is_pinned' in row.keys() else 0,
                    is_deleted=row['is_deleted'] if 'is_deleted' in row.keys() else 0,
                    deleted_at=row['deleted_at'] if 'deleted_at' in row.keys() else None
                )
                
                # 处理tags字段，将JSON字符串转换为列表
                if row['tags']:
                    try:
                        prompt.tags = json.loads(row['tags'])
                    except json.JSONDecodeError:
                        prompt.tags = []
                
                # 处理media_files字段，将JSON字符串转换为列表
                if 'media_files' in row.keys() and row['media_files']:
                    try:
                        prompt.media_files = json.loads(row['media_files'])
                    except json.JSONDecodeError:
                        prompt.media_files = []
                        
                # 处理type字段
                if 'type' in row.keys() and row['type']:
                    prompt.type = row['type']
                
                # 处理auxiliary_fields字段
                if 'auxiliary_fields' in row.keys() and row['auxiliary_fields']:
                    try:
                        prompt.auxiliary_fields = json.loads(row['auxiliary_fields'])
                    except json.JSONDecodeError:
                        prompt.auxiliary_fields = {}
                else:
                    prompt.auxiliary_fields = {}
                
                prompts.append(prompt)
            
            return prompts
            
    def search_prompts(self, keyword: str, include_deleted=False) -> List[Prompt]:
        """搜索提示词
        
        Args:
            keyword: 搜索关键词
            include_deleted: 是否包含已删除的提示词，默认为False
        """
        prompts = []
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            # 构建查询语句，根据include_deleted参数决定是否包含已删除的提示词
            query = """
                SELECT * FROM prompts 
                WHERE (title LIKE ? OR content LIKE ? OR category LIKE ? OR tags LIKE ?)
            """
            if not include_deleted:
                query += " AND (is_deleted = 0 OR is_deleted IS NULL)"
                
            # 执行查询
            cursor.execute(query, (f'%{keyword}%', f'%{keyword}%', f'%{keyword}%', f'%{keyword}%'))
            
            for row in cursor.fetchall():
                prompt = Prompt(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    category=row['category'],
                    created_at=row['created_at'],
                    updated_at=row['updated_at'],
                    is_favorite=row['is_favorite'] if 'is_favorite' in row.keys() else 0,
                    is_pinned=row['is_pinned'] if 'is_pinned' in row.keys() else 0,
                    is_deleted=row['is_deleted'] if 'is_deleted' in row.keys() else 0,
                    deleted_at=row['deleted_at'] if 'deleted_at' in row.keys() else None
                )
                
                # 处理tags字段
                if row['tags']:
                    try:
                        tags = json.loads(row['tags'])
                        prompt.tags = tags
                    except json.JSONDecodeError:
                        prompt.tags = []
                
                # 处理media_files字段
                if 'media_files' in row.keys() and row['media_files']:
                    try:
                        media_files = json.loads(row['media_files'])
                        prompt.media_files = media_files
                    except json.JSONDecodeError:
                        prompt.media_files = []
                
                # 处理type字段
                if 'type' in row.keys() and row['type']:
                    prompt.type = row['type']
                    
                prompts.append(prompt)
        
        return prompts

    def get_all_tags(self) -> List[str]:
        """获取所有唯一的标签"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT tags FROM prompts')
            rows = cursor.fetchall()
            
            all_tags = set()
            for row in rows:
                tags_str = row[0]
                if tags_str:
                    try:
                        tags_list = json.loads(tags_str)
                        if isinstance(tags_list, list):
                            all_tags.update(tag for tag in tags_list if tag.strip())
                    except json.JSONDecodeError:
                        # 如果JSON解析失败，按逗号分割
                        if isinstance(tags_str, str):
                            all_tags.update([tag.strip() for tag in tags_str.split(',') if tag.strip()])
                            
            return sorted(list(all_tags))
    
    def toggle_favorite(self, prompt_id: int) -> bool:
        """切换提示词的收藏状态，如果成功返回True，失败返回False"""
        prompt = self.get_prompt(prompt_id)
        if not prompt:
            print(f"❌ 未找到ID为{prompt_id}的提示词")
            return False
            
        try:
            # 切换收藏状态
            new_state = 0 if prompt.is_favorite == 1 else 1
            print(f"🔄 切换提示词{prompt_id}的收藏状态: {prompt.is_favorite} -> {new_state}")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 先检查字段是否存在
                cursor.execute("PRAGMA table_info(prompts)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'is_favorite' not in columns:
                    print("❌ 数据库中缺少is_favorite字段")
                    return False
                
                cursor.execute('UPDATE prompts SET is_favorite=? WHERE id=?', (new_state, prompt_id))
                affected_rows = cursor.rowcount
                conn.commit()
                
                if affected_rows > 0:
                    print(f"✅ 成功更新提示词{prompt_id}的收藏状态为{new_state}")
                    return True
                else:
                    print(f"❌ 更新收藏状态失败，没有影响到任何行")
                    return False
                
        except Exception as e:
            print(f"❌ 切换收藏状态时出错: {e}")
            return False
            
    def toggle_pin(self, prompt_id: int) -> bool:
        """切换提示词的置顶状态，如果成功返回True，失败返回False"""
        prompt = self.get_prompt(prompt_id)
        if not prompt:
            print(f"❌ 未找到ID为{prompt_id}的提示词")
            return False
            
        try:
            # 切换置顶状态
            new_state = 0 if prompt.is_pinned == 1 else 1
            print(f"🔄 切换提示词{prompt_id}的置顶状态: {prompt.is_pinned} -> {new_state}")
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                # 先检查字段是否存在
                cursor.execute("PRAGMA table_info(prompts)")
                columns = [column[1] for column in cursor.fetchall()]
                if 'is_pinned' not in columns:
                    print("❌ 数据库中缺少is_pinned字段")
                    return False
                
                cursor.execute('UPDATE prompts SET is_pinned=? WHERE id=?', (new_state, prompt_id))
                affected_rows = cursor.rowcount
                conn.commit()
                
                if affected_rows > 0:
                    print(f"✅ 成功更新提示词{prompt_id}的置顶状态为{new_state}")
                    return True
                else:
                    print(f"❌ 更新置顶状态失败，没有影响到任何行")
                    return False
                
        except Exception as e:
            print(f"❌ 切换置顶状态时出错: {e}")
            return False
            
    def get_favorite_prompts(self) -> List[Prompt]:
        """获取收藏的提示词列表"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使用命名行
            cursor = conn.cursor()
            
            # 检查数据库中是否有type列
            cursor.execute("PRAGMA table_info(prompts)")
            columns = [column[1] for column in cursor.fetchall()]
            has_type_column = 'type' in columns
            has_deleted_column = 'is_deleted' in columns
            
            # 构建WHERE条件
            where_clause = "WHERE is_favorite = 1"
            if has_deleted_column:
                where_clause += " AND (is_deleted = 0 OR is_deleted IS NULL)"
            
            # 根据是否有type列构建查询
            if has_type_column:
                query = f'''
                SELECT id, title, content, tags, category, media_files, type, is_favorite, is_pinned, created_at, updated_at
                FROM prompts
                {where_clause}
                ORDER BY 
                    is_pinned DESC,  -- 置顶的排在前面
                    updated_at DESC   -- 最近更新的排在前面
                '''
            else:
                query = f'''
                SELECT id, title, content, tags, category, media_files, is_favorite, is_pinned, created_at, updated_at
                FROM prompts
                {where_clause}
                ORDER BY 
                    is_pinned DESC,  -- 置顶的排在前面
                    updated_at DESC   -- 最近更新的排在前面
                '''
            
            # 查询收藏的提示词
            cursor.execute(query)
            rows = cursor.fetchall()
            prompts = []
            
            for row in rows:
                # 将标签JSON字符串转换为列表
                try:
                    tags = json.loads(row['tags']) if row['tags'] else []
                except json.JSONDecodeError:
                    tags = []
                    
                # 将媒体文件JSON字符串转换为列表
                try:
                    media_files = json.loads(row['media_files']) if row['media_files'] else []
                except json.JSONDecodeError:
                    media_files = []
                
                # 获取类型，如果不存在则根据媒体文件推断
                prompt_type = '文本'  # 默认为文本类型
                if has_type_column and 'type' in row.keys():
                    prompt_type = row['type']
                elif media_files:
                    # 根据第一个媒体文件推断类型
                    from pathlib import Path
                    file_path = Path(media_files[0])
                    if file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']:
                        prompt_type = "图片"
                    elif file_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm']:
                        prompt_type = "视频"
                    
                # 创建Prompt对象
                prompt = Prompt(
                    id=row['id'],
                    title=row['title'],
                    content=row['content'],
                    tags=tags,
                    category=row['category'],
                    media_files=media_files,
                    type=prompt_type,
                    is_favorite=row['is_favorite'],
                    is_pinned=row['is_pinned'],
                    created_at=row['created_at'] if 'created_at' in row.keys() else None,
                    updated_at=row['updated_at'] if 'updated_at' in row.keys() else None
                )
                
                prompts.append(prompt)
            
            conn.close()
            return prompts
        except Exception as e:
            print(f"获取收藏提示词列表出错: {e}")
            return []
            
    def clear_all_prompts(self) -> bool:
        """清空所有提示词(移至回收站)"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 将所有提示词标记为已删除，并记录删除时间
            cursor.execute('''
                UPDATE prompts 
                SET is_deleted = 1, deleted_at = CURRENT_TIMESTAMP 
                WHERE is_deleted = 0 OR is_deleted IS NULL
            ''')
            
            # 删除所有历史版本（历史版本不需要保留）
            cursor.execute("DELETE FROM prompt_history")
            
            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"清空提示词列表出错: {e}")
            return False

class JSONStorage(StorageBackend):
    """
    JSON文件存储后端
    """
    def __init__(self, file_path: str = "prompts.json"):
        self.file_path = Path(file_path)
        self._ensure_file_exists()
        
    def _ensure_file_exists(self):
        """确保JSON文件存在"""
        if not self.file_path.exists():
            self.file_path.write_text(json.dumps([]), encoding='utf-8')
            
    def _load_prompts(self) -> List[Dict]:
        """从JSON文件加载所有提示词"""
        try:
            return json.loads(self.file_path.read_text(encoding='utf-8'))
        except (json.JSONDecodeError, FileNotFoundError):
            return []
            
    def _save_prompts(self, prompts: List[Dict]):
        """将提示词保存到JSON文件"""
        self.file_path.write_text(json.dumps(prompts, ensure_ascii=False, indent=2), encoding='utf-8')
        
    def add_prompt(self, prompt: Prompt) -> int:
        """添加新的提示词"""
        prompts = self._load_prompts()
        
        # 为新提示词分配ID
        if prompts:
            prompt.id = max(p.get('id', 0) for p in prompts) + 1
        else:
            prompt.id = 1
        
        # 确保收藏状态字段存在
        if not hasattr(prompt, 'is_favorite'):
            prompt.is_favorite = 0
            
        # 确保置顶状态字段存在
        if not hasattr(prompt, 'is_pinned'):
            prompt.is_pinned = 0
            
        prompts.append(prompt.to_dict())
        self._save_prompts(prompts)
        return prompt.id
            
    def update_prompt(self, prompt: Prompt):
        """更新提示词"""
        prompts = self._load_prompts()
        for i, p in enumerate(prompts):
            if p.get('id') == prompt.id:
                prompts[i] = prompt.to_dict()
                break
        self._save_prompts(prompts)
            
    def delete_prompt(self, prompt_id: int):
        """删除提示词"""
        prompts = self._load_prompts()
        prompts = [p for p in prompts if p.get('id') != prompt_id]
        self._save_prompts(prompts)
            
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        """根据ID获取提示词"""
        prompts = self._load_prompts()
        for p in prompts:
            if p.get('id') == prompt_id:
                # 确保收藏状态字段存在
                if 'is_favorite' not in p:
                    p['is_favorite'] = 0
                # 确保置顶状态字段存在
                if 'is_pinned' not in p:
                    p['is_pinned'] = 0
                return Prompt.from_dict(p)
        return None
            
    def get_all_prompts(self) -> List[Prompt]:
        """获取所有提示词"""
        prompts = self._load_prompts()
        return [Prompt.from_dict(p) for p in prompts]
            
    def search_prompts(self, keyword: str) -> List[Prompt]:
        """根据关键词搜索提示词"""
        prompts = self._load_prompts()
        result = []
        for p in prompts:
            prompt_obj = Prompt.from_dict(p)
            if (keyword.lower() in prompt_obj.title.lower() or 
                keyword.lower() in prompt_obj.content.lower() or
                any(keyword.lower() in tag.lower() for tag in prompt_obj.tags)):
                result.append(prompt_obj)
        return result

    def get_all_tags(self) -> List[str]:
        """获取所有唯一的标签"""
        prompts = self._load_prompts()
        all_tags = set()
        for p in prompts:
            prompt_obj = Prompt.from_dict(p)
            if prompt_obj.tags:
                all_tags.update(tag for tag in prompt_obj.tags if tag.strip())
        return sorted(list(all_tags))

class PromptModel:
    """提示词模型，封装对提示词的操作"""
    
    def __init__(self, storage_type: str = "sqlite", storage_path: str = None):
        """初始化提示词模型"""
        if storage_type == "sqlite":
            self.storage = SQLiteStorage(storage_path or "prompts.db")
        elif storage_type == "json":
            self.storage = JSONStorage(storage_path or "prompts.json")
        else:
            raise ValueError(f"不支持的存储类型: {storage_type}")
        
    def add_prompt(self, prompt: Prompt) -> int:
        """添加提示词"""
        return self.storage.add_prompt(prompt)
            
    def update_prompt(self, prompt: Prompt):
        """更新提示词"""
        self.storage.update_prompt(prompt)
            
    def delete_prompt(self, prompt_id: int):
        """删除提示词(移至回收站)"""
        self.storage.delete_prompt(prompt_id)
        
    def permanently_delete_prompt(self, prompt_id: int):
        """永久删除提示词"""
        self.storage.permanently_delete_prompt(prompt_id)
        
    def restore_prompt(self, prompt_id: int):
        """从回收站恢复提示词"""
        self.storage.restore_prompt(prompt_id)
            
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        """获取指定ID的提示词"""
        return self.storage.get_prompt(prompt_id)
            
    def get_all_prompts(self, include_deleted=False) -> List[Prompt]:
        """获取所有提示词"""
        return self.storage.get_all_prompts(include_deleted)
        
    def get_deleted_prompts(self) -> List[Prompt]:
        """获取所有已删除的提示词"""
        return self.storage.get_deleted_prompts()
            
    def search_prompts(self, keyword: str, include_deleted=False) -> List[Prompt]:
        """搜索提示词"""
        return self.storage.search_prompts(keyword, include_deleted)

    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        return self.storage.get_all_tags()
        
    def save_prompt_history(self, prompt: Prompt) -> int:
        """保存提示词历史版本"""
        # 仅SQLite存储后端支持历史版本
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.save_prompt_history(prompt)
        return 0
    
    def get_prompt_history(self, prompt_id: int) -> List[PromptHistory]:
        """获取提示词的历史版本"""
        # 仅SQLite存储后端支持历史版本
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.get_prompt_history(prompt_id)
        return []
        
    def get_history_version(self, history_id: int) -> Optional[PromptHistory]:
        """获取特定历史版本"""
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.get_history_version(history_id)
        return None
        
    def restore_prompt_from_history(self, history_id: int) -> bool:
        """从历史版本恢复提示词"""
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.restore_prompt_from_history(history_id)
        return False
        
    def toggle_favorite(self, prompt_id: int) -> bool:
        """切换提示词的收藏状态"""
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.toggle_favorite(prompt_id)
        return False
        
    def toggle_pin(self, prompt_id: int) -> bool:
        """切换提示词的置顶状态"""
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.toggle_pin(prompt_id)
        return False
        
    def get_favorite_prompts(self) -> List[Prompt]:
        """获取所有收藏的提示词"""
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.get_favorite_prompts()
        return []
        
    def clear_all_prompts(self) -> bool:
        """清空所有提示词"""
        if isinstance(self.storage, SQLiteStorage):
            return self.storage.clear_all_prompts()
        return False
