#!/usr/bin/env python3
"""
演示文件分析功能的脚本
"""

import json
import csv
from datetime import datetime
from pathlib import Path
from model import Prompt

def create_demo_files():
    """创建演示文件"""
    
    # 创建演示数据
    demo_prompts = [
        Prompt(
            title="AI绘画提示词",
            content="一个美丽的风景画，包含山脉、湖泊和日落，使用油画风格",
            category="AI绘画",
            tags=["风景", "油画", "日落"],
            type="图片"
        ),
        Prompt(
            title="代码优化建议",
            content="请帮我优化这段Python代码的性能，重点关注循环和数据结构的使用",
            category="编程",
            tags=["Python", "性能优化", "代码"],
            type="文本"
        ),
        Prompt(
            title="商业计划书",
            content="为一家新的科技创业公司撰写商业计划书，包括市场分析、财务预测和营销策略",
            category="商业",
            tags=["创业", "商业计划", "营销"],
            type="文本"
        ),
        Prompt(
            title="健康饮食建议",
            content="为一位想要减肥的上班族制定一周的健康饮食计划，包含营养搭配和食谱",
            category="健康",
            tags=["减肥", "营养", "食谱"],
            type="文本"
        ),
        Prompt(
            title="旅行攻略",
            content="为日本东京5日游制定详细的旅行攻略，包括景点推荐、交通安排和美食指南",
            category="旅行",
            tags=["日本", "东京", "攻略"],
            type="文本"
        ),
        Prompt(
            title="学习计划制定",
            content="为准备考研的学生制定3个月的复习计划，包括各科目的时间分配和学习方法",
            category="教育",
            tags=["考研", "学习计划", "复习"],
            type="文本"
        )
    ]
    
    print("创建演示文件...")
    
    # 创建JSON文件
    json_data = []
    for prompt in demo_prompts:
        prompt_dict = prompt.to_dict()
        if 'id' in prompt_dict:
            del prompt_dict['id']
        json_data.append(prompt_dict)
    
    with open('demo_prompts.json', 'w', encoding='utf-8') as f:
        json.dump(json_data, f, ensure_ascii=False, indent=2)
    print("✅ JSON文件已创建: demo_prompts.json")
    
    # 创建CSV文件
    with open('demo_prompts.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow(['标题', '内容', '分类', '标签', '类型', '收藏', '置顶', '创建时间'])
        
        for prompt in demo_prompts:
            tags_str = ', '.join(prompt.tags) if prompt.tags else ''
            writer.writerow([
                prompt.title,
                prompt.content,
                prompt.category,
                tags_str,
                prompt.type,
                '是' if prompt.is_favorite else '否',
                '是' if prompt.is_pinned else '否',
                prompt.created_at
            ])
    print("✅ CSV文件已创建: demo_prompts.csv")
    
    # 创建Markdown文件
    with open('demo_prompts.md', 'w', encoding='utf-8') as f:
        f.write("# 提示词集合\n\n")
        f.write(f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"总计: {len(demo_prompts)} 个提示词\n\n")
        
        for i, prompt in enumerate(demo_prompts, 1):
            f.write(f"## {i}. {prompt.title}\n\n")
            f.write(f"**分类:** {prompt.category}\n\n")
            
            if prompt.tags:
                tags_str = ', '.join([f'`{tag}`' for tag in prompt.tags])
                f.write(f"**标签:** {tags_str}\n\n")
            
            f.write(f"**类型:** {prompt.type}\n\n")
            
            if prompt.is_favorite:
                f.write("**收藏:** 是\n\n")
            if prompt.is_pinned:
                f.write("**置顶:** 是\n\n")
            
            f.write("**内容:**\n\n")
            f.write(f"{prompt.content}\n\n")
            
            if prompt.created_at:
                f.write(f"**创建时间:** {prompt.created_at}\n\n")
            
            f.write("---\n\n")
    print("✅ Markdown文件已创建: demo_prompts.md")
    
    print(f"\n📁 演示文件创建完成！")
    print("现在可以在应用中使用'从文件导入'功能来测试这些文件：")
    print("- demo_prompts.json (JSON格式)")
    print("- demo_prompts.csv (CSV格式)")
    print("- demo_prompts.md (Markdown格式)")
    print("\n每个文件都包含6个不同类别的提示词，可以测试分析功能的效果。")

if __name__ == '__main__':
    create_demo_files() 