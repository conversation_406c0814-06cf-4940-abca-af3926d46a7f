#!/usr/bin/env python3
"""
筛选按钮组件模块
从main.py提取的筛选相关按钮组件
"""
from PySide6.QtWidgets import QPushButton, QMenu
from PySide6.QtCore import Qt, Signal, QSize, QPoint
from PySide6.QtGui import QCursor, QIcon, QPixmap, QPainter
from PySide6.QtSvg import QSvgRenderer

from components.button_factory import ButtonFactory
from components.style_manager import StyleManager


class FilterToggleButton(QPushButton):
    """筛选切换按钮 - 通用筛选按钮类"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.key = key
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        
        # 为收藏按钮添加激活状态下的特殊SVG
        if key == "favorite":
            self.active_svg = '''<svg width="16" height="16" viewBox="0 0 24 24" fill="#FFD700" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2Z" stroke="#EAB308" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>'''
        else:
            self.active_svg = None
            
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            if self.key == "favorite":
                # 收藏按钮激活状态：透明背景，黄色边框
                bg_color = "transparent"
                border_color = "#FBBF24"
            else:
                # 其他按钮激活状态：蓝色背景，白色图标
                bg_color = "#3B82F6"
                border_color = "#3B82F6"
            icon_color = "#FFFFFF"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        # 使用StyleManager获取按钮样式
        button_style = StyleManager.get_button_style('filter')
        tooltip_style = StyleManager.get_tooltip_style()
        
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"transparent" if self.is_active and self.key == "favorite" else "#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#F59E0B" if self.is_active and self.key == "favorite" else "#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"transparent" if self.is_active and self.key == "favorite" else "#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            {tooltip_style}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        # 收藏按钮激活状态使用特殊SVG
        if self.is_active and self.key == "favorite" and self.active_svg:
            svg_data = self.active_svg
        else:
            svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event)


class ToolboxButton(FilterToggleButton):
    """工具箱按钮 - 带下拉菜单功能"""
    
    def __init__(self, svg_content, tooltip, key, parent=None):
        super().__init__(svg_content, tooltip, key, parent)
        self.menu = None
        self.parent_content_area = parent
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.showMenu()
        super().mousePressEvent(event)
    
    def showMenu(self):
        """显示下拉菜单"""
        self.menu = QMenu(self)
        self.menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 6px;
                padding: 5px;
            }
            QMenu::item {
                padding: 6px 25px 6px 10px;
                border-radius: 4px;
                margin: 2px 5px;
                color: #1F2937;
                font-size: 12px;
            }
            QMenu::item:selected {
                background-color: #EBF4FF;
                color: #1E40AF;
            }
            QMenu::separator {
                height: 1px;
                background-color: #E5E7EB;
                margin: 5px 10px;
            }
        """)
        
        # 添加分享功能菜单项
        share_action = self.menu.addAction("分享提示词")
        share_action.triggered.connect(self.on_share_action)
        
        # 添加导入功能菜单项
        import_action = self.menu.addAction("获取提示词")
        import_action.triggered.connect(self.on_import_action)
        
        # 添加分隔线
        self.menu.addSeparator()
        
        # 添加导入导出功能菜单项
        export_action = self.menu.addAction("导出提示词")
        export_action.triggered.connect(self.on_export_action)
        
        import_file_action = self.menu.addAction("从文件导入")
        import_file_action.triggered.connect(self.on_import_file_action)
        
        # 添加分隔线
        self.menu.addSeparator()
        
        # 添加清空列表选项
        clear_action = self.menu.addAction("清空列表")
        clear_action.triggered.connect(self.on_clear_action)
        
        # 菜单位置计算：在按钮下方居中
        pos = self.mapToGlobal(QPoint(0, self.height()))
        self.menu.popup(pos)
    
    def on_share_action(self):
        """分享提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.share_prompts()
    
    def on_import_action(self):
        """获取提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.import_prompt()
    
    def on_clear_action(self):
        """清空列表事件处理"""
        if self.parent_content_area:
            self.parent_content_area.clear_all_prompts()
    
    def on_export_action(self):
        """导出提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.export_prompts()
    
    def on_import_file_action(self):
        """从文件导入提示词事件处理"""
        if self.parent_content_area:
            self.parent_content_area.import_prompts_from_file()


class ViewToggleButton(QPushButton):
    """视图切换按钮 - 28x28px尺寸"""
    
    def __init__(self, svg_content, tooltip, view_type, parent=None):
        super().__init__(parent)
        self.svg_content = svg_content
        self.view_type = view_type
        self.is_active = False
        self.setFixedSize(28, 28)
        self.setToolTip(tooltip)
        self.update_style()
        
    def update_style(self):
        """更新按钮样式"""
        if self.is_active:
            # 激活状态：蓝色背景，白色图标
            bg_color = "#3B82F6"
            icon_color = "#FFFFFF"
            border_color = "#3B82F6"
        else:
            # 默认状态：透明背景，灰色图标
            bg_color = "transparent"
            icon_color = "#6B7280"
            border_color = "#D1D5DB"
            
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {bg_color};
                border: 1px solid {border_color};
                border-radius: 4px;
            }}
            QPushButton:hover {{
                background-color: {"#2563EB" if self.is_active else "#F3F4F6"};
                border-color: {"#2563EB" if self.is_active else "#9CA3AF"};
            }}
            QPushButton:pressed {{
                background-color: {"#1D4ED8" if self.is_active else "#E5E7EB"};
            }}
            QToolTip {{
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }}
        """)
        
        # 更新图标
        self.update_icon(icon_color)
        
    def update_icon(self, color):
        """更新图标颜色"""
        svg_data = self.svg_content.replace("currentColor", color)
        
        renderer = QSvgRenderer()
        renderer.load(svg_data.encode())
        
        pixmap = QPixmap(16, 16)
        pixmap.fill(Qt.transparent)
        
        painter = QPainter(pixmap)
        renderer.render(painter)
        painter.end()
        
        self.setIcon(QIcon(pixmap))
        self.setIconSize(QSize(16, 16))
        
    def set_active(self, active):
        """设置激活状态"""
        self.is_active = active
        self.update_style()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        if not self.is_active:
            # 悬停状态
            self.update_icon("#374151")
        super().enterEvent(event)
        
    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.update_style()
        super().leaveEvent(event) 