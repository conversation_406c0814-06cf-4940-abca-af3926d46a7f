#!/usr/bin/env python3
"""
完整的删除流程测试
模拟用户的完整操作：删除提示词 -> 切换到回收站 -> 查看是否显示
"""

from PySide6.QtWidgets import QApplication, QMessageBox
import sys
from model import PromptModel, Prompt
from main import PromptCardWidget, ContentArea

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🧪 完整删除流程测试")
print("=" * 60)

# 1. 准备测试数据
model = PromptModel()
active_prompts = model.get_all_prompts(include_deleted=False)

if not active_prompts:
    print("❌ 没有活跃提示词可供测试")
    exit(1)

test_prompt = active_prompts[0]
print(f"📝 测试提示词: ID={test_prompt.id}, 标题={test_prompt.title[:50]}...")

# 2. 记录删除前状态
before_deleted = len(model.get_deleted_prompts())
print(f"📊 删除前回收站提示词数量: {before_deleted}")

# 3. 模拟删除操作（直接调用model，跳过UI确认对话框）
print("\n🗑️ 步骤1：执行删除操作...")
model.delete_prompt(test_prompt.id)
print("✅ 删除操作完成")

# 4. 验证删除结果
after_deleted = len(model.get_deleted_prompts())
print(f"📊 删除后回收站提示词数量: {after_deleted}")

if after_deleted > before_deleted:
    print("✅ 提示词已成功移入回收站！")
    
    # 5. 模拟切换到回收站页面并验证显示
    print("\n📱 步骤2：模拟切换到回收站页面...")
    
    # 创建ContentArea实例来模拟主界面
    content_area = ContentArea()
    
    # 模拟parent_window结构
    class MockParentWindow:
        def __init__(self, model):
            self.model = model
    
    content_area.parent_window = MockParentWindow(model)
    
    # 模拟切换到回收站页面的操作
    print("🔄 切换到回收站页面...")
    content_area.switch_to_page("trash")
    
    # 检查回收站页面是否正确显示数据
    if hasattr(content_area, 'trash_page'):
        trash_prompts = content_area.trash_page.trash_prompts
        print(f"📋 回收站页面显示的提示词数量: {len(trash_prompts)}")
        
        # 检查我们删除的提示词是否在回收站中显示
        deleted_prompt_found = any(p.id == test_prompt.id for p in trash_prompts)
        
        if deleted_prompt_found:
            print(f"✅ 删除的提示词 (ID:{test_prompt.id}) 在回收站中正确显示！")
            print("🎉 删除功能完全正常工作！")
        else:
            print(f"❌ 删除的提示词 (ID:{test_prompt.id}) 未在回收站中显示")
            print("🐛 发现bug：回收站页面未正确显示新删除的提示词")
    else:
        print("❌ 无法访问回收站页面")
        
    # 6. 恢复测试数据
    print(f"\n🔄 恢复测试数据...")
    model.restore_prompt(test_prompt.id)
    print(f"✅ 测试数据已恢复")
    
else:
    print("❌ 删除操作失败，提示词未移入回收站")

print("\n" + "=" * 60)
print("🏁 测试完成") 