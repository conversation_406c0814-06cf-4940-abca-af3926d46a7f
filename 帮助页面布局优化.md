# 帮助页面布局优化

## Core Features

- 采用固定导航栏和可滚动内容区的两栏式布局

- 解决内容溢出问题，确保所有帮助信息完整显示

- 提供清晰的交互式导航，方便用户切换帮助主题

## Tech Stack

{
  "Web": {
    "arch": "react",
    "component": "shadcn"
  }
}

## Design

界面采用现代简约风格，通过清晰的两栏布局优化用户体验。左侧为固定宽度的导航栏，方便快速定位主题；右侧为独立的可滚动内容区，确保长篇幅的帮助文档也能完整展示，不会出现内容被截断的情况。整体设计注重可读性和易用性。

## Plan

Note: 

- [ ] is holding
- [/] is doing
- [X] is done

---

[ ] 创建 `HelpPage` React 组件的基本文件结构。

[ ] 使用 CSS Flexbox 或 Grid 实现页面的两栏布局，划分左侧导航栏和右侧内容区。

[ ] 在左侧导航栏中，使用 shadcn/ui 组件构建一个可交互的垂直主题列表。

[ ] 为右侧内容区设置 `overflow-y: auto` 样式，确保内容超出时可独立滚动。

[ ] 实现组件状态管理，以根据左侧导航栏的选择动态渲染右侧的帮助内容。

[ ] 使用 shadcn/ui 的 `Card` 和排版组件来格式化和美化右侧的帮助文档内容。
