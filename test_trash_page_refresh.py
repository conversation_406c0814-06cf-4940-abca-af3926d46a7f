#!/usr/bin/env python3
"""
测试回收站页面的刷新功能
"""

from PySide6.QtWidgets import QApplication
import sys
from model import PromptModel
from pages.trash_page import TrashPage

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🗑️ 测试回收站页面刷新功能")
print("=" * 50)

# 创建模型
model = PromptModel()

# 获取删除的提示词数量
deleted_prompts = model.get_deleted_prompts()
print(f"📊 当前回收站中的提示词数量: {len(deleted_prompts)}")

# 创建TrashPage实例
print("🏗️ 创建TrashPage实例...")
trash_page = TrashPage(None)

# 检查refresh方法是否存在
has_refresh = hasattr(trash_page, 'refresh')
print(f"🔍 TrashPage是否有refresh方法: {has_refresh}")

if has_refresh:
    print("✅ 调用refresh方法...")
    trash_page.refresh()
    print("✅ refresh方法调用完成")
else:
    print("❌ TrashPage没有refresh方法！")
    
    # 检查其他可能的刷新方法
    print("🔍 检查其他可能的刷新方法...")
    has_load_data = hasattr(trash_page, 'load_data_with_model')
    print(f"   有load_data_with_model方法: {has_load_data}")
    
    if has_load_data:
        print("✅ 调用load_data_with_model方法...")
        trash_page.load_data_with_model(model=model)
        print("✅ load_data_with_model方法调用完成")

print("\n" + "=" * 50)
print("🏁 测试完成") 