import sqlite3

def check_auxiliary_fields():
    conn = sqlite3.connect('prompts.db')
    cursor = conn.cursor()
    
    # 检查表结构
    cursor.execute("PRAGMA table_info(prompts)")
    columns = [column[1] for column in cursor.fetchall()]
    print("prompts表的列:", columns)
    
    # 检查是否有auxiliary_fields数据
    cursor.execute("SELECT id, title, auxiliary_fields FROM prompts WHERE auxiliary_fields IS NOT NULL AND auxiliary_fields != '' LIMIT 5")
    rows = cursor.fetchall()
    print(f"\n有auxiliary_fields数据的记录数量: {len(rows)}")
    
    for row in rows:
        print(f"ID: {row[0]}, 标题: {row[1]}, 辅助字段: {row[2]}")
    
    # 检查所有记录的auxiliary_fields
    cursor.execute("SELECT id, title, auxiliary_fields FROM prompts LIMIT 10")
    rows = cursor.fetchall()
    print(f"\n前10条记录的auxiliary_fields:")
    
    for row in rows:
        print(f"ID: {row[0]}, 标题: {row[1]}, 辅助字段: {row[2]}")
    
    conn.close()

if __name__ == "__main__":
    check_auxiliary_fields() 