#!/usr/bin/env python3
"""
测试按钮事件连接情况
"""

from PySide6.QtWidgets import QApplication
import sys

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("开始测试按钮事件...")

# 测试1: 检查ButtonFactory创建的按钮
from components.button_factory import ButtonFactory

# 创建一个收藏按钮测试
normal_svg = '<svg>test</svg>'
active_svg = '<svg>test_active</svg>'

btn = ButtonFactory.create_favorite_button(normal_svg, active_svg, "test", False)
print(f"ButtonFactory按钮属性:")
print(f"  is_favorited: {getattr(btn, 'is_favorited', '未设置')}")
print(f"  normal_svg: {getattr(btn, 'normal_svg', '未设置')}")
print(f"  active_svg: {getattr(btn, 'active_svg', '未设置')}")

# 测试2: 检查断开连接是否有效
print(f"连接前信号数量: {len(btn.clicked.receivers())}")
btn.clicked.disconnect()
print(f"断开后信号数量: {len(btn.clicked.receivers())}")

# 测试3: 重新连接
def test_handler():
    print("测试处理器被调用")

btn.clicked.connect(test_handler)
print(f"重连后信号数量: {len(btn.clicked.receivers())}")

# 测试4: 模拟点击
print("模拟点击按钮...")
btn.clicked.emit()

print("测试完成!") 