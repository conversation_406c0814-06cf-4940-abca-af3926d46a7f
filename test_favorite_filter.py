#!/usr/bin/env python3
"""
测试收藏筛选功能
"""
import sys
from PySide6.QtWidgets import QApplication
from model import PromptModel, Prompt
from main import PromptAssistantRedesigned

def create_test_data():
    """创建测试数据"""
    model = PromptModel("sqlite", "test_prompts.db")
    
    # 清除现有数据
    try:
        model.storage.cursor.execute("DELETE FROM prompts")
        model.storage.connection.commit()
    except:
        pass
    
    # 创建测试提示词
    test_prompts = [
        Prompt(
            title="收藏的AI绘画提示词",
            content="创建一个美丽的风景画，包含山川河流",
            category="AI绘画",
            tags=["绘画", "风景"],
            is_favorite=1
        ),
        Prompt(
            title="普通文案写作提示词",
            content="帮我写一篇关于科技发展的文章",
            category="文案写作",
            tags=["写作", "科技"],
            is_favorite=0
        ),
        Prompt(
            title="收藏的代码生成提示词",
            content="生成一个Python函数来处理数据",
            category="代码生成",
            tags=["编程", "Python"],
            is_favorite=1
        ),
        Prompt(
            title="普通学习辅助提示词",
            content="解释量子物理的基本概念",
            category="学习辅助",
            tags=["学习", "物理"],
            is_favorite=0
        ),
        Prompt(
            title="收藏的创意设计提示词",
            content="设计一个现代简约风格的logo",
            category="创意设计",
            tags=["设计", "logo"],
            is_favorite=1
        )
    ]
    
    # 添加到数据库
    for prompt in test_prompts:
        model.add_prompt(prompt)
    
    print("测试数据创建完成！")
    print(f"总共创建了 {len(test_prompts)} 个提示词")
    print(f"其中 {sum(1 for p in test_prompts if p.is_favorite == 1)} 个是收藏的")
    
    return model

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试数据
    create_test_data()
    
    # 启动应用
    window = PromptAssistantRedesigned()
    window.show()
    
    print("\n=== 收藏筛选功能测试说明 ===")
    print("1. 应用启动后会显示所有5个提示词")
    print("2. 点击工具栏中的收藏筛选按钮（心形图标）")
    print("3. 激活后应该只显示3个收藏的提示词")
    print("4. 再次点击可以取消筛选，显示所有提示词")
    print("5. 状态栏会显示筛选状态和数量信息")
    
    sys.exit(app.exec())

if __name__ == '__main__':
    main()