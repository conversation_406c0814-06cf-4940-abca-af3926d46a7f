#!/usr/bin/env python3
"""
生成大量测试提示词数据
"""
import sqlite3
import random
from datetime import datetime, timed<PERSON>ta

def create_test_data():
    """创建大量测试数据"""
    
    # 连接数据库
    conn = sqlite3.connect('prompts.db')
    cursor = conn.cursor()
    
    # 创建表（如果不存在）
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS prompts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT NOT NULL,
            category TEXT DEFAULT '默认',
            tags TEXT DEFAULT '',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_favorite INTEGER DEFAULT 0,
            is_pinned INTEGER DEFAULT 0,
            has_media INTEGER DEFAULT 0
        )
    ''')
    
    # 清除现有数据
    cursor.execute('DELETE FROM prompts')
    
    # 测试数据模板
    categories = ['AI助手', '写作创意', '编程开发', '商务办公', '学习教育', '生活娱乐', '设计创作', '数据分析']
    
    titles_templates = [
        "高效{category}助手",
        "专业{category}指导",
        "创意{category}方案",
        "智能{category}工具",
        "实用{category}技巧",
        "深度{category}分析",
        "全面{category}解决方案",
        "个性化{category}建议",
        "系统化{category}流程",
        "优化{category}策略"
    ]
    
    content_templates = [
        "这是一个专门用于{category}的高效提示词，能够帮助用户快速完成相关任务。通过精心设计的指令结构，可以获得更准确、更有用的回答。适用于各种复杂场景，提供专业级别的输出质量。",
        "针对{category}领域的专业提示词模板，包含详细的步骤指导和最佳实践。经过大量测试验证，能够显著提升工作效率和输出质量。支持多种使用场景和个性化定制。",
        "创新设计的{category}提示词，融合了行业最佳实践和前沿理念。具有强大的适应性和扩展性，能够满足不同用户的个性化需求。提供清晰的使用指南和优化建议。",
        "经过精心优化的{category}专用提示词，采用结构化设计理念，确保输出结果的一致性和可靠性。包含丰富的示例和使用技巧，帮助用户快速上手并掌握核心要点。",
        "面向{category}的智能提示词系统，集成了多种高级功能和优化策略。通过模块化设计，用户可以根据具体需求进行灵活配置。提供完整的文档说明和技术支持。"
    ]
    
    tags_pool = [
        ['AI', '智能', '自动化'],
        ['创意', '灵感', '设计'],
        ['效率', '优化', '工具'],
        ['专业', '深度', '分析'],
        ['实用', '便捷', '快速'],
        ['系统', '流程', '管理'],
        ['个性化', '定制', '灵活'],
        ['创新', '前沿', '技术'],
        ['质量', '可靠', '稳定'],
        ['学习', '教育', '指导']
    ]
    
    # 生成50个测试提示词
    test_prompts = []
    for i in range(50):
        category = random.choice(categories)
        title_template = random.choice(titles_templates)
        content_template = random.choice(content_templates)
        tags = random.choice(tags_pool)
        
        title = title_template.format(category=category)
        content = content_template.format(category=category)
        tags_str = ','.join(tags)
        
        # 随机设置一些属性
        is_favorite = random.choice([0, 0, 0, 1])  # 25%概率为收藏
        is_pinned = random.choice([0, 0, 0, 0, 1])  # 20%概率为置顶
        has_media = random.choice([0, 0, 0, 1])     # 25%概率有媒体
        
        # 随机创建时间（最近30天内）
        days_ago = random.randint(0, 30)
        created_at = datetime.now() - timedelta(days=days_ago)
        
        test_prompts.append((
            title, content, category, tags_str,
            created_at.isoformat(), created_at.isoformat(),
            is_favorite, is_pinned, has_media
        ))
    
    # 插入测试数据
    cursor.executemany('''
        INSERT INTO prompts (title, content, category, tags, created_at, updated_at, is_favorite, is_pinned, has_media)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', test_prompts)
    
    conn.commit()
    conn.close()
    
    print(f"✅ 成功生成 {len(test_prompts)} 个测试提示词数据")
    print("📊 数据分布：")
    print(f"   - 分类：{len(categories)} 种")
    print(f"   - 收藏：约 {len([p for p in test_prompts if p[6]])} 个")
    print(f"   - 置顶：约 {len([p for p in test_prompts if p[7]])} 个") 
    print(f"   - 媒体：约 {len([p for p in test_prompts if p[8]])} 个")

if __name__ == '__main__':
    create_test_data()