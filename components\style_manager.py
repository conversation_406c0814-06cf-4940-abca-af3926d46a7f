#!/usr/bin/env python3
"""
样式管理器模块
提供统一的样式管理功能，消除重复的样式设置代码
"""
from PySide6.QtWidgets import QWidget
from typing import Dict, Optional


class StyleManager:
    """样式管理器类，提供统一的样式管理方法"""
    
    @staticmethod
    def apply_transparent_background(widget: QWidget) -> None:
        """为组件应用透明背景 - 复制自main.py中重复出现的样式"""
        widget.setStyleSheet("background-color: transparent;")
    
    @staticmethod
    def get_card_style(selected: bool = False) -> str:
        """获取卡片样式字符串"""
        if selected:
            return """
                background-color: #EBF4FF;
                border: 2px solid #3B82F6;
                border-radius: 8px;
            """
        else:
            return """
                background-color: white;
                border: 1px solid #E5E7EB;
                border-radius: 8px;
            """
    
    @staticmethod
    def apply_card_style(widget: QWidget, selected: bool = False) -> str:
        """应用卡片样式"""
        if selected:
            return """
                QFrame {
                    background-color: #EBF4FF;
                    border: 2px solid #3B82F6;
                    border-radius: 8px;
                }
            """
        else:
            return """
                QFrame {
                    background-color: white;
                    border: 1px solid #E5E7EB;
                    border-radius: 8px;
                }
                QFrame:hover {
                    border-color: #D1D5DB;
                    background-color: #F9FAFB;
                }
            """
    
    @staticmethod
    def get_button_style(button_type: str, state: str = "default") -> str:
        """获取按钮样式"""
        base_styles = {
            "small_icon": """
                QPushButton {
                    background-color: transparent;
                    border: none;
                    border-radius: 2px;
                    padding: 2px;
                }
                QPushButton:hover {
                    background-color: rgba(243, 244, 246, 0.8);
                }
                QPushButton:pressed {
                    background-color: rgba(229, 231, 235, 0.8);
                }
                QToolTip {
                    background-color: #374151;
                    color: white;
                    border: 1px solid #4B5563;
                    border-radius: 4px;
                    padding: 4px;
                    font-size: 11px;
                }
            """,
            "toggle": """
                QPushButton {
                    background-color: transparent;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 8px 12px;
                    font-size: 12px;
                    color: #374151;
                }
                QPushButton:hover {
                    background-color: #F3F4F6;
                    border-color: #9CA3AF;
                }
                QPushButton:pressed {
                    background-color: #E5E7EB;
                }
            """,
            "active_toggle": """
                QPushButton {
                    background-color: #3B82F6;
                    border: 1px solid #3B82F6;
                    border-radius: 6px;
                    padding: 8px 12px;
                    font-size: 12px;
                    color: white;
                }
                QPushButton:hover {
                    background-color: #2563EB;
                    border-color: #2563EB;
                }
                QPushButton:pressed {
                    background-color: #1D4ED8;
                }
            """,
            "secondary": """
                QPushButton {
                    background-color: #F3F4F6;
                    border: 1px solid #D1D5DB;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-size: 12px;
                    color: #374151;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #E5E7EB;
                    border-color: #9CA3AF;
                }
                QPushButton:pressed {
                    background-color: #D1D5DB;
                }
                QPushButton:disabled {
                    background-color: #F9FAFB;
                    color: #9CA3AF;
                    border-color: #F3F4F6;
                }
            """,
            "danger": """
                QPushButton {
                    background-color: #EF4444;
                    border: 1px solid #EF4444;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-size: 12px;
                    color: white;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #DC2626;
                    border-color: #DC2626;
                }
                QPushButton:pressed {
                    background-color: #B91C1C;
                }
                QPushButton:disabled {
                    background-color: #F9FAFB;
                    color: #9CA3AF;
                    border-color: #F3F4F6;
                }
            """,
            "danger_outline": """
                QPushButton {
                    background-color: transparent;
                    border: 1px solid #EF4444;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-size: 12px;
                    color: #EF4444;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #FEF2F2;
                    border-color: #DC2626;
                    color: #DC2626;
                }
                QPushButton:pressed {
                    background-color: #FEE2E2;
                    color: #B91C1C;
                    border-color: #B91C1C;
                }
                QPushButton:disabled {
                    background-color: transparent;
                    color: #D1D5DB;
                    border-color: #F3F4F6;
                }
            """
        }
        
        return base_styles.get(button_type, base_styles["small_icon"])
    
    @staticmethod
    def get_tooltip_style() -> str:
        """获取统一的工具提示样式"""
        return """
            QToolTip {
                background-color: #374151;
                color: white;
                border: 1px solid #4B5563;
                border-radius: 4px;
                padding: 4px;
                font-size: 11px;
            }
        """
    
    @staticmethod
    def get_scrollbar_style() -> str:
        """获取现代化滚动条样式"""
        return """
            /* 现代化滚动条样式 */
            QScrollBar:vertical {
                background: transparent;
                width: 8px;
                margin: 0px;
                border-radius: 4px;
            }
            
            QScrollBar::handle:vertical {
                background: #D1D5DB;
                border-radius: 4px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: #9CA3AF;
            }
            
            QScrollBar::handle:vertical:pressed {
                background: #6B7280;
            }
            
            QScrollBar::add-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::sub-line:vertical {
                height: 0px;
                background: transparent;
            }
            
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {
                background: transparent;
            }
            
            QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {
                height: 0px;
                background: transparent;
            }
        """
        
    @staticmethod
    def get_dialog_style(dialog_type: str = 'default') -> str:
        """获取对话框样式"""
        return """
            QDialog {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 8px;
            }
        """
        
    @staticmethod
    def get_combobox_style() -> str:
        """获取下拉框样式"""
        return """
            QComboBox {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                padding: 4px 8px;
                font-size: 12px;
                min-width: 100px;
            }
            QComboBox:hover {
                border-color: #9CA3AF;
            }
            QComboBox:focus {
                border-color: #3B82F6;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            QComboBox QAbstractItemView {
                background-color: white;
                border: 1px solid #D1D5DB;
                border-radius: 4px;
                selection-background-color: #EBF4FF;
                selection-color: #1E40AF;
            }
        """ 