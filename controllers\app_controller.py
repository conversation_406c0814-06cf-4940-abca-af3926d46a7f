#!/usr/bin/env python3
"""
应用程序控制器
负责协调模型和视图的交互，处理业务逻辑
"""
from typing import List, Dict, Any, Optional, Set, Tuple
import os
import shutil
from datetime import datetime

from PySide6.QtCore import QObject, Signal, Slot, Qt
from PySide6.QtWidgets import QMessageBox

from model import PromptModel, Prompt
from interfaces.service_interfaces import IPromptService, ITagService, ICategoryService, IColorService
from utils.error_handler import log_error, show_error_dialog, AppError, DataError, DatabaseError
from utils.event_bus import EventType, publish_event, Event

# 应用程序状态，可以扩展为专门的状态管理类
class AppState:
    """应用程序状态类"""
    
    def __init__(self):
        # 当前活动页面
        self.active_page = "home"
        
        # 当前视图类型
        self.view_type = "card"
        
        # 当前选中的提示词ID
        self.selected_prompt_ids = set()
        
        # 筛选条件
        self.filters = {
            'category': '全部',
            'tags': set(),
            'is_favorite': False,
            'search_text': ""
        }
        
        # 排序方式
        self.sort_method = "updated_desc"


class AppController(QObject):
    """应用程序控制器"""
    
    # 定义信号，用于更新UI
    prompts_updated = Signal(list)  # 提示词列表更新
    filter_updated = Signal(dict)   # 筛选条件更新
    status_message = Signal(str)    # 状态栏消息
    
    def __init__(self, 
                 prompt_service: IPromptService,
                 tag_service: ITagService,
                 category_service: ICategoryService,
                 color_service: IColorService):
        super().__init__()
        
        # 注入的服务
        self.prompt_service = prompt_service
        self.tag_service = tag_service
        self.category_service = category_service
        self.color_service = color_service
        
        # 获取数据模型（从prompt_service获取）
        self.model = getattr(prompt_service, 'model', None)
        
        # 应用程序状态
        self.state = AppState()
        
        # 设置事件处理
        self._setup_event_handlers()
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        # 在实际应用中，可以使用utils/event_bus.py中的subscribe装饰器
        # 这里为了清晰展示所有处理的事件，直接使用方法调用
        from utils.event_bus import event_bus
        
        # 提示词相关事件
        event_bus.subscribe(EventType.PROMPT_CREATED, self._on_prompt_created)
        event_bus.subscribe(EventType.PROMPT_UPDATED, self._on_prompt_updated)
        event_bus.subscribe(EventType.PROMPT_DELETED, self._on_prompt_deleted)
        event_bus.subscribe(EventType.PROMPT_SELECTED, self._on_prompt_selected)
        event_bus.subscribe(EventType.PROMPT_DESELECTED, self._on_prompt_deselected)
        event_bus.subscribe(EventType.PROMPT_FAVORITED, self._on_prompt_favorited)
        event_bus.subscribe(EventType.PROMPT_UNFAVORITED, self._on_prompt_unfavorited)
        
        # 筛选器相关事件
        event_bus.subscribe(EventType.FILTER_CHANGED, self._on_filter_changed)
        event_bus.subscribe(EventType.CATEGORY_FILTER_CHANGED, self._on_category_filter_changed)
        event_bus.subscribe(EventType.TAG_FILTER_CHANGED, self._on_tag_filter_changed)
        event_bus.subscribe(EventType.FAVORITE_FILTER_CHANGED, self._on_favorite_filter_changed)
        event_bus.subscribe(EventType.SEARCH_FILTER_CHANGED, self._on_search_filter_changed)
        
        # 视图相关事件
        event_bus.subscribe(EventType.VIEW_CHANGED, self._on_view_changed)
        event_bus.subscribe(EventType.PAGE_CHANGED, self._on_page_changed)
    
    #----------------------------------------------------------------------
    # 事件处理方法
    #----------------------------------------------------------------------
    
    def _on_prompt_created(self, event: Event):
        """处理提示词创建事件"""
        prompt_id = event.data
        try:
            self._update_prompt_list()
            self.status_message.emit(f"已创建提示词")
        except Exception as e:
            log_error(e)
    
    def _on_prompt_updated(self, event: Event):
        """处理提示词更新事件"""
        prompt_id = event.data
        try:
            self._update_prompt_list()
            self.status_message.emit(f"已更新提示词")
        except Exception as e:
            log_error(e)
    
    def _on_prompt_deleted(self, event: Event):
        """处理提示词删除事件"""
        prompt_id = event.data
        try:
            # 从选中集合中移除已删除的提示词
            if prompt_id in self.state.selected_prompt_ids:
                self.state.selected_prompt_ids.remove(prompt_id)
            
            self._update_prompt_list()
            self.status_message.emit(f"已删除提示词")
        except Exception as e:
            log_error(e)
    
    def _on_prompt_selected(self, event: Event):
        """处理提示词选中事件"""
        prompt_id = event.data
        self.state.selected_prompt_ids.add(prompt_id)
        self._update_selection_status()
    
    def _on_prompt_deselected(self, event: Event):
        """处理提示词取消选中事件"""
        prompt_id = event.data
        if prompt_id in self.state.selected_prompt_ids:
            self.state.selected_prompt_ids.remove(prompt_id)
        self._update_selection_status()
    
    def _on_prompt_favorited(self, event: Event):
        """处理提示词收藏事件"""
        prompt_id = event.data
        try:
            self._update_prompt_list()
            self.status_message.emit(f"已收藏提示词")
        except Exception as e:
            log_error(e)
    
    def _on_prompt_unfavorited(self, event: Event):
        """处理提示词取消收藏事件"""
        prompt_id = event.data
        try:
            self._update_prompt_list()
            self.status_message.emit(f"已取消收藏提示词")
        except Exception as e:
            log_error(e)
    
    def _on_filter_changed(self, event: Event):
        """处理筛选条件变化事件"""
        filters = event.data
        self.state.filters.update(filters)
        self._update_filter_ui()
        self._update_prompt_list()
    
    def _on_category_filter_changed(self, event: Event):
        """处理分类筛选变化事件"""
        category = event.data
        self.state.filters['category'] = category
        self._update_filter_ui()
        self._update_prompt_list()
        
        # 更新状态栏消息
        if category == "全部":
            self.status_message.emit("显示所有分类")
        else:
            self.status_message.emit(f"筛选分类: {category}")
    
    def _on_tag_filter_changed(self, event: Event):
        """处理标签筛选变化事件"""
        tags_data = event.data
        if isinstance(tags_data, str):
            # 单个标签切换
            tag = tags_data
            if tag in self.state.filters['tags']:
                self.state.filters['tags'].remove(tag)
            else:
                self.state.filters['tags'].add(tag)
        elif isinstance(tags_data, (list, set)):
            # 设置整个标签集合
            self.state.filters['tags'] = set(tags_data)
        
        self._update_filter_ui()
        self._update_prompt_list()
        
        # 更新状态栏消息
        tags = self.state.filters['tags']
        if not tags:
            self.status_message.emit("显示所有标签")
        elif len(tags) == 1:
            self.status_message.emit(f"筛选标签: {next(iter(tags))}")
        else:
            self.status_message.emit(f"筛选多个标签: {len(tags)}个")
    
    def _on_favorite_filter_changed(self, event: Event):
        """处理收藏筛选变化事件"""
        is_favorite = event.data
        self.state.filters['is_favorite'] = is_favorite
        self._update_filter_ui()
        self._update_prompt_list()
        
        # 更新状态栏消息
        if is_favorite:
            self.status_message.emit("显示收藏的提示词")
        else:
            self.status_message.emit("显示所有提示词")
    
    def _on_search_filter_changed(self, event: Event):
        """处理搜索筛选变化事件"""
        search_text = event.data
        self.state.filters['search_text'] = search_text
        self._update_prompt_list()
        
        # 更新状态栏消息
        if search_text:
            self.status_message.emit(f"搜索: {search_text}")
        else:
            self.status_message.emit("清除搜索")
    
    def _on_view_changed(self, event: Event):
        """处理视图类型变化事件"""
        view_type = event.data
        self.state.view_type = view_type
        self.status_message.emit(f"切换到{view_type}视图")
    
    def _on_page_changed(self, event: Event):
        """处理页面切换事件"""
        page = event.data
        self.state.active_page = page
        
        # 清除选中的提示词
        self.state.selected_prompt_ids.clear()
        
        # 根据页面类型加载不同的数据
        if page == "favorite":
            self.state.filters['is_favorite'] = True
        else:
            self.state.filters['is_favorite'] = False
            
        self._update_filter_ui()
        self._update_prompt_list()
        
        # 更新状态栏
        page_names = {
            "home": "主页",
            "favorite": "收藏",
            "trash": "回收站",
            "settings": "设置",
            "help": "帮助"
        }
        self.status_message.emit(f"切换到{page_names.get(page, page)}页面")
    
    #----------------------------------------------------------------------
    # UI更新方法
    #----------------------------------------------------------------------
    
    def _update_prompt_list(self):
        """更新提示词列表"""
        try:
            # 应用筛选和排序
            filtered_prompts = self.prompt_service.filter_prompts(self.state.filters)
            sorted_prompts = self.prompt_service.sort_prompts(filtered_prompts, self.state.sort_method)
            
            # 发送信号更新UI
            self.prompts_updated.emit(sorted_prompts)
            
        except Exception as e:
            log_error(e)
            # 出错时显示空列表
            self.prompts_updated.emit([])
    
    def _update_filter_ui(self):
        """更新筛选UI状态"""
        self.filter_updated.emit(self.state.filters)
    
    def _update_selection_status(self):
        """更新选择状态"""
        count = len(self.state.selected_prompt_ids)
        if count == 0:
            self.status_message.emit("未选择提示词")
        elif count == 1:
            self.status_message.emit(f"已选择1个提示词")
        else:
            self.status_message.emit(f"已选择{count}个提示词")
    
    #----------------------------------------------------------------------
    # 公共方法 - 提示词操作
    #----------------------------------------------------------------------
    
    def load_initial_data(self):
        """加载初始数据"""
        try:
            self._update_prompt_list()
            self.status_message.emit("数据加载完成")
        except Exception as e:
            log_error(e)
            self.status_message.emit("数据加载失败")
    
    def create_prompt(self, prompt_data: Dict[str, Any]) -> int:
        """
        创建新的提示词
        :param prompt_data: 提示词数据字典
        :return: 新创建的提示词ID
        """
        try:
            # 创建Prompt对象
            prompt = Prompt(
                title=prompt_data.get('title', ''),
                content=prompt_data.get('content', ''),
                tags=prompt_data.get('tags', []),
                category=prompt_data.get('category', ''),
                media_files=prompt_data.get('media_files', []),
                type=prompt_data.get('type', '文本')
            )
            
            # 标准化处理
            prompt.tags = self.tag_service.normalize_tags(prompt.tags)
            prompt.category = self.category_service.normalize_category(prompt.category)
            
            # 保存提示词
            prompt_id = self.prompt_service.create_prompt(prompt)
            
            return prompt_id
        except DataError as e:
            show_error_dialog("数据错误", str(e))
            raise
        except Exception as e:
            log_error(e)
            show_error_dialog("创建提示词失败", str(e))
            raise
    
    def update_prompt(self, prompt_id: int, prompt_data: Dict[str, Any]) -> bool:
        """
        更新提示词
        :param prompt_id: 提示词ID
        :param prompt_data: 提示词数据字典
        :return: 是否成功更新
        """
        try:
            # 获取现有提示词
            prompt = self.prompt_service.get_prompt(prompt_id)
            if not prompt:
                raise DataError(f"找不到ID为{prompt_id}的提示词")
            
            # 更新提示词字段
            if 'title' in prompt_data:
                prompt.title = prompt_data['title']
            if 'content' in prompt_data:
                prompt.content = prompt_data['content']
            if 'tags' in prompt_data:
                prompt.tags = self.tag_service.normalize_tags(prompt_data['tags'])
            if 'category' in prompt_data:
                prompt.category = self.category_service.normalize_category(prompt_data['category'])
            if 'media_files' in prompt_data:
                prompt.media_files = prompt_data['media_files']
            if 'type' in prompt_data:
                prompt.type = prompt_data['type']
            
            # 保存更新
            return self.prompt_service.update_prompt(prompt)
        except DataError as e:
            show_error_dialog("数据错误", str(e))
            raise
        except Exception as e:
            log_error(e)
            show_error_dialog("更新提示词失败", str(e))
            raise
    
    def delete_prompt(self, prompt_id: int) -> bool:
        """
        删除提示词
        :param prompt_id: 提示词ID
        :return: 是否成功删除
        """
        try:
            return self.prompt_service.delete_prompt(prompt_id)
        except Exception as e:
            log_error(e)
            show_error_dialog("删除提示词失败", str(e))
            raise
    
    def delete_selected_prompts(self) -> int:
        """
        删除选中的提示词
        :return: 删除的提示词数量
        """
        count = 0
        for prompt_id in list(self.state.selected_prompt_ids):
            try:
                if self.prompt_service.delete_prompt(prompt_id):
                    count += 1
            except Exception as e:
                log_error(e)
        
        # 清空选中集合
        self.state.selected_prompt_ids.clear()
        
        return count
    
    def toggle_favorite(self, prompt_id: int) -> bool:
        """
        切换提示词的收藏状态
        :param prompt_id: 提示词ID
        :return: 新的收藏状态
        """
        try:
            return self.prompt_service.toggle_favorite(prompt_id)
        except Exception as e:
            log_error(e)
            show_error_dialog("切换收藏状态失败", str(e))
            raise
    
    def toggle_pin(self, prompt_id: int) -> bool:
        """
        切换提示词的置顶状态
        :param prompt_id: 提示词ID
        :return: 新的置顶状态
        """
        try:
            return self.prompt_service.toggle_pin(prompt_id)
        except Exception as e:
            log_error(e)
            show_error_dialog("切换置顶状态失败", str(e))
            raise
    
    #----------------------------------------------------------------------
    # 公共方法 - 筛选和排序
    #----------------------------------------------------------------------
    
    def apply_category_filter(self, category: str):
        """
        应用分类筛选
        :param category: 分类名称
        """
        publish_event(EventType.CATEGORY_FILTER_CHANGED, category)
    
    def apply_tag_filter(self, tag: str):
        """
        应用标签筛选（切换单个标签）
        :param tag: 标签名称
        """
        publish_event(EventType.TAG_FILTER_CHANGED, tag)
    
    def set_tag_filters(self, tags: List[str]):
        """
        设置标签筛选（一次性设置多个标签）
        :param tags: 标签列表
        """
        publish_event(EventType.TAG_FILTER_CHANGED, set(tags))
    
    def toggle_favorite_filter(self):
        """切换收藏筛选状态"""
        is_favorite = not self.state.filters['is_favorite']
        publish_event(EventType.FAVORITE_FILTER_CHANGED, is_favorite)
    
    def apply_search_filter(self, search_text: str):
        """
        应用搜索筛选
        :param search_text: 搜索关键词
        """
        publish_event(EventType.SEARCH_FILTER_CHANGED, search_text)
    
    def set_sort_method(self, method: str):
        """
        设置排序方式
        :param method: 排序方法
                      updated_desc: 按更新时间降序
                      created_desc: 按创建时间降序
                      title_asc: 按标题升序
                      category_asc: 按分类升序
        """
        self.state.sort_method = method
        self._update_prompt_list()
    
    def clear_filters(self):
        """清除所有筛选条件"""
        self.state.filters = {
            'category': '全部',
            'tags': set(),
            'is_favorite': False,
            'search_text': ""
        }
        self._update_filter_ui()
        self._update_prompt_list()
        self.status_message.emit("清除所有筛选条件")
    
    def switch_view(self, view_type: str):
        """
        切换视图类型
        :param view_type: 视图类型，例如 "card" 或 "list"
        """
        publish_event(EventType.VIEW_CHANGED, view_type)
    
    def switch_page(self, page: str):
        """
        切换页面
        :param page: 页面名称，例如 "home", "favorite", "settings" 等
        """
        publish_event(EventType.PAGE_CHANGED, page)
    
    #----------------------------------------------------------------------
    # 公共方法 - 获取数据
    #----------------------------------------------------------------------
    
    def get_all_prompts(self) -> List[Prompt]:
        """获取所有提示词"""
        return self.prompt_service.get_all_prompts()
    
    def get_prompt(self, prompt_id: int) -> Optional[Prompt]:
        """获取特定提示词"""
        return self.prompt_service.get_prompt(prompt_id)
    
    def get_all_tags(self) -> List[str]:
        """获取所有标签"""
        return self.tag_service.get_all_tags()
    
    def get_all_categories(self) -> List[str]:
        """获取所有分类"""
        return self.category_service.get_all_categories()
    
    def get_tag_counts(self) -> Dict[str, int]:
        """获取标签使用次数"""
        return self.tag_service.get_tag_counts()
    
    def get_category_counts(self) -> Dict[str, int]:
        """获取分类使用次数"""
        return self.category_service.get_category_counts()
    
    def get_prompt_history(self, prompt_id: int) -> List[Any]:
        """获取提示词历史记录"""
        return self.prompt_service.get_prompt_history(prompt_id) 