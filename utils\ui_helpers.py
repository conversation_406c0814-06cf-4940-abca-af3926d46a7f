#!/usr/bin/env python3
"""
UI辅助模块
提供通用的UI组件和功能，减少代码重复
"""
from typing import Optional, Callable, Any, Dict, List, Tuple
import os
from pathlib import Path

from PySide6.QtWidgets import (QWidget, QPushButton, QLabel, QFrame, QVBoxLayout,
                              QHBoxLayout, QGraphicsDropShadowEffect, QToolTip,
                              QDialog, QMessageBox)
from PySide6.QtCore import Qt, QPoint, QSize, QRect, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QIcon, QPixmap, QPainter, QColor, QPalette, QBrush, QImage
from PySide6.QtSvg import QSvgRenderer

# 导入样式配置
try:
    from config.style_config import COLORS, BUTTON_STYLES, CARD_STYLES, INPUT_STYLES
except ImportError:
    # 如果无法导入，使用默认颜色
    COLORS = {
        "primary": "#0EA5E9",
        "primary_dark": "#0284C7",
        "white": "#FFFFFF",
        "gray_200": "#E5E7EB",
        "gray_300": "#D1D5DB",
        "gray_600": "#4B5563",
        "transparent": "transparent"
    }
    BUTTON_STYLES = {}
    CARD_STYLES = {}
    INPUT_STYLES = {}


def create_svg_icon(svg_content: str, color: str = None) -> QIcon:
    """
    从SVG内容创建图标
    :param svg_content: SVG内容字符串
    :param color: 颜色代码，如果提供，将替换SVG中的填充颜色
    :return: QIcon对象
    """
    # 如果需要替换颜色
    if color:
        # 替换SVG中的填充颜色
        if 'fill="' in svg_content:
            svg_content = svg_content.replace('fill="', f'fill="{color}" fill_old="')
        else:
            svg_content = svg_content.replace('<path ', f'<path fill="{color}" ')
    
    # 创建渲染器
    renderer = QSvgRenderer(bytes(svg_content, 'utf-8'))
    
    # 创建图片
    pixmap = QPixmap(64, 64)  # 使用合适的大小
    pixmap.fill(Qt.transparent)
    
    # 在图片上绘制SVG
    painter = QPainter(pixmap)
    renderer.render(painter)
    painter.end()
    
    # 创建图标并返回
    return QIcon(pixmap)


def set_button_svg_icon(button: QPushButton, svg_content: str, color: str = None):
    """
    设置按钮的SVG图标
    :param button: 按钮
    :param svg_content: SVG内容
    :param color: 图标颜色
    """
    icon = create_svg_icon(svg_content, color)
    button.setIcon(icon)
    button.setIconSize(QSize(16, 16))


def create_styled_button(text: str = "", style_key: str = "primary_button", 
                        icon_svg: str = None, size: QSize = None,
                        tooltip: str = None) -> QPushButton:
    """
    创建已应用样式的按钮
    :param text: 按钮文字
    :param style_key: 样式键名，对应style_config中的样式
    :param icon_svg: 图标SVG内容
    :param size: 按钮大小
    :param tooltip: 提示文本
    :return: 已设置样式的QPushButton
    """
    button = QPushButton(text)
    
    # 应用样式
    if style_key in BUTTON_STYLES:
        button.setStyleSheet(BUTTON_STYLES[style_key])
    
    # 设置图标
    if icon_svg:
        set_button_svg_icon(button, icon_svg)
    
    # 设置大小
    if size:
        button.setFixedSize(size)
    
    # 设置提示
    if tooltip:
        button.setToolTip(tooltip)
    
    return button


def create_shadow_effect(color: str = "#000000", blur_radius: int = 20,
                       x_offset: int = 0, y_offset: int = 0) -> QGraphicsDropShadowEffect:
    """
    创建阴影效果
    :param color: 阴影颜色
    :param blur_radius: 模糊半径
    :param x_offset: X轴偏移
    :param y_offset: Y轴偏移
    :return: 阴影效果对象
    """
    shadow = QGraphicsDropShadowEffect()
    shadow.setColor(QColor(color))
    shadow.setBlurRadius(blur_radius)
    shadow.setXOffset(x_offset)
    shadow.setYOffset(y_offset)
    return shadow


def create_horizontal_separator(height: int = 1, color: str = None) -> QFrame:
    """
    创建水平分隔线
    :param height: 分隔线高度
    :param color: 分隔线颜色
    :return: 分隔线对象
    """
    separator = QFrame()
    separator.setFrameShape(QFrame.HLine)
    separator.setFrameShadow(QFrame.Sunken)
    separator.setFixedHeight(height)
    
    if color:
        separator.setStyleSheet(f"background-color: {color};")
    else:
        separator.setStyleSheet(f"background-color: {COLORS.get('gray_200', '#E5E7EB')};")
    
    return separator


def confirm_dialog(parent: QWidget, title: str, message: str) -> bool:
    """
    显示确认对话框
    :param parent: 父窗口
    :param title: 对话框标题
    :param message: 对话框消息
    :return: 用户是否确认
    """
    reply = QMessageBox.question(
        parent, 
        title, 
        message,
        QMessageBox.Yes | QMessageBox.No,
        QMessageBox.No
    )
    return reply == QMessageBox.Yes


def center_widget(widget: QWidget, parent: QWidget = None):
    """
    将窗口居中显示
    :param widget: 要居中的窗口
    :param parent: 父窗口，如果不提供则相对于屏幕居中
    """
    if parent:
        # 相对于父窗口居中
        parent_rect = parent.geometry()
        widget_rect = widget.geometry()
        x = parent_rect.x() + (parent_rect.width() - widget_rect.width()) / 2
        y = parent_rect.y() + (parent_rect.height() - widget_rect.height()) / 2
        widget.move(int(x), int(y))
    else:
        # 相对于屏幕居中
        screen = QApplication.primaryScreen().geometry()
        widget_rect = widget.geometry()
        x = (screen.width() - widget_rect.width()) / 2
        y = (screen.height() - widget_rect.height()) / 2
        widget.move(int(x), int(y))


def create_animation(target: QWidget, property_name: bytes, 
                   start_value: Any, end_value: Any, 
                   duration: int = 300, easing_curve=QEasingCurve.InOutCubic):
    """
    创建属性动画
    :param target: 目标控件
    :param property_name: 属性名称
    :param start_value: 起始值
    :param end_value: 结束值
    :param duration: 持续时间(毫秒)
    :param easing_curve: 缓动曲线
    :return: 动画对象
    """
    animation = QPropertyAnimation(target, property_name)
    animation.setDuration(duration)
    animation.setStartValue(start_value)
    animation.setEndValue(end_value)
    animation.setEasingCurve(easing_curve)
    return animation


def scale_image(image_path: str, max_width: int, max_height: int) -> QPixmap:
    """
    缩放图片，保持比例
    :param image_path: 图片路径
    :param max_width: 最大宽度
    :param max_height: 最大高度
    :return: 缩放后的QPixmap
    """
    pixmap = QPixmap(image_path)
    if pixmap.width() > max_width or pixmap.height() > max_height:
        pixmap = pixmap.scaled(max_width, max_height, Qt.KeepAspectRatio, Qt.SmoothTransformation)
    return pixmap


def extract_video_thumbnail(video_path: str, max_size: tuple = (150, 150)) -> Optional[QPixmap]:
    """
    提取视频缩略图（第一帧）
    :param video_path: 视频文件路径
    :param max_size: 缩略图最大尺寸
    :return: 缩略图QPixmap或None
    """
    # 由于可能涉及到opencv导入问题，使用全局的导入检查
    global OPENCV_AVAILABLE
    
    if 'OPENCV_AVAILABLE' not in globals():
        try:
            import cv2
            OPENCV_AVAILABLE = True
        except ImportError:
            OPENCV_AVAILABLE = False
    
    if not OPENCV_AVAILABLE:
        return None
    
    try:
        import cv2
        import numpy as np
        
        # 打开视频文件
        cap = cv2.VideoCapture(video_path)
        
        # 检查是否成功打开
        if not cap.isOpened():
            print(f"无法打开视频文件: {video_path}")
            return None
        
        # 读取第一帧
        ret, frame = cap.read()
        
        # 释放视频对象
        cap.release()
        
        if not ret or frame is None:
            print(f"无法读取视频帧: {video_path}")
            return None
        
        # 将BGR格式转换为RGB格式
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # 调整大小
        height, width = rgb_frame.shape[:2]
        max_width, max_height = max_size
        
        # 计算缩放比例
        scale = min(max_width / width, max_height / height)
        
        if scale < 1:
            new_width = int(width * scale)
            new_height = int(height * scale)
            rgb_frame = cv2.resize(rgb_frame, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # 创建QImage
        height, width, channel = rgb_frame.shape
        bytes_per_line = channel * width
        q_img = QImage(rgb_frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
        
        # 创建QPixmap
        pixmap = QPixmap.fromImage(q_img)
        
        return pixmap
        
    except Exception as e:
        print(f"提取视频缩略图时出错: {e}")
        return None 