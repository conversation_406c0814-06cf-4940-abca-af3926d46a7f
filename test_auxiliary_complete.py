import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model import PromptModel, Prompt
from create_prompt_dialog import CreatePromptDialog
from PySide6.QtWidgets import QApplication

def test_auxiliary_complete():
    app = QApplication(sys.argv)
    
    model = PromptModel()
    
    # 创建一个模拟的父窗口，包含model属性
    from PySide6.QtWidgets import QWidget
    
    class MockParent(QWidget):
        def __init__(self, model):
            super().__init__()
            self.model = model
        
        def refresh_prompt_list(self):
            print("刷新提示词列表")
    
    mock_parent = MockParent(model)
    
    # 创建一个新的提示词，包含辅助选择字段
    prompt = Prompt(
        title="完整测试辅助选择",
        content="这是一个完整的测试内容",
        category="通用",
        tags=["测试", "辅助选择"],
        auxiliary_fields={
            "负向提示词": "这是负向提示词内容",
            "参数设置": "steps=20, cfg=7.5",
            "自定义字段": "这是自定义内容"
        }
    )
    
    # 保存到数据库
    prompt_id = model.add_prompt(prompt)
    print(f"保存的提示词ID: {prompt_id}")
    
    # 从数据库获取
    retrieved_prompt = model.get_prompt(prompt_id)
    print(f"从数据库获取的auxiliary_fields: {retrieved_prompt.auxiliary_fields}")
    
    # 创建编辑对话框
    dialog = CreatePromptDialog(parent=mock_parent, edit_prompt=retrieved_prompt)
    
    # 显示对话框
    dialog.show()
    
    # 运行对话框
    result = dialog.exec()
    
    if result == dialog.accepted:
        print("编辑成功")
        
        # 再次从数据库获取，查看是否保存成功
        updated_prompt = model.get_prompt(prompt_id)
        print(f"编辑后的auxiliary_fields: {updated_prompt.auxiliary_fields}")
    else:
        print("编辑取消")

if __name__ == "__main__":
    test_auxiliary_complete() 