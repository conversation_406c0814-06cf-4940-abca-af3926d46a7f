#!/usr/bin/env python3
"""
AI服务测试脚本
用于验证AI服务基础架构是否正常工作
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入是否正常"""
    print("=== 测试模块导入 ===")
    try:
        from config.ai_config import AI_PLATFORMS, DEFAULT_AI_PARAMS
        print("✓ AI配置导入成功")
        
        from services.api.base_adapter import BaseAdapter, AIRequest, AIResponse
        print("✓ 基础适配器导入成功")
        
        from services.api.openai_adapter import OpenAIAdapter, OpenAIAdapterSync
        print("✓ OpenAI适配器导入成功")
        
        from services.ai_service import AIService, ai_service
        print("✓ AI服务导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_config():
    """测试配置加载"""
    print("\n=== 测试配置 ===")
    try:
        from config.ai_config import AI_PLATFORMS
        
        print(f"支持的平台数量: {len(AI_PLATFORMS)}")
        for platform, config in AI_PLATFORMS.items():
            print(f"  - {platform}: {config['name']}")
            print(f"    模型数量: {len(config['models'])}")
            print(f"    默认模型: {config['default_model']}")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_ai_service_basic():
    """测试AI服务基础功能"""
    print("\n=== 测试AI服务基础功能 ===")
    try:
        from services.ai_service import ai_service
        
        # 测试获取平台信息
        platforms = list(ai_service._adapter_classes.keys())
        print(f"注册的适配器: {platforms}")
        
        # 测试获取可用平台（没有API密钥时应该为空）
        available = ai_service.get_available_platforms()
        print(f"可用平台: {available}")
        
        # 测试获取平台信息
        for platform in platforms:
            info = ai_service.get_platform_info(platform)
            print(f"{platform} 平台信息: {info.get('name', '未知')}")
        
        return True
    except Exception as e:
        print(f"✗ AI服务基础功能测试失败: {e}")
        return False

def test_openai_with_key():
    """测试OpenAI连接（需要提供API密钥）"""
    print("\n=== 测试OpenAI连接 ===")
    
    # 从环境变量或用户输入获取API密钥
    api_key = os.environ.get('OPENAI_API_KEY')
    if not api_key:
        print("未找到OPENAI_API_KEY环境变量")
        api_key = input("请输入OpenAI API密钥（可跳过）: ").strip()
    
    if not api_key:
        print("跳过OpenAI连接测试")
        return True
    
    try:
        from services.ai_service import ai_service
        
        # 保存API密钥
        ai_service.save_api_key('openai', api_key)
        print("✓ API密钥已保存")
        
        # 测试连接
        result = ai_service.test_connection('openai')
        if result['success']:
            print(f"✓ {result['message']}")
            print(f"  模型: {result['details']['model']}")
            print(f"  Tokens: {result['details']['tokens']}")
        else:
            print(f"✗ {result['message']}")
            print(f"  错误: {result.get('error', '未知错误')}")
        
        return result['success']
        
    except Exception as e:
        print(f"✗ OpenAI连接测试失败: {e}")
        return False

def test_simple_chat():
    """测试简单对话"""
    print("\n=== 测试简单对话 ===")
    
    try:
        from services.ai_service import ai_service
        
        # 检查是否有可用平台
        available = ai_service.get_available_platforms()
        if not available:
            print("没有可用的AI平台，跳过对话测试")
            return True
        
        platform = available[0]
        print(f"使用平台: {platform}")
        
        # 发送简单测试消息
        prompt = "请用一句话介绍一下人工智能。"
        print(f"发送消息: {prompt}")
        
        result = ai_service.chat_completion_sync(platform, prompt)
        
        if result['success']:
            print("✓ 对话成功")
            print(f"回复: {result['content'][:100]}...")
            usage = result.get('usage', {})
            if usage:
                print(f"Token使用: {usage.get('total_tokens', 0)}")
        else:
            print(f"✗ 对话失败: {result.get('error', '未知错误')}")
        
        return result['success']
        
    except Exception as e:
        print(f"✗ 对话测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("AI服务功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_import),
        ("配置加载", test_config),
        ("AI服务基础功能", test_ai_service_basic),
        ("OpenAI连接", test_openai_with_key),
        ("简单对话", test_simple_chat)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试 '{name}' 失败")
        except KeyboardInterrupt:
            print("\n测试被用户中断")
            break
        except Exception as e:
            print(f"测试 '{name}' 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！AI服务基础架构工作正常。")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")

if __name__ == "__main__":
    main() 