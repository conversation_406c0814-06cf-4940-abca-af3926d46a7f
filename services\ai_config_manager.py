#!/usr/bin/env python3
"""
AI配置管理器
负责用户AI平台配置的读写、管理和状态追踪
"""

import json
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional


class AIConfigManager:
    """AI配置管理器"""
    
    def __init__(self):
        self.config_file = Path("config/ai_user_config.json")
        self.user_config = self.load_user_config()
    
    def load_user_config(self) -> dict:
        """加载用户配置"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                print("配置文件读取失败，使用默认配置")
        
        return {
            'active_platforms': {},
            'global_params': {
                'temperature': 0.7,
                'max_tokens': 1000
            },
            'ui_preferences': {
                'preferred_platforms': [],
                'show_categories': True
            }
        }
    
    def save_user_config(self):
        """保存用户配置"""
        try:
            self.config_file.parent.mkdir(exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.user_config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"配置保存失败: {e}")
    
    def add_platform_config(self, platform_id: str, config: dict):
        """添加平台配置"""
        self.user_config['active_platforms'][platform_id] = {
            'api_key': config['api_key'],
            'default_model': config.get('default_model'),
            'custom_params': config.get('custom_params', {}),
            'status': 'unconfigured',
            'created_at': datetime.now().isoformat(),
            'last_test': None,
            'error_message': None
        }
        self.save_user_config()
    
    def remove_platform_config(self, platform_id: str):
        """移除平台配置"""
        if platform_id in self.user_config['active_platforms']:
            del self.user_config['active_platforms'][platform_id]
            self.save_user_config()
    
    def update_platform_status(self, platform_id: str, status: str, error_msg: str = None):
        """更新平台状态"""
        if platform_id in self.user_config['active_platforms']:
            self.user_config['active_platforms'][platform_id]['status'] = status
            self.user_config['active_platforms'][platform_id]['last_test'] = datetime.now().isoformat()
            if error_msg:
                self.user_config['active_platforms'][platform_id]['error_message'] = error_msg
            else:
                self.user_config['active_platforms'][platform_id]['error_message'] = None
            self.save_user_config()
    
    def get_configured_platforms(self) -> List[Dict]:
        """获取已配置的平台列表"""
        from config.ai_platforms import PLATFORM_UI_CONFIG
        
        result = []
        for platform_id, config in self.user_config['active_platforms'].items():
            ui_config = PLATFORM_UI_CONFIG.get(platform_id, {})
            result.append({
                'platform_id': platform_id,
                'config': config,
                'ui_config': ui_config
            })
        return result
    
    def get_platform_config(self, platform_id: str) -> Optional[Dict]:
        """获取特定平台的配置"""
        return self.user_config['active_platforms'].get(platform_id)
    
    def update_global_params(self, params: dict):
        """更新全局参数"""
        self.user_config['global_params'].update(params)
        self.save_user_config()
    
    def get_global_params(self) -> dict:
        """获取全局参数"""
        return self.user_config['global_params'].copy()
    
    def is_platform_configured(self, platform_id: str) -> bool:
        """检查平台是否已配置"""
        return platform_id in self.user_config['active_platforms']
    
    def get_configured_platform_ids(self) -> List[str]:
        """获取已配置的平台ID列表"""
        return list(self.user_config['active_platforms'].keys())


# 全局配置管理器实例
ai_config_manager = AIConfigManager() 