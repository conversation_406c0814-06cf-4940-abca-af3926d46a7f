# Prompt Assistant 提示词助手 - MVC架构重构

## 项目概述

Prompt Assistant（提示词助手）是一个用于管理和收藏AI提示词的桌面应用程序。
本项目正在进行架构重构，从单体架构转向MVC（模型-视图-控制器）架构，以提高代码的可维护性和可扩展性。

## 重构目标

1. **分离关注点**：将业务逻辑、UI和数据访问分开
2. **减少耦合**：降低组件间的相互依赖
3. **提高可测试性**：便于编写单元测试
4. **改进错误处理**：统一错误处理机制
5. **消除代码重复**：通过抽象共同功能减少重复代码
6. **统一配置管理**：将硬编码的配置集中管理

## 新架构设计

### 目录结构

```
prompt_assistant/
├── app.py                # 应用程序入口点
├── main.py               # 原始程序（暂时保留）
├── model.py              # 数据模型（暂时保留）
├── config/               # 配置文件
│   ├── __init__.py
│   ├── app_config.py     # 应用程序配置
│   └── style_config.py   # 样式配置
├── controllers/          # 控制器
│   ├── __init__.py
│   ├── app_controller.py # 应用程序控制器
│   └── filter_controller.py # 筛选控制器
├── services/             # 服务层
│   ├── __init__.py
│   ├── prompt_service.py # 提示词服务
│   ├── tag_service.py    # 标签服务
│   └── category_service.py # 分类服务
├── utils/                # 工具类
│   ├── __init__.py
│   ├── error_handler.py  # 错误处理
│   ├── event_bus.py      # 事件总线
│   └── ui_helpers.py     # UI辅助函数
├── views/                # 视图组件
│   ├── __init__.py
│   ├── base_components.py # 基础组件
│   └── main_window.py    # 主窗口（待实现）
└── tests/                # 测试
    ├── __init__.py
    └── test_prompt_service.py # 提示词服务测试
```

### 架构概述

- **模型层（Model）**：`model.py`和`services/`目录
  - 处理数据访问和业务逻辑
  - 包括提示词、标签、分类等服务

- **视图层（View）**：`views/`目录
  - 处理UI展示和用户交互
  - 基础UI组件和专用视图组件

- **控制器层（Controller）**：`controllers/`目录
  - 协调模型和视图
  - 处理用户输入和业务流程

- **实用工具**：`utils/`目录
  - 提供错误处理
  - 实现事件总线
  - 提供UI辅助功能

- **配置管理**：`config/`目录
  - 集中管理应用程序配置
  - 统一管理UI样式

## 重构进度

### 已完成
- [x] 创建基础目录结构
- [x] 创建配置文件系统
  - 实现app_config.py配置应用程序信息
  - 实现style_config.py统一管理样式
- [x] 实现工具类
  - 错误处理机制（error_handler.py）
  - 事件总线（event_bus.py）
  - UI辅助函数（ui_helpers.py）
- [x] 创建服务层
  - PromptService处理提示词业务逻辑
  - TagService处理标签业务逻辑
  - CategoryService处理分类业务逻辑
- [x] 实现控制器层
  - AppController协调应用程序状态
  - FilterController处理筛选逻辑
- [x] 实现基础UI组件
  - 创建BaseComponent等基础组件类
- [x] 创建应用程序入口（app.py）
  - 支持原始版本和MVC版本启动

### 进行中
- [ ] 重构视图层
  - 创建PromptCard组件
  - 创建筛选组件
  - 创建MainWindow

### 待完成
- [ ] 完成视图层重构
- [ ] 修复单元测试
- [ ] 编写更多单元测试
- [ ] 完全迁移到MVC架构
- [ ] 整合新旧代码，保证功能一致性
- [ ] 性能优化和代码清理

## 运行方式

目前重构处于过渡阶段，可以通过以下命令启动应用程序：

```bash
# 运行原始版本
python app.py

# 运行MVC版本（如果已实现）
python app.py --mvc
```

## 已解决的问题

1. **控制逻辑分散**：
   - 使用服务层和控制器层分离业务逻辑
   - 使用事件总线实现组件间通信

2. **模型与视图耦合**：
   - 通过控制器实现模型和视图的解耦
   - 使用服务层封装数据访问逻辑

3. **错误处理机制不完善**：
   - 实现统一的错误处理服务
   - 自定义异常类型
   - 日志记录机制

4. **代码重复**：
   - 创建基础组件类减少UI代码重复
   - 使用工厂方法创建常用组件

5. **配置管理分散**：
   - 集中管理应用程序配置
   - 将UI样式提取到配置文件

## 下一步计划

1. 完成视图层重构，创建符合MVC架构的界面组件
2. 修复单元测试，确保服务层功能正确性
3. 实现视图层与控制器的连接，使MVC模式完全运作
4. 编写更完整的测试覆盖整个应用程序
5. 清理冗余代码，完成全面迁移

## 注意事项

在重构过程中，我们会保持原有功能不变，确保用户体验的连续性。重构的主要目标是改进代码架构，而非修改功能。 