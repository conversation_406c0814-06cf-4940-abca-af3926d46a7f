#!/usr/bin/env python3
"""
直接测试事件连接
"""

from PySide6.QtWidgets import QApplication, QPushButton
import sys
from components.button_factory import ButtonFactory

# 创建QApplication实例
app = QApplication.instance() or QApplication(sys.argv)

print("🔍 测试ButtonFactory按钮的事件连接...")

# 创建一个测试按钮
normal_svg = '<svg>test</svg>'
active_svg = '<svg>test_active</svg>'

btn = ButtonFactory.create_favorite_button(normal_svg, active_svg, "test", False)
print(f"✅ 按钮创建完成，初始状态: {btn.is_favorited}")

# 测试原始点击
print("1️⃣ 测试原始ButtonFactory点击行为:")
print(f"   点击前: {btn.is_favorited}")
btn.clicked.emit()
print(f"   点击后: {btn.is_favorited}")

# 重置状态
btn.is_favorited = False

# 断开并重新连接
print("2️⃣ 断开原始连接并重连自定义处理器:")

# 查看所有连接
import weakref
print(f"   断开前连接数: {len(btn.clicked._slots) if hasattr(btn.clicked, '_slots') else '无法获取'}")

btn.clicked.disconnect()
print(f"   断开后连接数: {len(btn.clicked._slots) if hasattr(btn.clicked, '_slots') else '无法获取'}")

# 连接自定义处理器
def custom_handler():
    print("   🎯 自定义处理器被调用！")
    btn.is_favorited = not btn.is_favorited
    print(f"   状态已切换为: {btn.is_favorited}")

btn.clicked.connect(custom_handler)
print(f"   重连后连接数: {len(btn.clicked._slots) if hasattr(btn.clicked, '_slots') else '无法获取'}")

# 测试自定义点击
print("3️⃣ 测试自定义处理器:")
print(f"   点击前: {btn.is_favorited}")
btn.clicked.emit()
print(f"   点击后: {btn.is_favorited}")

print("🏁 测试完成") 