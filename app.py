#!/usr/bin/env python3
"""
提示词助手应用程序入口文件
重构版本，采用MVC架构设计
"""
import sys
import os
import traceback
import argparse
from pathlib import Path

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QCoreApplication, QLocale, QTranslator
from PySide6.QtGui import QIcon, QFontDatabase

# 导入配置
from config.app_config import (
    APP_NAME, APP_VERSION, WINDOW_WIDTH, WINDOW_HEIGHT, 
    WINDOW_TITLE, DB_PATH, DB_TYPE
)

# 导入错误处理模块
from utils.error_handler import setup_exception_handling

def setup_qt_environment():
    """设置Qt环境"""
    # 高分辨率屏幕支持
    QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 设置应用程序信息
    QCoreApplication.setApplicationName(APP_NAME)
    QCoreApplication.setApplicationVersion(APP_VERSION)
    QCoreApplication.setOrganizationName("PromptAssistant")
    QCoreApplication.setOrganizationDomain("promptassistant.example.com")
    
    # 加载翻译
    translator = QTranslator()
    if translator.load(QLocale.system(), "translations", "_", 
                     os.path.join(os.path.dirname(__file__), "translations")):
        QApplication.installTranslator(translator)
    
    # 加载字体
    # QFontDatabase.addApplicationFont("fonts/your_font.ttf")


def run_legacy_app():
    """运行原始版本的应用程序"""
    try:
        # 导入现有的应用程序模块
        from main import PromptAssistantRedesigned
        
        # 创建并显示窗口
        print(f"启动{APP_NAME} v{APP_VERSION} (原始版本)")
        window = PromptAssistantRedesigned()
        window.show()
        
        return window
    except Exception as e:
        print(f"原始版本启动失败: {e}")
        traceback.print_exc()
        return None


def run_mvc_app():
    """运行MVC版本的应用程序"""
    try:
        # 导入MVC组件
        from model import PromptModel
        from controllers.app_controller import AppController
        from controllers.filter_controller import FilterController
        from services.prompt_service import PromptService
        from services.tag_service import TagService
        from services.category_service import CategoryService
        
        # 临时使用原始窗口，将来会替换为专门的MainWindow视图
        from main import PromptAssistantRedesigned
        
        # 创建模型和服务
        model = PromptModel(DB_TYPE, DB_PATH)
        prompt_service = PromptService(model)
        tag_service = TagService(model)
        category_service = CategoryService(model)
        
        # 创建控制器
        app_controller = AppController(model)
        filter_controller = FilterController(tag_service, category_service)
        
        # 创建主窗口并初始化
        print(f"启动{APP_NAME} v{APP_VERSION} (MVC版本)")
        window = PromptAssistantRedesigned()
        
        # 未来将使用专门的MainWindow
        # window = MainWindow(app_controller, filter_controller)
        
        window.show()
        
        # 加载初始数据
        app_controller.load_initial_data()
        filter_controller.load_filter_data()
        
        return window
    except Exception as e:
        print(f"MVC版本启动失败: {e}")
        traceback.print_exc()
        return None


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description=f'{APP_NAME} - {APP_VERSION}')
    parser.add_argument('--mvc', action='store_true', help='使用MVC架构版本')
    parser.add_argument('--legacy', action='store_true', help='使用原始版本')
    
    return parser.parse_args()


def main():
    """应用程序入口点"""
    # 设置异常处理
    setup_exception_handling()
    
    # 解析命令行参数
    args = parse_arguments()
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置Qt环境
    setup_qt_environment()
    
    # 默认使用原始版本，除非指定使用MVC版本
    use_mvc = args.mvc and not args.legacy
    
    # 启动应用程序
    try:
        if use_mvc:
            window = run_mvc_app()
        else:
            window = run_legacy_app()
            
        # 检查是否成功创建窗口
        if window is None:
            print("无法创建应用程序窗口，尝试使用备选方案...")
            window = run_legacy_app() if use_mvc else run_mvc_app()
            
            if window is None:
                print("所有尝试均失败，无法启动应用程序。")
                return 1
        
        return app.exec()
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main()) 