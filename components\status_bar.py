#!/usr/bin/env python3
"""
状态栏组件
从main.py提取的StatusBar类
"""
from PySide6.QtWidgets import QWidget, QHBoxLayout, QLabel
from PySide6.QtCore import Qt, QTimer


class StatusBar(QWidget):
    """底部状态栏组件 (400x32) - 提取自main.py"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(400, 32)  # 调整高度从50px到32px，更符合移动端设计规范
        self.default_status = "就绪"
        self.status_timer = QTimer()
        self.status_timer.setSingleShot(True)
        self.status_timer.timeout.connect(self.restore_default_status)
        self.init_ui()
        
    def init_ui(self):
        self.setStyleSheet("""
            QWidget {
                background-color: #F3F4F6;
                border-top: 1px solid #E5E7EB;
            }
        """)
        
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 0, 12, 0)
        layout.setAlignment(Qt.AlignVCenter)  # 设置整个布局垂直居中
        
        # 状态信息标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #6B7280;
                font-size: 11px;
                background: transparent;
                border: none;
            }
        """)
        self.status_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # 右侧信息
        self.info_label = QLabel("")
        self.info_label.setStyleSheet("""
            QLabel {
                color: #9CA3AF;
                font-size: 10px;
                background: transparent;
                border: none;
            }
        """)
        self.info_label.setAlignment(Qt.AlignVCenter)  # 文本垂直居中
        layout.addWidget(self.info_label)
        
    def set_status(self, message, timeout=None):
        """设置状态信息"""
        # 停止当前的定时器
        if self.status_timer.isActive():
            self.status_timer.stop()
        
        # 设置状态文本
        self.status_label.setText(message)
        
        # 如果提供了超时时间，启动定时器
        if timeout is not None:
            self.status_timer.start(timeout)
        
    def set_info(self, info):
        """设置右侧信息"""
        self.info_label.setText(info)
    
    def restore_default_status(self):
        """恢复默认状态"""
        self.status_timer.stop()
        self.status_label.setText(self.default_status) 