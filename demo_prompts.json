[{"title": "AI绘画提示词", "content": "一个美丽的风景画，包含山脉、湖泊和日落，使用油画风格", "tags": ["风景", "油画", "日落"], "category": "AI绘画", "media_files": [], "type": "图片", "auxiliary_fields": {}, "is_favorite": 0, "is_pinned": 0, "is_deleted": 0, "deleted_at": null, "created_at": "2025-08-01T11:16:21.796685", "updated_at": "2025-08-01T11:16:21.796713"}, {"title": "代码优化建议", "content": "请帮我优化这段Python代码的性能，重点关注循环和数据结构的使用", "tags": ["Python", "性能优化", "代码"], "category": "编程", "media_files": [], "type": "文本", "auxiliary_fields": {}, "is_favorite": 0, "is_pinned": 0, "is_deleted": 0, "deleted_at": null, "created_at": "2025-08-01T11:16:21.796719", "updated_at": "2025-08-01T11:16:21.796720"}, {"title": "商业计划书", "content": "为一家新的科技创业公司撰写商业计划书，包括市场分析、财务预测和营销策略", "tags": ["创业", "商业计划", "营销"], "category": "商业", "media_files": [], "type": "文本", "auxiliary_fields": {}, "is_favorite": 0, "is_pinned": 0, "is_deleted": 0, "deleted_at": null, "created_at": "2025-08-01T11:16:21.796724", "updated_at": "2025-08-01T11:16:21.796725"}, {"title": "健康饮食建议", "content": "为一位想要减肥的上班族制定一周的健康饮食计划，包含营养搭配和食谱", "tags": ["减肥", "营养", "食谱"], "category": "健康", "media_files": [], "type": "文本", "auxiliary_fields": {}, "is_favorite": 0, "is_pinned": 0, "is_deleted": 0, "deleted_at": null, "created_at": "2025-08-01T11:16:21.796727", "updated_at": "2025-08-01T11:16:21.796728"}, {"title": "旅行攻略", "content": "为日本东京5日游制定详细的旅行攻略，包括景点推荐、交通安排和美食指南", "tags": ["日本", "东京", "攻略"], "category": "旅行", "media_files": [], "type": "文本", "auxiliary_fields": {}, "is_favorite": 0, "is_pinned": 0, "is_deleted": 0, "deleted_at": null, "created_at": "2025-08-01T11:16:21.796731", "updated_at": "2025-08-01T11:16:21.796732"}, {"title": "学习计划制定", "content": "为准备考研的学生制定3个月的复习计划，包括各科目的时间分配和学习方法", "tags": ["考研", "学习计划", "复习"], "category": "教育", "media_files": [], "type": "文本", "auxiliary_fields": {}, "is_favorite": 0, "is_pinned": 0, "is_deleted": 0, "deleted_at": null, "created_at": "2025-08-01T11:16:21.796734", "updated_at": "2025-08-01T11:16:21.796735"}]